from typing import Dict
from pathlib import Path
import yaml

class ConfigValidator:
    """Validates and provides safe access to configuration"""
    
    DEFAULT_CONFIG = {
        'logging': {
            'log_dir': 'logs',
            'file': 'logs/scalper.log',  # Add explicit log file path
            'level': 'INFO',
            'max_size': 100,
            'backup_count': 5,
            'heartbeat_interval': 30,
            'debug_logs': {
                'prompts': 'logs/llm_prompts.log',
                'responses': 'logs/llm_responses.log',
                'rejected_signals': 'logs/rejected_signals.jsonl',
                'heartbeat': 'logs/heartbeat.log'
            }
        },
        'llm_model': 'lmstudio-community/meta-llama-3-8b-instruct',
        'prompt_confidence_threshold': 0.65,
        'risk_limits': {
            'max_drawdown_pct': 10,
            'min_liquidation_buffer_pct': 3,
            'min_win_rate': 0.35
        },
        'debug_mode': False,
        'live_mode': False,
        'performance_window': 10,
        'min_spread_pct': 0.002,
        'min_sl_distance_pct': 0.003,
        'position_size_pct': 0.05,
        'leverage': 20,
        'cooldown_seconds': 60
    }

    @staticmethod
    def load_and_validate(config_path: str) -> Dict:
        """Load config file and merge with defaults"""
        try:
            with open(config_path, 'r') as f:
                user_config = yaml.safe_load(f)
        except FileNotFoundError:
            print(f"Config file not found: {config_path}, using defaults")
            user_config = {}
        except yaml.YAMLError as e:
            print(f"Error parsing config file: {e}, using defaults")
            user_config = {}

        # Deep merge user config with defaults
        config = ConfigValidator._deep_merge(ConfigValidator.DEFAULT_CONFIG.copy(), user_config)
        
        # Create base log directory
        log_dir = Path(config['logging']['log_dir'])
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Ensure all log file directories exist
        log_file = Path(config['logging']['file'])
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Create debug log directories
        for log_path in config['logging']['debug_logs'].values():
            Path(log_path).parent.mkdir(parents=True, exist_ok=True)
            
        return config

    @staticmethod
    def _deep_merge(base: Dict, update: Dict) -> Dict:
        """Deep merge two dictionaries"""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                ConfigValidator._deep_merge(base[key], value)
            else:
                base[key] = value
        return base
