# Simple Momentum Strategy for Epinnox v7
# This strategy enters long positions when price is above moving averages
# and RSI indicates momentum, with volume confirmation

name: "Simple Momentum Strategy"
description: "Basic momentum strategy using RSI, moving averages, and volume"
version: "1.0"
author: "Epinnox v7"

# Risk management parameters
risk_management:
  max_position_size_pct: 2.0  # Maximum 2% of account per trade
  stop_loss_pct: 0.5          # 0.5% stop loss
  take_profit_pct: 1.0        # 1.0% take profit (2:1 RR)
  max_drawdown_pct: 5.0       # Stop trading if drawdown exceeds 5%

# Trading rules
rules:
  # Long entry conditions (all must be met)
  long_entry:
    - type: "indicator_above"
      indicator: "rsi"
      timeframe: "1m"
      value: 55
      description: "RSI above 55 (momentum)"
    
    - type: "price_above"
      indicator: "ema_20"
      timeframe: "1m"
      description: "Price above 20 EMA"
    
    - type: "indicator_above"
      indicator: "ema_20"
      timeframe: "1m"
      value: "sma_50"  # EMA 20 above SMA 50
      description: "Short MA above long MA"
    
    - type: "volume_surge"
      value: 120
      description: "Volume 20% above average"

  # Short entry conditions (all must be met)
  short_entry:
    - type: "indicator_below"
      indicator: "rsi"
      timeframe: "1m"
      value: 45
      description: "RSI below 45 (bearish momentum)"
    
    - type: "price_below"
      indicator: "ema_20"
      timeframe: "1m"
      description: "Price below 20 EMA"
    
    - type: "indicator_below"
      indicator: "ema_20"
      timeframe: "1m"
      value: "sma_50"  # EMA 20 below SMA 50
      description: "Short MA below long MA"
    
    - type: "volume_surge"
      value: 120
      description: "Volume 20% above average"

  # Exit conditions (any can trigger exit)
  exit:
    - type: "indicator_above"
      indicator: "rsi"
      timeframe: "1m"
      value: 70
      description: "RSI overbought (long exit)"
    
    - type: "indicator_below"
      indicator: "rsi"
      timeframe: "1m"
      value: 30
      description: "RSI oversold (short exit)"
    
    - type: "position_pnl_above"
      value: 0.8
      description: "Take profit at 0.8%"
    
    - type: "position_pnl_below"
      value: -0.4
      description: "Stop loss at -0.4%"

# Time-based filters
time_filters:
  # Only trade during active market hours (UTC)
  active_hours:
    start_hour: 8   # 8 AM UTC
    end_hour: 22    # 10 PM UTC
  
  # Avoid trading during low liquidity periods
  avoid_hours:
    - 0   # Midnight UTC
    - 1   # 1 AM UTC
    - 23  # 11 PM UTC

# Market condition filters
market_filters:
  min_volatility: 0.001      # Minimum 0.1% volatility
  max_volatility: 0.05       # Maximum 5% volatility
  min_volume_ratio: 0.8      # Minimum 80% of average volume
  max_spread_pct: 0.1        # Maximum 0.1% spread

# Performance targets
targets:
  min_win_rate: 45           # Minimum 45% win rate
  min_profit_factor: 1.2     # Minimum 1.2 profit factor
  max_drawdown: 3.0          # Maximum 3% drawdown
  min_sharpe_ratio: 0.5      # Minimum 0.5 Sharpe ratio
