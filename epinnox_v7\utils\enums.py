from enum import Enum, auto

class ActionType(Enum):
    LONG = "long"
    SHORT = "short"
    HOLD = "hold"
    CLOSE = "close"

class RiskLevel(Enum):
    LOW = auto()
    MEDIUM = auto()
    HIGH = auto()
    EXTREME = auto()

class MarketRegime(Enum):
    TRENDING = "trending"
    RANGING = "ranging"
    VOLATILE = "volatile"
    UNKNOWN = "unknown"

class PositionSide(Enum):
    LONG = "long"
    SHORT = "short"
    NONE = "none"

class SignalConfidence(Enum):
    LOW = "low"          # < 0.4
    MODERATE = "moderate"  # 0.4 - 0.65
    HIGH = "high"        # 0.65 - 0.85
    VERY_HIGH = "very_high" # > 0.85
