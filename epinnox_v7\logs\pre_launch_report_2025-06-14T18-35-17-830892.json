{"overall_status": "failed", "ready_for_production": false, "duration_seconds": 2.406491, "timestamp": "2025-06-14T18:35:17.830892", "summary": {"total_checks": 32, "passed": 27, "failed": 5, "warnings": 0, "critical_failures": 4, "non_critical_warnings": -3}, "critical_failures": [{"name": "Exchange Connectivity", "message": "Failed to connect to exchanges", "category": "exchanges"}, {"name": "Exchange Authentication", "message": "Exchange authentication failed", "category": "exchanges"}, {"name": "Account <PERSON><PERSON>", "message": "Cannot check balance - no connection", "category": "exchanges"}, {"name": "Trading Permissions", "message": "Cannot verify permissions", "category": "exchanges"}], "warnings": [{"name": "Rate Limits", "message": "Cannot check rate limits", "category": "exchanges", "critical": false}], "categories": {"configuration": [{"id": "config_env", "name": "Environment Configuration", "status": "passed", "message": "Environment set to production", "critical": true, "timestamp": "2025-06-14T18:35:15.424401"}, {"id": "config_dry_run", "name": "Dry Run Mode", "status": "passed", "message": "Dry run mode disabled", "critical": true, "timestamp": "2025-06-14T18:35:15.424401"}, {"id": "config_debug", "name": "Debug Mode", "status": "passed", "message": "Debug mode disabled", "critical": false, "timestamp": "2025-06-14T18:35:15.424401"}, {"id": "config_symbols", "name": "Trading Symbols", "status": "passed", "message": "Trading symbols configured: ['BTC/USDT', 'ETH/USDT']", "critical": true, "timestamp": "2025-06-14T18:35:15.424401"}, {"id": "config_risk", "name": "Risk Parameters", "status": "passed", "message": "All required risk parameters configured", "critical": true, "timestamp": "2025-06-14T18:35:15.424401"}], "security": [{"id": "security_api_keys", "name": "API Key Security", "status": "passed", "message": "API keys properly secured", "critical": true, "timestamp": "2025-06-14T18:35:15.462319"}, {"id": "security_permissions", "name": "API Permissions", "status": "passed", "message": "API permissions check passed", "critical": true, "timestamp": "2025-06-14T18:35:15.690790"}, {"id": "security_auth", "name": "Authentication System", "status": "passed", "message": "Authentication system available", "critical": true, "timestamp": "2025-06-14T18:35:15.690790"}, {"id": "security_encryption", "name": "Data Encryption", "status": "passed", "message": "Data encryption check passed", "critical": true, "timestamp": "2025-06-14T18:35:15.690790"}], "exchanges": [{"id": "exchange_connectivity", "name": "Exchange Connectivity", "status": "failed", "message": "Failed to connect to exchanges", "critical": true, "timestamp": "2025-06-14T18:35:17.827386"}, {"id": "exchange_auth", "name": "Exchange Authentication", "status": "failed", "message": "Exchange authentication failed", "critical": true, "timestamp": "2025-06-14T18:35:17.828387"}, {"id": "exchange_balance", "name": "Account <PERSON><PERSON>", "status": "failed", "message": "Cannot check balance - no connection", "critical": true, "timestamp": "2025-06-14T18:35:17.828387"}, {"id": "exchange_permissions", "name": "Trading Permissions", "status": "failed", "message": "Cannot verify permissions", "critical": true, "timestamp": "2025-06-14T18:35:17.828387"}, {"id": "exchange_rate_limits", "name": "Rate Limits", "status": "failed", "message": "Cannot check rate limits", "critical": false, "timestamp": "2025-06-14T18:35:17.828387"}], "risk_management": [{"id": "risk_limits", "name": "Risk Limits", "status": "passed", "message": "Daily loss limit: $500", "critical": true, "timestamp": "2025-06-14T18:35:17.828387"}, {"id": "risk_circuit_breakers", "name": "Circuit Breakers", "status": "passed", "message": "Circuit breakers enabled", "critical": true, "timestamp": "2025-06-14T18:35:17.828387"}, {"id": "risk_position_sizing", "name": "Position Sizing", "status": "passed", "message": "Position size: 1.0%", "critical": true, "timestamp": "2025-06-14T18:35:17.828387"}, {"id": "risk_stop_loss", "name": "Stop Loss Configuration", "status": "passed", "message": "Stop loss: 1.5%", "critical": true, "timestamp": "2025-06-14T18:35:17.828387"}], "llm_engine": [{"id": "llm_connectivity", "name": "LLM Connectivity", "status": "passed", "message": "LLM engine enabled", "critical": true, "timestamp": "2025-06-14T18:35:17.828387"}, {"id": "llm_model", "name": "LLM Model", "status": "passed", "message": "LLM model: Phi-3.1-mini-128k-instruct.Q4_K_M.gguf", "critical": true, "timestamp": "2025-06-14T18:35:17.828387"}, {"id": "llm_confidence", "name": "Confidence Thresholds", "status": "passed", "message": "Confidence threshold: 0.7", "critical": true, "timestamp": "2025-06-14T18:35:17.828387"}, {"id": "llm_decision_logic", "name": "Decision Logic", "status": "passed", "message": "LLM decision logic configured", "critical": true, "timestamp": "2025-06-14T18:35:17.828387"}], "monitoring": [{"id": "monitoring_logging", "name": "Logging System", "status": "passed", "message": "File logging enabled", "critical": true, "timestamp": "2025-06-14T18:35:17.829387"}, {"id": "monitoring_alerts", "name": "Alert System", "status": "passed", "message": "Alert system enabled", "critical": false, "timestamp": "2025-06-14T18:35:17.829387"}, {"id": "monitoring_metrics", "name": "Performance Metrics", "status": "passed", "message": "Metrics collection enabled", "critical": false, "timestamp": "2025-06-14T18:35:17.829387"}, {"id": "monitoring_backup", "name": "Backup System", "status": "passed", "message": "Backup system enabled", "critical": true, "timestamp": "2025-06-14T18:35:17.829387"}], "emergency_systems": [{"id": "emergency_stop", "name": "Emergency Stop", "status": "passed", "message": "Emergency stop functionality available", "critical": true, "timestamp": "2025-06-14T18:35:17.829387"}, {"id": "emergency_procedures", "name": "Emergency Procedures", "status": "passed", "message": "Emergency procedures configured", "critical": true, "timestamp": "2025-06-14T18:35:17.829387"}, {"id": "emergency_contacts", "name": "Emergency Contacts", "status": "passed", "message": "Emergency contacts configured", "critical": false, "timestamp": "2025-06-14T18:35:17.829387"}], "compliance": [{"id": "compliance_logging", "name": "Compliance Logging", "status": "passed", "message": "Trade reporting enabled", "critical": true, "timestamp": "2025-06-14T18:35:17.829387"}, {"id": "compliance_reporting", "name": "Trade Reporting", "status": "passed", "message": "Transaction reporting enabled", "critical": false, "timestamp": "2025-06-14T18:35:17.829387"}, {"id": "compliance_audit", "name": "Audit Trail", "status": "passed", "message": "Audit trail enabled", "critical": true, "timestamp": "2025-06-14T18:35:17.829387"}]}, "recommendations": ["❌ CRITICAL: Fix all critical failures before proceeding to live trading"], "next_steps": ["1. Fix all critical failures listed above", "2. Re-run the pre-launch checklist", "3. Only proceed when all critical checks pass"]}