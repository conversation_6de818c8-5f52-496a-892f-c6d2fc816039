"""
Strategy Backtester for Epinnox v7

Comprehensive backtesting system that simulates trading over historical OHLCV data
using the full LLM-driven pipeline with rule-based strategy support.
"""
import json
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict

from core.strategy_state import StrategyState
from core.risk_guard import RiskGuard
from core.llm_prompt_builder import LLMPromptBuilder
from core.llm_response_parser import LLMResponseParser
from llm.performance_feedback import PerformanceFeedback
from utils.logger import get_logger
from utils.enums import ActionType, PositionSide

@dataclass
class BacktestTrade:
    """Represents a single trade in the backtest"""
    timestamp: float
    symbol: str
    action: str
    entry_price: float
    exit_price: Optional[float] = None
    position_size: float = 0.0
    pnl: float = 0.0
    pnl_pct: float = 0.0
    confidence: float = 0.0
    reason: str = ""
    duration: float = 0.0  # in seconds
    max_adverse_excursion: float = 0.0
    max_favorable_excursion: float = 0.0

@dataclass
class BacktestResults:
    """Comprehensive backtest results"""
    # Basic metrics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0

    # PnL metrics
    total_pnl: float = 0.0
    total_pnl_pct: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0

    # Risk metrics
    max_drawdown: float = 0.0
    max_drawdown_pct: float = 0.0
    sharpe_ratio: float = 0.0
    calmar_ratio: float = 0.0

    # Time-based metrics
    avg_trade_duration: float = 0.0  # in minutes
    trades_per_day: float = 0.0

    # Strategy-specific
    signal_accuracy: float = 0.0
    signal_frequency: Dict[str, int] = None
    time_of_day_performance: Dict[str, float] = None

    # Equity curve data
    equity_curve: List[Tuple[float, float]] = None  # (timestamp, equity)
    trades: List[BacktestTrade] = None

class RuleEngine:
    """Evaluates YAML-based strategy rules"""

    def __init__(self, strategy_config: Dict):
        self.strategy = strategy_config
        self.logger = get_logger()

    def evaluate_conditions(self, market_data: Dict, indicators: Dict, position: Dict) -> Dict:
        """
        Evaluate strategy conditions and return trading decision

        Returns:
            Dict with action, confidence, and reasoning
        """
        try:
            rules = self.strategy.get('rules', {})

            # Evaluate entry conditions
            long_conditions = self._evaluate_condition_group(
                rules.get('long_entry', []), market_data, indicators, position
            )
            short_conditions = self._evaluate_condition_group(
                rules.get('short_entry', []), market_data, indicators, position
            )
            exit_conditions = self._evaluate_condition_group(
                rules.get('exit', []), market_data, indicators, position
            )

            # Determine action based on conditions
            if exit_conditions['met'] and position.get('size', 0) != 0:
                return {
                    'action': ActionType.CLOSE.value,
                    'confidence': exit_conditions['confidence'],
                    'reason': f"Exit conditions met: {exit_conditions['reasons']}"
                }
            elif long_conditions['met'] and position.get('side') != PositionSide.LONG.value:
                return {
                    'action': ActionType.LONG.value,
                    'confidence': long_conditions['confidence'],
                    'reason': f"Long entry: {long_conditions['reasons']}"
                }
            elif short_conditions['met'] and position.get('side') != PositionSide.SHORT.value:
                return {
                    'action': ActionType.SHORT.value,
                    'confidence': short_conditions['confidence'],
                    'reason': f"Short entry: {short_conditions['reasons']}"
                }
            else:
                return {
                    'action': ActionType.HOLD.value,
                    'confidence': 0.5,
                    'reason': "No conditions met"
                }

        except Exception as e:
            self.logger.error(f"Error evaluating strategy rules: {str(e)}")
            return {
                'action': ActionType.HOLD.value,
                'confidence': 0.0,
                'reason': f"Rule evaluation error: {str(e)}"
            }

    def _evaluate_condition_group(self, conditions: List[Dict], market_data: Dict,
                                 indicators: Dict, position: Dict) -> Dict:
        """Evaluate a group of conditions (AND logic)"""
        if not conditions:
            return {'met': False, 'confidence': 0.0, 'reasons': []}

        met_conditions = []
        confidence_scores = []
        reasons = []

        for condition in conditions:
            result = self._evaluate_single_condition(condition, market_data, indicators, position)
            if result['met']:
                met_conditions.append(True)
                confidence_scores.append(result['confidence'])
                reasons.append(result['reason'])
            else:
                met_conditions.append(False)

        # All conditions must be met (AND logic)
        all_met = all(met_conditions)
        avg_confidence = np.mean(confidence_scores) if confidence_scores else 0.0

        return {
            'met': all_met,
            'confidence': avg_confidence,
            'reasons': reasons
        }

    def _evaluate_single_condition(self, condition: Dict, market_data: Dict,
                                  indicators: Dict, position: Dict) -> Dict:
        """Evaluate a single condition"""
        try:
            condition_type = condition.get('type')

            if condition_type == 'price_above':
                value = market_data.get('price', 0)
                threshold = condition.get('value', 0)
                met = value > threshold
                confidence = min(1.0, (value - threshold) / threshold * 10) if met else 0.0

            elif condition_type == 'price_below':
                value = market_data.get('price', 0)
                threshold = condition.get('value', 0)
                met = value < threshold
                confidence = min(1.0, (threshold - value) / threshold * 10) if met else 0.0

            elif condition_type == 'indicator_above':
                indicator_name = condition.get('indicator')
                timeframe = condition.get('timeframe', '1m')
                value = indicators.get(timeframe, {}).get(indicator_name, 0)
                threshold = condition.get('value', 0)
                met = value > threshold
                confidence = min(1.0, abs(value - threshold) / threshold) if met else 0.0

            elif condition_type == 'indicator_below':
                indicator_name = condition.get('indicator')
                timeframe = condition.get('timeframe', '1m')
                value = indicators.get(timeframe, {}).get(indicator_name, 0)
                threshold = condition.get('value', 0)
                met = value < threshold
                confidence = min(1.0, abs(threshold - value) / threshold) if met else 0.0

            elif condition_type == 'volume_surge':
                volume_surge = market_data.get('volume_surge', 100)
                threshold = condition.get('value', 150)
                met = volume_surge > threshold
                confidence = min(1.0, (volume_surge - threshold) / threshold) if met else 0.0

            else:
                self.logger.warning(f"Unknown condition type: {condition_type}")
                return {'met': False, 'confidence': 0.0, 'reason': f"Unknown condition: {condition_type}"}

            return {
                'met': met,
                'confidence': confidence,
                'reason': f"{condition_type}: {value:.4f} vs {threshold:.4f}" if 'value' in locals() else condition_type
            }

        except Exception as e:
            self.logger.error(f"Error evaluating condition {condition}: {str(e)}")
            return {'met': False, 'confidence': 0.0, 'reason': f"Condition error: {str(e)}"}

class StrategyBacktester:
    """
    Comprehensive backtesting system for Epinnox v7

    Features:
    - Historical OHLCV data simulation
    - LLM-driven or rule-based strategy execution
    - Comprehensive performance metrics
    - Signal heatmap generation
    - Equity curve tracking
    - Risk-adjusted returns
    """

    def __init__(self, config: Dict, ohlcv_data: pd.DataFrame, symbol: str,
                 strategy_config: Optional[Dict] = None):
        self.config = config.copy()
        self.ohlcv_data = ohlcv_data.copy()
        self.symbol = symbol
        self.strategy_config = strategy_config
        self.logger = get_logger()

        # Initialize components
        self.strategy_state = StrategyState(config)
        self.risk_guard = RiskGuard(config)
        self.performance_feedback = PerformanceFeedback(config)

        # Initialize rule engine if strategy provided
        self.rule_engine = RuleEngine(strategy_config) if strategy_config else None

        # Simulation state
        self.current_position = {
            'side': PositionSide.NONE.value,
            'size': 0.0,
            'entry_price': 0.0,
            'unrealized_pnl': 0.0,
            'leverage': 1.0
        }

        # Results tracking
        self.trades: List[BacktestTrade] = []
        self.equity_curve: List[Tuple[float, float]] = []
        self.signal_frequency: Dict[str, int] = {action.value: 0 for action in ActionType}
        self.time_of_day_signals: Dict[int, List[str]] = {}  # hour -> [actions]

        # Performance tracking
        self.initial_balance = config.get('initial_balance', 10000.0)
        self.current_balance = self.initial_balance
        self.peak_balance = self.initial_balance
        self.max_drawdown = 0.0

        # Ensure required columns exist
        self._validate_data()

    def _validate_data(self):
        """Validate OHLCV data has required columns"""
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in self.ohlcv_data.columns]

        if missing_columns:
            raise ValueError(f"Missing required columns in OHLCV data: {missing_columns}")

        # Convert timestamp to datetime if needed
        if not pd.api.types.is_datetime64_any_dtype(self.ohlcv_data['timestamp']):
            self.ohlcv_data['timestamp'] = pd.to_datetime(self.ohlcv_data['timestamp'])

        # Sort by timestamp
        self.ohlcv_data = self.ohlcv_data.sort_values('timestamp').reset_index(drop=True)

        self.logger.info(f"Validated OHLCV data: {len(self.ohlcv_data)} candles from "
                        f"{self.ohlcv_data['timestamp'].iloc[0]} to {self.ohlcv_data['timestamp'].iloc[-1]}")

    async def run(self, use_llm: bool = False) -> BacktestResults:
        """
        Run the backtest simulation

        Parameters:
            use_llm: If True, use LLM for decisions. If False, use rule engine.

        Returns:
            BacktestResults with comprehensive metrics
        """
        self.logger.info(f"Starting backtest for {self.symbol} over {len(self.ohlcv_data)} candles...")
        self.logger.info(f"Strategy mode: {'LLM-driven' if use_llm else 'Rule-based'}")

        # Initialize LLM components if needed
        if use_llm:
            await self._initialize_llm_components()

        # Process each candle
        for idx, row in self.ohlcv_data.iterrows():
            try:
                await self._process_candle(idx, row, use_llm)

                # Update equity curve
                current_equity = self._calculate_current_equity(row['close'])
                self.equity_curve.append((row['timestamp'].timestamp(), current_equity))

                # Update peak and drawdown
                if current_equity > self.peak_balance:
                    self.peak_balance = current_equity

                current_drawdown = (self.peak_balance - current_equity) / self.peak_balance
                self.max_drawdown = max(self.max_drawdown, current_drawdown)

            except Exception as e:
                self.logger.error(f"Error processing candle {idx}: {str(e)}")
                continue

        # Calculate final results
        results = self._calculate_results()

        self.logger.info("Backtest complete.")
        self.logger.info(f"Total trades: {results.total_trades}")
        self.logger.info(f"Win rate: {results.win_rate:.2f}%")
        self.logger.info(f"Total PnL: {results.total_pnl:.2f} USDT ({results.total_pnl_pct:.2f}%)")
        self.logger.info(f"Max drawdown: {results.max_drawdown_pct:.2f}%")
        self.logger.info(f"Sharpe ratio: {results.sharpe_ratio:.2f}")

        return results

    async def _initialize_llm_components(self):
        """Initialize LLM components for decision making"""
        try:
            from llm.model_runner import ModelRunner

            self.model_runner = ModelRunner(self.config)
            self.prompt_builder = LLMPromptBuilder()
            self.response_parser = LLMResponseParser(self.config)

            await self.model_runner.initialize()
            self.logger.info("LLM components initialized for backtesting")

        except Exception as e:
            self.logger.error(f"Failed to initialize LLM components: {str(e)}")
            raise

    async def _process_candle(self, idx: int, row: pd.Series, use_llm: bool):
        """Process a single candle and make trading decisions"""
        try:
            # Create market data from candle
            market_data = self._create_market_data(row)

            # Update strategy state
            self._update_strategy_state(market_data)

            # Get trading decision
            if use_llm:
                decision = await self._get_llm_decision(market_data)
            else:
                decision = self._get_rule_based_decision(market_data)

            if not decision:
                return

            # Track signal frequency
            action = decision.get('action', ActionType.HOLD.value)
            self.signal_frequency[action] += 1

            # Track time-of-day patterns
            hour = row['timestamp'].hour
            if hour not in self.time_of_day_signals:
                self.time_of_day_signals[hour] = []
            self.time_of_day_signals[hour].append(action)

            # Validate decision with risk guard
            if not self._validate_decision(decision, market_data):
                return

            # Execute trade simulation
            await self._simulate_trade(decision, market_data, row['timestamp'])

        except Exception as e:
            self.logger.error(f"Error processing candle {idx}: {str(e)}")

    def _update_strategy_state(self, market_data: Dict):
        """Update strategy state with current market data"""
        try:
            # Create price and volume data for regime analysis
            price_data = [market_data['price']]
            volume_data = [market_data['volume']]

            # Update market regime
            self.strategy_state.update_market_regime(price_data, volume_data)

            # Update risk level based on current performance
            current_drawdown = self.max_drawdown
            win_rate = 0.5  # Default neutral win rate for backtesting
            volatility = market_data.get('volatility', 0.01)

            self.strategy_state.update_risk_level(current_drawdown, win_rate, volatility)

        except Exception as e:
            self.logger.error(f"Error updating strategy state: {str(e)}")

    def _create_market_data(self, row: pd.Series) -> Dict:
        """Create market data dictionary from OHLCV row"""
        return {
            'symbol': self.symbol,
            'timestamp': row['timestamp'].timestamp(),
            'price': float(row['close']),
            'open': float(row['open']),
            'high': float(row['high']),
            'low': float(row['low']),
            'close': float(row['close']),
            'volume': float(row['volume']),
            'bid': float(row['close']) * 0.9995,  # Simulate bid/ask spread
            'ask': float(row['close']) * 1.0005,
            'spread_pct': 0.05,  # 0.05% spread simulation
            'volatility': self._calculate_candle_volatility(row),
            'delta_1m': 0.0,  # Would need more data for real calculation
            'delta_5m': 0.0,
            'buy_pressure_pct': 50.0,  # Neutral simulation
            'sell_wall_size': 0.0,
            'volume_surge': 100.0
        }

    def _calculate_candle_volatility(self, row: pd.Series) -> float:
        """Calculate volatility for a single candle"""
        try:
            high_low_range = (row['high'] - row['low']) / row['close']
            return float(high_low_range)
        except:
            return 0.0

    async def _get_llm_decision(self, market_data: Dict) -> Optional[Dict]:
        """Get trading decision from LLM"""
        try:
            # Build prompt
            prompt = self.prompt_builder.build_prompt(
                market_data=market_data,
                position_data=self.current_position,
                strategy_data=self.strategy_state.get_state_summary(),
                performance_data=self.performance_feedback.get_performance_summary(),
                analysis={},  # Would need technical analysis in real implementation
                account_data=self._get_account_data(),
                opportunity_data=None
            )

            # Get model response
            response = await self.model_runner.get_trading_decision(prompt)
            if not response:
                return None

            # Parse response
            decision = await self.response_parser.parse_response(response, prompt)
            return decision

        except Exception as e:
            self.logger.error(f"Error getting LLM decision: {str(e)}")
            return None

    def _get_rule_based_decision(self, market_data: Dict) -> Optional[Dict]:
        """Get trading decision from rule engine"""
        if not self.rule_engine:
            return None

        try:
            # Simulate basic indicators (in real implementation, would use TechnicalAnalysis)
            indicators = self._simulate_indicators(market_data)

            # Get decision from rule engine
            decision = self.rule_engine.evaluate_conditions(
                market_data, indicators, self.current_position
            )

            # Add required fields for trade execution
            if decision['action'] in [ActionType.LONG.value, ActionType.SHORT.value]:
                decision.update({
                    'entry_price': market_data['price'],
                    'position_size': self._calculate_position_size(market_data),
                    'stop_loss': self._calculate_stop_loss(market_data, decision['action']),
                    'take_profit': self._calculate_take_profit(market_data, decision['action'])
                })

            return decision

        except Exception as e:
            self.logger.error(f"Error getting rule-based decision: {str(e)}")
            return None

    def _simulate_indicators(self, market_data: Dict) -> Dict:
        """Simulate basic technical indicators for rule evaluation"""
        # In a real implementation, this would use the TechnicalAnalysis class
        # For now, simulate basic indicators
        price = market_data['price']

        return {
            '1m': {
                'rsi': 50.0 + np.random.normal(0, 10),  # Simulate RSI around 50
                'macd': np.random.normal(0, 0.1),
                'bb_upper': price * 1.02,
                'bb_lower': price * 0.98,
                'ema_20': price * (1 + np.random.normal(0, 0.01)),
                'sma_50': price * (1 + np.random.normal(0, 0.02))
            }
        }

    def _calculate_position_size(self, market_data: Dict) -> float:
        """Calculate position size based on risk management"""
        try:
            # Use configured position size percentage
            position_size_pct = self.config.get('position_size_pct', 0.02)
            leverage = self.config.get('leverage', 1)

            # Calculate position size in base currency
            position_value = self.current_balance * position_size_pct * leverage
            position_size = position_value / market_data['price']

            return position_size

        except Exception as e:
            self.logger.error(f"Error calculating position size: {str(e)}")
            return 0.0

    def _calculate_stop_loss(self, market_data: Dict, action: str) -> float:
        """Calculate stop loss price"""
        price = market_data['price']
        sl_distance_pct = self.config.get('min_sl_distance_pct', 0.005)

        if action == ActionType.LONG.value:
            return price * (1 - sl_distance_pct)
        elif action == ActionType.SHORT.value:
            return price * (1 + sl_distance_pct)
        else:
            return price

    def _calculate_take_profit(self, market_data: Dict, action: str) -> float:
        """Calculate take profit price"""
        price = market_data['price']
        tp_distance_pct = self.config.get('min_sl_distance_pct', 0.005) * 2  # 2:1 RR

        if action == ActionType.LONG.value:
            return price * (1 + tp_distance_pct)
        elif action == ActionType.SHORT.value:
            return price * (1 - tp_distance_pct)
        else:
            return price

    def _validate_decision(self, decision: Dict, market_data: Dict) -> bool:
        """Validate trading decision with risk guard"""
        try:
            action = decision.get('action')

            # Skip validation for hold actions
            if action == ActionType.HOLD.value:
                return True

            # Check confidence threshold
            confidence = decision.get('confidence', 0.0)
            min_confidence = self.config.get('prompt_confidence_threshold', 0.65)
            if confidence < min_confidence:
                return False

            # Use risk guard for validation
            strategy_state = self.strategy_state.get_state_summary()
            is_valid, reason = self.risk_guard.validate_trade(
                decision, market_data, self.current_position, strategy_state
            )

            if not is_valid:
                self.logger.debug(f"Trade rejected by risk guard: {reason}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Error validating decision: {str(e)}")
            return False

    async def _simulate_trade(self, decision: Dict, market_data: Dict, timestamp: pd.Timestamp):
        """Simulate trade execution and update position"""
        try:
            action = decision.get('action')
            price = market_data['price']

            if action == ActionType.CLOSE.value:
                await self._close_position(price, timestamp, decision.get('reason', ''))

            elif action in [ActionType.LONG.value, ActionType.SHORT.value]:
                # Close existing position if different direction
                if (self.current_position['side'] != PositionSide.NONE.value and
                    self.current_position['side'] != action):
                    await self._close_position(price, timestamp, 'Direction change')

                # Open new position
                await self._open_position(decision, market_data, timestamp)

            # Update position with current market price for unrealized PnL
            self._update_unrealized_pnl(price)

        except Exception as e:
            self.logger.error(f"Error simulating trade: {str(e)}")

    async def _open_position(self, decision: Dict, market_data: Dict, timestamp: pd.Timestamp):
        """Open a new position"""
        try:
            action = decision['action']
            entry_price = decision.get('entry_price', market_data['price'])
            position_size = decision.get('position_size', 0.0)

            if position_size <= 0:
                return

            # Update position state
            self.current_position.update({
                'side': action,
                'size': position_size,
                'entry_price': entry_price,
                'entry_timestamp': timestamp.timestamp(),
                'stop_loss': decision.get('stop_loss', 0.0),
                'take_profit': decision.get('take_profit', 0.0),
                'leverage': self.config.get('leverage', 1),
                'unrealized_pnl': 0.0
            })

            self.logger.debug(f"Opened {action} position: {position_size:.6f} @ {entry_price:.2f}")

        except Exception as e:
            self.logger.error(f"Error opening position: {str(e)}")

    async def _close_position(self, exit_price: float, timestamp: pd.Timestamp, reason: str = ''):
        """Close current position and record trade"""
        try:
            if self.current_position['side'] == PositionSide.NONE.value:
                return

            # Calculate PnL
            entry_price = self.current_position['entry_price']
            position_size = self.current_position['size']
            leverage = self.current_position.get('leverage', 1)

            if self.current_position['side'] == PositionSide.LONG.value:
                pnl_pct = ((exit_price - entry_price) / entry_price) * 100 * leverage
            else:  # SHORT
                pnl_pct = ((entry_price - exit_price) / entry_price) * 100 * leverage

            # Calculate PnL in USDT
            position_value = position_size * entry_price
            pnl_usd = position_value * (pnl_pct / 100)

            # Calculate trade duration
            entry_time = self.current_position.get('entry_timestamp', timestamp.timestamp())
            duration = timestamp.timestamp() - entry_time

            # Create trade record
            trade = BacktestTrade(
                timestamp=timestamp.timestamp(),
                symbol=self.symbol,
                action=self.current_position['side'],
                entry_price=entry_price,
                exit_price=exit_price,
                position_size=position_size,
                pnl=pnl_usd,
                pnl_pct=pnl_pct,
                confidence=0.8,  # Default confidence for backtesting
                reason=reason,
                duration=duration,
                max_adverse_excursion=0.0,  # Would need tick data for accurate calculation
                max_favorable_excursion=0.0
            )

            # Add trade to results
            self.trades.append(trade)

            # Update balance
            self.current_balance += pnl_usd

            # Update performance feedback
            trade_dict = asdict(trade)
            self.performance_feedback.add_trade(trade_dict)

            # Reset position
            self.current_position = {
                'side': PositionSide.NONE.value,
                'size': 0.0,
                'entry_price': 0.0,
                'unrealized_pnl': 0.0,
                'leverage': 1.0
            }

            self.logger.debug(f"Closed position: PnL {pnl_usd:.2f} USDT ({pnl_pct:.2f}%)")

        except Exception as e:
            self.logger.error(f"Error closing position: {str(e)}")

    def _update_unrealized_pnl(self, current_price: float):
        """Update unrealized PnL for open position"""
        try:
            if self.current_position['side'] == PositionSide.NONE.value:
                self.current_position['unrealized_pnl'] = 0.0
                return

            entry_price = self.current_position['entry_price']
            position_size = self.current_position['size']
            leverage = self.current_position.get('leverage', 1)

            if self.current_position['side'] == PositionSide.LONG.value:
                pnl_pct = ((current_price - entry_price) / entry_price) * 100 * leverage
            else:  # SHORT
                pnl_pct = ((entry_price - current_price) / entry_price) * 100 * leverage

            position_value = position_size * entry_price
            unrealized_pnl = position_value * (pnl_pct / 100)

            self.current_position['unrealized_pnl'] = unrealized_pnl

        except Exception as e:
            self.logger.error(f"Error updating unrealized PnL: {str(e)}")

    def _calculate_current_equity(self, current_price: float) -> float:
        """Calculate current account equity including unrealized PnL"""
        self._update_unrealized_pnl(current_price)
        return self.current_balance + self.current_position.get('unrealized_pnl', 0.0)

    def _get_account_data(self) -> Dict:
        """Get account data for LLM prompt building"""
        return {
            'balance': self.current_balance,
            'equity': self.current_balance + self.current_position.get('unrealized_pnl', 0.0),
            'margin_used': 0.0,  # Simplified for backtesting
            'margin_available': self.current_balance,
            'risk_metrics': {
                'account_risk_pct': 0.0,
                'margin_usage_pct': 0.0,
                'available_margin_pct': 100.0,
                'liquidation_risk': 0.0
            },
            'trading_capacity': {
                'max_position_size': self.current_balance * 0.1,
                'safe_position_size': self.current_balance * 0.05,
                'recommended_leverage': self.config.get('leverage', 1)
            }
        }

    def _calculate_results(self) -> BacktestResults:
        """Calculate comprehensive backtest results"""
        try:
            if not self.trades:
                return BacktestResults(
                    equity_curve=self.equity_curve,
                    trades=[],
                    signal_frequency=self.signal_frequency,
                    time_of_day_performance={}
                )

            # Basic metrics
            total_trades = len(self.trades)
            winning_trades = len([t for t in self.trades if t.pnl > 0])
            losing_trades = total_trades - winning_trades
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0.0

            # PnL metrics
            total_pnl = sum(t.pnl for t in self.trades)
            total_pnl_pct = ((self.current_balance - self.initial_balance) / self.initial_balance * 100)

            wins = [t.pnl for t in self.trades if t.pnl > 0]
            losses = [t.pnl for t in self.trades if t.pnl < 0]

            avg_win = np.mean(wins) if wins else 0.0
            avg_loss = np.mean(losses) if losses else 0.0
            profit_factor = abs(sum(wins) / sum(losses)) if losses and sum(losses) != 0 else 0.0

            # Risk metrics
            max_drawdown_pct = self.max_drawdown * 100

            # Calculate Sharpe ratio
            returns = [t.pnl_pct for t in self.trades]
            sharpe_ratio = self._calculate_sharpe_ratio(returns)

            # Calculate Calmar ratio
            calmar_ratio = (total_pnl_pct / max_drawdown_pct) if max_drawdown_pct > 0 else 0.0

            # Time-based metrics
            durations = [t.duration for t in self.trades if t.duration > 0]
            avg_trade_duration = np.mean(durations) / 60 if durations else 0.0  # in minutes

            # Calculate trades per day
            if self.equity_curve:
                start_time = self.equity_curve[0][0]
                end_time = self.equity_curve[-1][0]
                days = (end_time - start_time) / 86400  # seconds to days
                trades_per_day = total_trades / days if days > 0 else 0.0
            else:
                trades_per_day = 0.0

            # Time of day performance
            time_of_day_performance = self._calculate_time_of_day_performance()

            # Signal accuracy (for rule-based strategies)
            signal_accuracy = win_rate / 100.0  # Convert to 0-1 scale

            return BacktestResults(
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                total_pnl=total_pnl,
                total_pnl_pct=total_pnl_pct,
                avg_win=avg_win,
                avg_loss=avg_loss,
                profit_factor=profit_factor,
                max_drawdown=self.max_drawdown * self.initial_balance,
                max_drawdown_pct=max_drawdown_pct,
                sharpe_ratio=sharpe_ratio,
                calmar_ratio=calmar_ratio,
                avg_trade_duration=avg_trade_duration,
                trades_per_day=trades_per_day,
                signal_accuracy=signal_accuracy,
                signal_frequency=self.signal_frequency,
                time_of_day_performance=time_of_day_performance,
                equity_curve=self.equity_curve,
                trades=self.trades
            )

        except Exception as e:
            self.logger.error(f"Error calculating results: {str(e)}")
            return BacktestResults()

    def _calculate_sharpe_ratio(self, returns: List[float]) -> float:
        """Calculate Sharpe ratio from returns"""
        try:
            if not returns or len(returns) < 2:
                return 0.0

            returns_array = np.array(returns)
            mean_return = np.mean(returns_array)
            std_return = np.std(returns_array, ddof=1)

            if std_return == 0:
                return 0.0

            # Annualized Sharpe ratio (assuming daily returns)
            sharpe = (mean_return / std_return) * np.sqrt(252)
            return float(sharpe)

        except Exception as e:
            self.logger.error(f"Error calculating Sharpe ratio: {str(e)}")
            return 0.0

    def _calculate_time_of_day_performance(self) -> Dict[str, float]:
        """Calculate performance by time of day"""
        try:
            hourly_performance = {}

            for hour in range(24):
                hour_trades = [
                    t for t in self.trades
                    if datetime.fromtimestamp(t.timestamp).hour == hour
                ]

                if hour_trades:
                    avg_pnl = np.mean([t.pnl for t in hour_trades])
                    hourly_performance[f"{hour:02d}:00"] = avg_pnl
                else:
                    hourly_performance[f"{hour:02d}:00"] = 0.0

            return hourly_performance

        except Exception as e:
            self.logger.error(f"Error calculating time of day performance: {str(e)}")
            return {}

    async def export_results(self, results: BacktestResults, output_dir: Optional[str] = None) -> str:
        """Export backtest results to JSON file"""
        try:
            # Create output directory
            if output_dir is None:
                output_dir = Path("logs/backtest_results")
            else:
                output_dir = Path(output_dir)

            output_dir.mkdir(parents=True, exist_ok=True)

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.symbol.replace('/', '_')}_{timestamp}.json"
            filepath = output_dir / filename

            # Prepare export data
            export_data = {
                'metadata': {
                    'symbol': self.symbol,
                    'start_time': self.ohlcv_data['timestamp'].iloc[0].isoformat(),
                    'end_time': self.ohlcv_data['timestamp'].iloc[-1].isoformat(),
                    'total_candles': len(self.ohlcv_data),
                    'initial_balance': self.initial_balance,
                    'final_balance': self.current_balance,
                    'strategy_type': 'rule_based' if self.rule_engine else 'llm_driven',
                    'config': self.config
                },
                'performance_metrics': {
                    'total_trades': results.total_trades,
                    'winning_trades': results.winning_trades,
                    'losing_trades': results.losing_trades,
                    'win_rate': results.win_rate,
                    'total_pnl': results.total_pnl,
                    'total_pnl_pct': results.total_pnl_pct,
                    'avg_win': results.avg_win,
                    'avg_loss': results.avg_loss,
                    'profit_factor': results.profit_factor,
                    'max_drawdown': results.max_drawdown,
                    'max_drawdown_pct': results.max_drawdown_pct,
                    'sharpe_ratio': results.sharpe_ratio,
                    'calmar_ratio': results.calmar_ratio,
                    'avg_trade_duration_minutes': results.avg_trade_duration,
                    'trades_per_day': results.trades_per_day,
                    'signal_accuracy': results.signal_accuracy
                },
                'signal_analysis': {
                    'signal_frequency': results.signal_frequency,
                    'time_of_day_performance': results.time_of_day_performance
                },
                'equity_curve': [
                    {'timestamp': ts, 'equity': equity}
                    for ts, equity in results.equity_curve
                ],
                'trades': [
                    {
                        'timestamp': trade.timestamp,
                        'datetime': datetime.fromtimestamp(trade.timestamp).isoformat(),
                        'symbol': trade.symbol,
                        'action': trade.action,
                        'entry_price': trade.entry_price,
                        'exit_price': trade.exit_price,
                        'position_size': trade.position_size,
                        'pnl': trade.pnl,
                        'pnl_pct': trade.pnl_pct,
                        'confidence': trade.confidence,
                        'reason': trade.reason,
                        'duration_seconds': trade.duration,
                        'duration_minutes': trade.duration / 60
                    }
                    for trade in results.trades
                ]
            }

            # Save to file
            import json
            with open(filepath, 'w') as f:
                json.dump(export_data, f, indent=2, default=str)

            self.logger.info(f"Backtest results exported to: {filepath}")
            return str(filepath)

        except Exception as e:
            self.logger.error(f"Error exporting results: {str(e)}")
            raise

    async def _initialize_llm_components(self):
        """Initialize LLM components for decision making"""
        try:
            from llm.model_runner import ModelRunner

            self.model_runner = ModelRunner(self.config)
            self.prompt_builder = LLMPromptBuilder()
            self.response_parser = LLMResponseParser(self.config)

            await self.model_runner.initialize()
            self.logger.info("LLM components initialized for backtesting")

        except Exception as e:
            self.logger.error(f"Failed to initialize LLM components: {str(e)}")
            raise

    async def _process_candle(self, idx: int, row: pd.Series, use_llm: bool):
        """Process a single candle and make trading decisions"""
        try:
            # Create market data from candle
            market_data = self._create_market_data(row)

            # Update strategy state
            self.strategy_state.update(market_data)

            # Get trading decision
            if use_llm:
                decision = await self._get_llm_decision(market_data)
            else:
                decision = self._get_rule_based_decision(market_data)

            if not decision:
                return

            # Track signal frequency
            action = decision.get('action', ActionType.HOLD.value)
            self.signal_frequency[action] += 1

            # Track time-of-day patterns
            hour = row['timestamp'].hour
            if hour not in self.time_of_day_signals:
                self.time_of_day_signals[hour] = []
            self.time_of_day_signals[hour].append(action)

            # Validate decision with risk guard
            if not self._validate_decision(decision, market_data):
                return

            # Execute trade simulation
            await self._simulate_trade(decision, market_data, row['timestamp'])

        except Exception as e:
            self.logger.error(f"Error processing candle {idx}: {str(e)}")

    def _create_market_data(self, row: pd.Series) -> Dict:
        """Create market data dictionary from OHLCV row"""
        return {
            'symbol': self.symbol,
            'timestamp': row['timestamp'].timestamp(),
            'price': float(row['close']),
            'open': float(row['open']),
            'high': float(row['high']),
            'low': float(row['low']),
            'close': float(row['close']),
            'volume': float(row['volume']),
            'bid': float(row['close']) * 0.9995,  # Simulate bid/ask spread
            'ask': float(row['close']) * 1.0005,
            'spread_pct': 0.05,  # 0.05% spread simulation
            'volatility': self._calculate_candle_volatility(row),
            'delta_1m': 0.0,  # Would need more data for real calculation
            'delta_5m': 0.0,
            'buy_pressure_pct': 50.0,  # Neutral simulation
            'sell_wall_size': 0.0,
            'volume_surge': 100.0
        }

    def _calculate_candle_volatility(self, row: pd.Series) -> float:
        """Calculate volatility for a single candle"""
        try:
            high_low_range = (row['high'] - row['low']) / row['close']
            return float(high_low_range)
        except:
            return 0.0

    async def _get_llm_decision(self, market_data: Dict) -> Optional[Dict]:
        """Get trading decision from LLM"""
        try:
            # Build prompt
            prompt = self.prompt_builder.build_prompt(
                market_data=market_data,
                position_data=self.current_position,
                strategy_data=self.strategy_state.get_state(),
                performance_data=self.performance_feedback.get_metrics(),
                analysis={},  # Would need technical analysis in real implementation
                account_data=self._get_account_data(),
                opportunity_data=None
            )

            # Get model response
            response = await self.model_runner.get_trading_decision(prompt)
            if not response:
                return None

            # Parse response
            decision = await self.response_parser.parse_response(response, prompt)
            return decision

        except Exception as e:
            self.logger.error(f"Error getting LLM decision: {str(e)}")
            return None

    def _get_rule_based_decision(self, market_data: Dict) -> Optional[Dict]:
        """Get trading decision from rule engine"""
        if not self.rule_engine:
            return None

        try:
            # Simulate basic indicators (in real implementation, would use TechnicalAnalysis)
            indicators = self._simulate_indicators(market_data)

            # Get decision from rule engine
            decision = self.rule_engine.evaluate_conditions(
                market_data, indicators, self.current_position
            )

            # Add required fields for trade execution
            if decision['action'] in [ActionType.LONG.value, ActionType.SHORT.value]:
                decision.update({
                    'entry_price': market_data['price'],
                    'position_size': self._calculate_position_size(market_data),
                    'stop_loss': self._calculate_stop_loss(market_data, decision['action']),
                    'take_profit': self._calculate_take_profit(market_data, decision['action'])
                })

            return decision

        except Exception as e:
            self.logger.error(f"Error getting rule-based decision: {str(e)}")
            return None

    def _simulate_indicators(self, market_data: Dict) -> Dict:
        """Simulate basic technical indicators for rule evaluation"""
        # In a real implementation, this would use the TechnicalAnalysis class
        # For now, simulate basic indicators
        price = market_data['price']

        return {
            '1m': {
                'rsi': 50.0 + np.random.normal(0, 10),  # Simulate RSI around 50
                'macd': np.random.normal(0, 0.1),
                'bb_upper': price * 1.02,
                'bb_lower': price * 0.98,
                'ema_20': price * (1 + np.random.normal(0, 0.01)),
                'sma_50': price * (1 + np.random.normal(0, 0.02))
            }
        }

    def _calculate_position_size(self, market_data: Dict) -> float:
        """Calculate position size based on risk management"""
        try:
            # Use configured position size percentage
            position_size_pct = self.config.get('position_size_pct', 0.02)
            leverage = self.config.get('leverage', 1)

            # Calculate position size in base currency
            position_value = self.current_balance * position_size_pct * leverage
            position_size = position_value / market_data['price']

            return position_size

        except Exception as e:
            self.logger.error(f"Error calculating position size: {str(e)}")
            return 0.0

    def _calculate_stop_loss(self, market_data: Dict, action: str) -> float:
        """Calculate stop loss price"""
        price = market_data['price']
        sl_distance_pct = self.config.get('min_sl_distance_pct', 0.005)

        if action == ActionType.LONG.value:
            return price * (1 - sl_distance_pct)
        elif action == ActionType.SHORT.value:
            return price * (1 + sl_distance_pct)
        else:
            return price

    def _calculate_take_profit(self, market_data: Dict, action: str) -> float:
        """Calculate take profit price"""
        price = market_data['price']
        tp_distance_pct = self.config.get('min_sl_distance_pct', 0.005) * 2  # 2:1 RR

        if action == ActionType.LONG.value:
            return price * (1 + tp_distance_pct)
        elif action == ActionType.SHORT.value:
            return price * (1 - tp_distance_pct)
        else:
            return price
