#!/usr/bin/env python3
"""
Comprehensive GUI Testing Suite for Epinnox v7 Dashboard

This module provides automated testing for all three dashboard tabs:
- Strategy Analysis Tab
- Parameter Sweeper Tab  
- LLM Simulation Tab

Run with: python tests/test_gui_comprehensive.py
"""

import asyncio
import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

from utils.config_validator import ConfigValidator
from strategy_builder.backtester import Backtester
from gui.llm_sim_tab import LLMSimulationWorker
from utils.logger import get_logger

logger = get_logger()

class GUITestSuite:
    """Comprehensive testing suite for GUI dashboard"""
    
    def __init__(self):
        self.config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
        self.test_results = {
            'strategy_analysis': {},
            'parameter_sweeper': {},
            'llm_simulation': {},
            'performance': {},
            'errors': []
        }
        self.start_time = time.time()
    
    async def run_all_tests(self):
        """Run comprehensive test suite"""
        print("🧪 Starting Epinnox v7 GUI Comprehensive Testing Suite")
        print("=" * 60)
        
        try:
            # Test 1: Strategy Analysis Tab
            await self.test_strategy_analysis()
            
            # Test 2: Parameter Sweeper Components
            await self.test_parameter_sweeper()
            
            # Test 3: LLM Simulation
            await self.test_llm_simulation()
            
            # Test 4: Performance & Load Testing
            await self.test_performance()
            
            # Test 5: Error Handling
            await self.test_error_handling()
            
            # Generate comprehensive report
            self.generate_test_report()
            
        except Exception as e:
            logger.error(f"Critical test failure: {str(e)}")
            self.test_results['errors'].append(f"Critical failure: {str(e)}")
            raise
    
    async def test_strategy_analysis(self):
        """Test Strategy Analysis Tab functionality"""
        print("\n📊 Testing Strategy Analysis Tab...")
        
        test_results = {}
        backtester = Backtester()
        
        # Test 1: Data Fetching
        print("  ├─ Testing data fetching...")
        try:
            start_time = time.time()
            data = await backtester.fetch_historical_data(
                symbol='DOGE/USDT:USDT',
                timeframe='1m',
                days=1,
                exchange_id='binance'
            )
            fetch_time = time.time() - start_time
            
            test_results['data_fetch'] = {
                'success': data is not None and len(data) > 0,
                'candles_count': len(data) if data is not None else 0,
                'fetch_time': fetch_time,
                'data_range': {
                    'start': str(data.iloc[0]['timestamp']) if data is not None and len(data) > 0 else None,
                    'end': str(data.iloc[-1]['timestamp']) if data is not None and len(data) > 0 else None
                }
            }
            print(f"    ✅ Fetched {len(data)} candles in {fetch_time:.2f}s")
            
        except Exception as e:
            test_results['data_fetch'] = {'success': False, 'error': str(e)}
            print(f"    ❌ Data fetch failed: {str(e)}")
        
        # Test 2: Strategy Loading
        print("  ├─ Testing strategy loading...")
        try:
            strategy = backtester.load_strategy('strategy_builder/strategies/simple_momentum.yaml')
            test_results['strategy_load'] = {
                'success': strategy is not None,
                'strategy_name': strategy.get('name', 'Unknown') if strategy else None,
                'has_conditions': 'conditions' in strategy if strategy else False
            }
            print(f"    ✅ Loaded strategy: {strategy.get('name', 'Unknown')}")
            
        except Exception as e:
            test_results['strategy_load'] = {'success': False, 'error': str(e)}
            print(f"    ❌ Strategy load failed: {str(e)}")
        
        # Test 3: Backtest Execution
        print("  ├─ Testing backtest execution...")
        try:
            if test_results['data_fetch']['success'] and test_results['strategy_load']['success']:
                start_time = time.time()
                results = await backtester.run_backtest(
                    symbol='DOGE/USDT:USDT',
                    ohlcv_data=data,
                    strategy_config=strategy,
                    use_llm=False,
                    initial_balance=500.0,
                    leverage=20
                )
                backtest_time = time.time() - start_time
                
                test_results['backtest_execution'] = {
                    'success': results is not None,
                    'execution_time': backtest_time,
                    'total_trades': results.total_trades if results else 0,
                    'win_rate': results.win_rate if results else 0,
                    'total_pnl': results.total_pnl if results else 0,
                    'max_drawdown': results.max_drawdown_pct if results else 0
                }
                print(f"    ✅ Backtest completed in {backtest_time:.2f}s")
                print(f"       Trades: {results.total_trades}, Win Rate: {results.win_rate:.1f}%, PnL: ${results.total_pnl:.2f}")
                
            else:
                test_results['backtest_execution'] = {'success': False, 'error': 'Prerequisites failed'}
                print("    ⚠️  Backtest skipped due to prerequisite failures")
                
        except Exception as e:
            test_results['backtest_execution'] = {'success': False, 'error': str(e)}
            print(f"    ❌ Backtest execution failed: {str(e)}")
        
        # Test 4: Results Analysis
        print("  └─ Testing results analysis...")
        try:
            if test_results['backtest_execution']['success']:
                analysis = backtester.analyze_results(results)
                test_results['results_analysis'] = {
                    'success': analysis is not None,
                    'performance_grade': analysis.get('performance_grade', 'Unknown') if analysis else None,
                    'has_recommendations': 'recommendations' in analysis if analysis else False
                }
                print(f"    ✅ Analysis completed, Grade: {analysis.get('performance_grade', 'Unknown')}")
            else:
                test_results['results_analysis'] = {'success': False, 'error': 'No results to analyze'}
                print("    ⚠️  Analysis skipped - no results available")
                
        except Exception as e:
            test_results['results_analysis'] = {'success': False, 'error': str(e)}
            print(f"    ❌ Results analysis failed: {str(e)}")
        
        self.test_results['strategy_analysis'] = test_results
    
    async def test_parameter_sweeper(self):
        """Test Parameter Sweeper functionality"""
        print("\n🔧 Testing Parameter Sweeper Components...")
        
        test_results = {}
        
        # Test 1: Parameter Range Generation
        print("  ├─ Testing parameter range generation...")
        try:
            # Simulate parameter ranges
            param_ranges = {
                'rsi_long_threshold': {'min': 20, 'max': 40, 'step': 5},
                'rsi_short_threshold': {'min': 60, 'max': 80, 'step': 5},
                'volume_surge_threshold': {'min': 120, 'max': 180, 'step': 20}
            }
            
            # Generate combinations
            combinations = []
            for rsi_long in range(20, 45, 5):
                for rsi_short in range(60, 85, 5):
                    for volume in range(120, 200, 20):
                        combinations.append({
                            'rsi_long_threshold': rsi_long,
                            'rsi_short_threshold': rsi_short,
                            'volume_surge_threshold': volume
                        })
            
            test_results['parameter_generation'] = {
                'success': len(combinations) > 0,
                'total_combinations': len(combinations),
                'sample_combination': combinations[0] if combinations else None
            }
            print(f"    ✅ Generated {len(combinations)} parameter combinations")
            
        except Exception as e:
            test_results['parameter_generation'] = {'success': False, 'error': str(e)}
            print(f"    ❌ Parameter generation failed: {str(e)}")
        
        # Test 2: Small Optimization Run
        print("  ├─ Testing small optimization run...")
        try:
            if test_results['parameter_generation']['success']:
                # Test with first 3 combinations only
                test_combinations = combinations[:3]
                optimization_results = []
                
                backtester = Backtester()
                base_strategy = backtester.load_strategy('strategy_builder/strategies/simple_momentum.yaml')
                
                # Fetch test data
                data = await backtester.fetch_historical_data(
                    symbol='DOGE/USDT:USDT',
                    timeframe='1m',
                    days=0.5,  # Smaller dataset for testing
                    exchange_id='binance'
                )
                
                start_time = time.time()
                for i, params in enumerate(test_combinations):
                    print(f"    │  Running combination {i+1}/{len(test_combinations)}...")
                    
                    # Update strategy with new parameters
                    test_strategy = base_strategy.copy()
                    # Note: In real implementation, you'd update the strategy config here
                    
                    # Run backtest
                    results = await backtester.run_backtest(
                        symbol='DOGE/USDT:USDT',
                        ohlcv_data=data,
                        strategy_config=test_strategy,
                        use_llm=False,
                        initial_balance=500.0,
                        leverage=20
                    )
                    
                    if results:
                        optimization_results.append({
                            'parameters': params,
                            'total_pnl': results.total_pnl,
                            'win_rate': results.win_rate,
                            'max_drawdown': results.max_drawdown_pct,
                            'sharpe_ratio': results.sharpe_ratio
                        })
                
                optimization_time = time.time() - start_time
                
                test_results['optimization_run'] = {
                    'success': len(optimization_results) > 0,
                    'combinations_tested': len(optimization_results),
                    'total_time': optimization_time,
                    'avg_time_per_combination': optimization_time / len(test_combinations) if test_combinations else 0,
                    'best_result': max(optimization_results, key=lambda x: x['total_pnl']) if optimization_results else None
                }
                print(f"    ✅ Optimization completed in {optimization_time:.2f}s")
                
            else:
                test_results['optimization_run'] = {'success': False, 'error': 'Parameter generation failed'}
                print("    ⚠️  Optimization skipped due to parameter generation failure")
                
        except Exception as e:
            test_results['optimization_run'] = {'success': False, 'error': str(e)}
            print(f"    ❌ Optimization run failed: {str(e)}")
        
        # Test 3: Results Export
        print("  └─ Testing results export...")
        try:
            if test_results['optimization_run']['success']:
                # Test CSV export
                export_data = pd.DataFrame(optimization_results)
                export_path = Path("logs/test_optimization_results.csv")
                export_path.parent.mkdir(parents=True, exist_ok=True)
                export_data.to_csv(export_path, index=False)
                
                test_results['results_export'] = {
                    'success': export_path.exists(),
                    'file_size': export_path.stat().st_size if export_path.exists() else 0,
                    'export_path': str(export_path)
                }
                print(f"    ✅ Results exported to {export_path}")
                
            else:
                test_results['results_export'] = {'success': False, 'error': 'No results to export'}
                print("    ⚠️  Export skipped - no results available")
                
        except Exception as e:
            test_results['results_export'] = {'success': False, 'error': str(e)}
            print(f"    ❌ Results export failed: {str(e)}")
        
        self.test_results['parameter_sweeper'] = test_results
    
    async def test_llm_simulation(self):
        """Test LLM Simulation functionality"""
        print("\n🤖 Testing LLM Simulation...")
        
        test_results = {}
        
        # Test 1: Worker Initialization
        print("  ├─ Testing LLM simulation worker...")
        try:
            worker = LLMSimulationWorker('DOGE/USDT:USDT', 30, self.config)
            test_results['worker_init'] = {
                'success': worker is not None,
                'symbol': worker.symbol,
                'interval': worker.interval_seconds
            }
            print(f"    ✅ Worker initialized for {worker.symbol}")
            
        except Exception as e:
            test_results['worker_init'] = {'success': False, 'error': str(e)}
            print(f"    ❌ Worker initialization failed: {str(e)}")
            return
        
        # Test 2: Market Data Creation
        print("  ├─ Testing market data creation...")
        try:
            # Create mock candle data
            mock_candle = pd.Series({
                'timestamp': datetime.now(),
                'open': 0.08,
                'high': 0.081,
                'low': 0.079,
                'close': 0.0805,
                'volume': 1000000
            })
            
            market_data = worker._create_market_data(mock_candle)
            test_results['market_data'] = {
                'success': market_data is not None,
                'has_required_fields': all(key in market_data for key in ['symbol', 'price', 'volume']),
                'price': market_data.get('price'),
                'volatility': market_data.get('volatility')
            }
            print(f"    ✅ Market data created, Price: ${market_data.get('price', 0):.6f}")
            
        except Exception as e:
            test_results['market_data'] = {'success': False, 'error': str(e)}
            print(f"    ❌ Market data creation failed: {str(e)}")
        
        # Test 3: LLM Prediction
        print("  ├─ Testing LLM prediction generation...")
        try:
            if test_results['market_data']['success']:
                prediction = await worker._get_llm_prediction(market_data)
                test_results['llm_prediction'] = {
                    'success': prediction is not None,
                    'has_action': 'action' in prediction if prediction else False,
                    'has_confidence': 'confidence' in prediction if prediction else False,
                    'action': prediction.get('action') if prediction else None,
                    'confidence': prediction.get('confidence') if prediction else None
                }
                print(f"    ✅ Prediction generated: {prediction.get('action', 'None')} ({prediction.get('confidence', 0):.2f})")
                
            else:
                test_results['llm_prediction'] = {'success': False, 'error': 'Market data creation failed'}
                print("    ⚠️  Prediction skipped due to market data failure")
                
        except Exception as e:
            test_results['llm_prediction'] = {'success': False, 'error': str(e)}
            print(f"    ❌ LLM prediction failed: {str(e)}")
        
        # Test 4: Prediction Logging
        print("  └─ Testing prediction logging...")
        try:
            if test_results['llm_prediction']['success']:
                prediction_data = {
                    'timestamp': datetime.now().timestamp(),
                    'symbol': 'DOGE/USDT:USDT',
                    'current_price': 0.0805,
                    'prediction': prediction,
                    'market_data': market_data
                }
                
                worker._log_prediction(prediction_data)
                
                # Check if log file was created
                log_dir = Path("logs/sim_llm_results")
                date_str = datetime.now().strftime("%Y%m%d")
                log_file = log_dir / f"llm_sim_DOGE_USDT_USDT_{date_str}.json"
                
                test_results['prediction_logging'] = {
                    'success': log_file.exists(),
                    'log_file': str(log_file),
                    'file_size': log_file.stat().st_size if log_file.exists() else 0
                }
                print(f"    ✅ Prediction logged to {log_file}")
                
            else:
                test_results['prediction_logging'] = {'success': False, 'error': 'No prediction to log'}
                print("    ⚠️  Logging skipped - no prediction available")
                
        except Exception as e:
            test_results['prediction_logging'] = {'success': False, 'error': str(e)}
            print(f"    ❌ Prediction logging failed: {str(e)}")
        
        self.test_results['llm_simulation'] = test_results
    
    async def test_performance(self):
        """Test system performance and resource usage"""
        print("\n⚡ Testing Performance & Resource Usage...")
        
        test_results = {}
        
        # Test 1: Memory Usage
        print("  ├─ Testing memory usage...")
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            test_results['memory_usage'] = {
                'success': True,
                'rss_mb': memory_info.rss / 1024 / 1024,
                'vms_mb': memory_info.vms / 1024 / 1024,
                'cpu_percent': process.cpu_percent()
            }
            print(f"    ✅ Memory: {memory_info.rss / 1024 / 1024:.1f} MB RSS")
            
        except Exception as e:
            test_results['memory_usage'] = {'success': False, 'error': str(e)}
            print(f"    ❌ Memory test failed: {str(e)}")
        
        # Test 2: Large Dataset Handling
        print("  └─ Testing large dataset handling...")
        try:
            backtester = Backtester()
            start_time = time.time()
            
            # Fetch larger dataset
            large_data = await backtester.fetch_historical_data(
                symbol='DOGE/USDT:USDT',
                timeframe='1m',
                days=7,  # 1 week of data
                exchange_id='binance'
            )
            
            fetch_time = time.time() - start_time
            
            test_results['large_dataset'] = {
                'success': large_data is not None and len(large_data) > 1000,
                'candles_count': len(large_data) if large_data is not None else 0,
                'fetch_time': fetch_time,
                'data_size_mb': large_data.memory_usage(deep=True).sum() / 1024 / 1024 if large_data is not None else 0
            }
            print(f"    ✅ Large dataset: {len(large_data)} candles in {fetch_time:.2f}s")
            
        except Exception as e:
            test_results['large_dataset'] = {'success': False, 'error': str(e)}
            print(f"    ❌ Large dataset test failed: {str(e)}")
        
        self.test_results['performance'] = test_results
    
    async def test_error_handling(self):
        """Test error handling and edge cases"""
        print("\n🛡️  Testing Error Handling...")
        
        test_results = {}
        
        # Test 1: Invalid Symbol
        print("  ├─ Testing invalid symbol handling...")
        try:
            backtester = Backtester()
            invalid_data = await backtester.fetch_historical_data(
                symbol='INVALID/SYMBOL',
                timeframe='1m',
                days=1,
                exchange_id='binance'
            )
            
            test_results['invalid_symbol'] = {
                'success': True,  # Success means it handled the error gracefully
                'data_returned': invalid_data is not None,
                'handled_gracefully': True
            }
            print("    ✅ Invalid symbol handled gracefully")
            
        except Exception as e:
            test_results['invalid_symbol'] = {
                'success': True,  # Exception is expected
                'error_type': type(e).__name__,
                'error_message': str(e)
            }
            print(f"    ✅ Invalid symbol error caught: {type(e).__name__}")
        
        # Test 2: Minimal Data
        print("  └─ Testing minimal data handling...")
        try:
            backtester = Backtester()
            minimal_data = await backtester.fetch_historical_data(
                symbol='DOGE/USDT:USDT',
                timeframe='1m',
                days=0.001,  # Very small amount
                exchange_id='binance'
            )
            
            test_results['minimal_data'] = {
                'success': True,
                'data_returned': minimal_data is not None,
                'candles_count': len(minimal_data) if minimal_data is not None else 0
            }
            print(f"    ✅ Minimal data handled: {len(minimal_data) if minimal_data else 0} candles")
            
        except Exception as e:
            test_results['minimal_data'] = {
                'success': False,
                'error': str(e)
            }
            print(f"    ❌ Minimal data test failed: {str(e)}")
        
        self.test_results['errors'] = test_results
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        total_time = time.time() - self.start_time
        
        print("\n" + "=" * 60)
        print("📋 COMPREHENSIVE TEST REPORT")
        print("=" * 60)
        
        # Summary
        total_tests = 0
        passed_tests = 0
        
        for category, tests in self.test_results.items():
            if category != 'errors':
                for test_name, result in tests.items():
                    total_tests += 1
                    if result.get('success', False):
                        passed_tests += 1
        
        print(f"📊 Overall Results: {passed_tests}/{total_tests} tests passed ({passed_tests/total_tests*100:.1f}%)")
        print(f"⏱️  Total execution time: {total_time:.2f} seconds")
        print()
        
        # Detailed results
        for category, tests in self.test_results.items():
            if category == 'errors':
                continue
                
            print(f"🔍 {category.replace('_', ' ').title()}:")
            for test_name, result in tests.items():
                status = "✅" if result.get('success', False) else "❌"
                print(f"  {status} {test_name.replace('_', ' ').title()}")
                if not result.get('success', False) and 'error' in result:
                    print(f"     Error: {result['error']}")
            print()
        
        # Save detailed report
        report_path = Path("logs/test_reports")
        report_path.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = report_path / f"gui_test_report_{timestamp}.json"
        
        with open(report_file, 'w') as f:
            json.dump({
                'timestamp': timestamp,
                'total_time': total_time,
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'success_rate': passed_tests/total_tests*100 if total_tests > 0 else 0
                },
                'detailed_results': self.test_results
            }, f, indent=2, default=str)
        
        print(f"📄 Detailed report saved to: {report_file}")
        
        # Recommendations
        print("\n🎯 RECOMMENDATIONS:")
        if passed_tests == total_tests:
            print("  ✅ All tests passed! System is ready for next phase.")
        else:
            print("  ⚠️  Some tests failed. Review errors before proceeding.")
            
        if self.test_results.get('performance', {}).get('memory_usage', {}).get('rss_mb', 0) > 500:
            print("  💾 High memory usage detected. Consider optimization.")
            
        if self.test_results.get('strategy_analysis', {}).get('backtest_execution', {}).get('execution_time', 0) > 30:
            print("  ⏱️  Slow backtest execution. Consider performance optimization.")


async def main():
    """Run comprehensive GUI testing"""
    test_suite = GUITestSuite()
    await test_suite.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
