import pytest
import numpy as np
from data.technical_analysis import TechnicalAnalysis

def test_technical_analysis():
    """Test technical analysis calculations"""
    config = {
        'symbols': ['BTC/USDT:USDT'],
        'timeframes': [1, 5, 15, 30, 60]
    }
    
    ta = TechnicalAnalysis(config)
    
    # Generate sample price data (uptrend with noise)
    n_points = 300
    base_price = 50000
    trend = np.linspace(0, 100, n_points)
    noise = np.random.normal(0, 20, n_points)
    prices = base_price + trend + noise
    
    # Generate high/low prices with some volatility
    volatility = np.random.normal(50, 10, n_points)
    highs = prices + np.abs(volatility)
    lows = prices - np.abs(volatility)
    
    # Generate sample volume data with spikes
    base_volume = 100
    volume_trend = np.random.normal(base_volume, 20, n_points)
    volume_spikes = np.random.randint(0, n_points, 10)
    volumes = volume_trend.copy()
    volumes[volume_spikes] *= 3  # Create volume spikes
    
    # Update data points
    symbol = 'BTC/USDT:USDT'
    for i in range(n_points):
        ta.update_data(
            symbol=symbol,
            price=prices[i],
            volume=volumes[i],
            high=highs[i],
            low=lows[i],
            timestamp=int(i * 60000)  # timestamps in milliseconds
        )
    
    # Calculate indicators
    indicators = ta.calculate_indicators(symbol)
    
    # Verify we have data for all timeframes
    for tf in ['1m', '5m', '15m', '30m', '60m']:
        assert tf in indicators
        tf_indicators = indicators[tf]
        
        # Basic indicator checks
        assert tf_indicators['sma_20'] is not None
        assert tf_indicators['sma_50'] is not None
        assert tf_indicators['rsi'] is not None
        assert 0 <= tf_indicators['rsi'] <= 100
        
        # Support/Resistance checks
        assert isinstance(tf_indicators['support_resistance'], list)
        if tf_indicators['support_resistance']:
            sr_level = tf_indicators['support_resistance'][0]
            assert 'price' in sr_level
            assert 'type' in sr_level
            assert 'strength' in sr_level
            
        # Pivot Points checks
        assert isinstance(tf_indicators['pivot_points'], dict)
        assert all(k in tf_indicators['pivot_points'] for k in ['pp', 'r1', 'r2', 'r3', 's1', 's2', 's3'])
        
        # ATR check
        assert tf_indicators['atr'] is not None
        assert tf_indicators['atr'] > 0
        
        # Volume Profile check
        assert isinstance(tf_indicators['volume_profile'], list)
        if tf_indicators['volume_profile']:
            vp_level = tf_indicators['volume_profile'][0]
            assert 'price_level' in vp_level
            assert 'volume' in vp_level
            
        # Market Structure check
        assert isinstance(tf_indicators['market_structure'], dict)
        assert 'trend' in tf_indicators['market_structure']
        assert 'strength' in tf_indicators['market_structure']
        
        # Volatility Ratio check
        assert tf_indicators['volatility_ratio'] is not None

        # Trend Lines check
        assert isinstance(tf_indicators['trend_lines'], list)
        if tf_indicators['trend_lines']:
            line = tf_indicators['trend_lines'][0]
            assert 'type' in line
            assert 'slope' in line
            assert 'intercept' in line
            assert 'strength' in line
            
        # Fibonacci Levels check
        assert isinstance(tf_indicators['fibonacci_levels'], dict)
        if tf_indicators['fibonacci_levels']:
            assert 'trend' in tf_indicators['fibonacci_levels']
            assert 'retracement_levels' in tf_indicators['fibonacci_levels']
            assert 'extension_levels' in tf_indicators['fibonacci_levels']
            
        # Candlestick Patterns check
        assert isinstance(tf_indicators['candlestick_patterns'], list)
        if tf_indicators['candlestick_patterns']:
            pattern = tf_indicators['candlestick_patterns'][0]
            assert 'name' in pattern
            assert 'type' in pattern
            assert 'strength' in pattern
            
        # Chart Patterns check
        assert isinstance(tf_indicators['chart_patterns'], list)
        if tf_indicators['chart_patterns']:
            pattern = tf_indicators['chart_patterns'][0]
            assert 'name' in pattern
            assert 'type' in pattern
            assert 'strength' in pattern

        # Manipulation Metrics check
        assert isinstance(tf_indicators['manipulation_metrics'], dict)
        assert 'overall_score' in tf_indicators['manipulation_metrics']
        assert 'safe_to_trade' in tf_indicators['manipulation_metrics']
        
        # Check specific manipulation patterns
        manipulation_metrics = tf_indicators['manipulation_metrics']
        
        # Spoofing detection
        assert isinstance(manipulation_metrics['spoofing_indicators'], dict)
        assert 'score' in manipulation_metrics['spoofing_indicators']
        assert 0 <= manipulation_metrics['spoofing_indicators']['score'] <= 1
        
        # Wash trading detection
        assert isinstance(manipulation_metrics['wash_trading'], dict)
        assert 'score' in manipulation_metrics['wash_trading']
        assert 0 <= manipulation_metrics['wash_trading']['score'] <= 1
        
        # Momentum ignition detection
        assert isinstance(manipulation_metrics['momentum_ignition'], dict)
        assert 'score' in manipulation_metrics['momentum_ignition']
        assert 0 <= manipulation_metrics['momentum_ignition']['score'] <= 1
        
        # Layering detection
        assert isinstance(manipulation_metrics['layering_indicators'], dict)
        assert 'score' in manipulation_metrics['layering_indicators']
        assert 0 <= manipulation_metrics['layering_indicators']['score'] <= 1
        
        # Natural trading evaluation
        assert isinstance(manipulation_metrics['natural_trading'], dict)
        assert 'confidence' in manipulation_metrics['natural_trading']
        assert 0 <= manipulation_metrics['natural_trading']['confidence'] <= 1

def test_indicator_edge_cases():
    """Test technical analysis with edge cases"""
    config = {
        'symbols': ['BTC/USDT:USDT'],
        'timeframes': [1, 5, 15]
    }
    
    ta = TechnicalAnalysis(config)
    symbol = 'BTC/USDT:USDT'
    
    # Test with minimal data points
    ta.update_data(symbol, 50000, 100, 50100, 49900, 1000000)
    indicators = ta.calculate_indicators(symbol)
    
    # Should return indicators dict but with None values for insufficient data
    assert '1m' in indicators
    assert indicators['1m']['sma_20'] is None  # Not enough data for 20-period SMA
    
    # Test with NaN/zero values
    ta.update_data(symbol, np.nan, 0, np.nan, np.nan, 2000000)
    indicators = ta.calculate_indicators(symbol)
    assert '1m' in indicators  # Should handle NaN gracefully