"""
Base Exchange Interface for Epinnox v7

Abstract base class for cryptocurrency exchange integrations.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from utils.logger import get_logger

logger = get_logger()


class OrderType(Enum):
    """Order types"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"


class OrderSide(Enum):
    """Order sides"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """Order status"""
    PENDING = "pending"
    OPEN = "open"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


@dataclass
class MarketData:
    """Market data structure"""
    symbol: str
    timestamp: datetime
    bid: float
    ask: float
    last: float
    volume: float
    high_24h: float
    low_24h: float
    change_24h: float
    change_pct_24h: float
    
    @property
    def spread(self) -> float:
        return self.ask - self.bid
    
    @property
    def spread_pct(self) -> float:
        return (self.spread / self.last) * 100 if self.last > 0 else 0


@dataclass
class Position:
    """Position data structure"""
    symbol: str
    side: str
    size: float
    entry_price: float
    mark_price: float
    unrealized_pnl: float
    percentage: float
    margin: float
    liquidation_price: float
    timestamp: datetime


@dataclass
class Order:
    """Order data structure"""
    id: str
    symbol: str
    type: OrderType
    side: OrderSide
    amount: float
    price: Optional[float]
    status: OrderStatus
    filled: float
    remaining: float
    timestamp: datetime
    fee: float = 0.0


@dataclass
class Balance:
    """Account balance structure"""
    currency: str
    free: float
    used: float
    total: float


class BaseExchange(ABC):
    """Abstract base class for exchange implementations"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger()
        self.name = self.__class__.__name__
        
        # Connection state
        self.connected = False
        self.authenticated = False
        
        # Rate limiting
        self.rate_limit_remaining = 1000
        self.rate_limit_reset = datetime.now()
        
        # API credentials (loaded securely)
        self.api_key = None
        self.api_secret = None
        self.passphrase = None  # For some exchanges
        
        self.logger.info(f"Initializing {self.name} exchange interface")
    
    @abstractmethod
    async def connect(self) -> bool:
        """Connect to exchange API"""
        pass
    
    @abstractmethod
    async def disconnect(self):
        """Disconnect from exchange API"""
        pass
    
    @abstractmethod
    async def authenticate(self, api_key: str, api_secret: str, passphrase: str = None) -> bool:
        """Authenticate with exchange API"""
        pass
    
    @abstractmethod
    async def get_market_data(self, symbol: str) -> MarketData:
        """Get current market data for symbol"""
        pass
    
    @abstractmethod
    async def get_orderbook(self, symbol: str, limit: int = 20) -> Dict[str, List[List[float]]]:
        """Get order book for symbol"""
        pass
    
    @abstractmethod
    async def get_klines(self, symbol: str, interval: str, limit: int = 100) -> List[List[float]]:
        """Get candlestick data"""
        pass
    
    @abstractmethod
    async def get_account_balance(self) -> List[Balance]:
        """Get account balances"""
        pass
    
    @abstractmethod
    async def get_positions(self) -> List[Position]:
        """Get open positions"""
        pass
    
    @abstractmethod
    async def get_open_orders(self, symbol: str = None) -> List[Order]:
        """Get open orders"""
        pass
    
    @abstractmethod
    async def place_order(self, symbol: str, side: OrderSide, type: OrderType, 
                         amount: float, price: float = None, 
                         stop_price: float = None, **kwargs) -> Order:
        """Place an order"""
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order"""
        pass
    
    @abstractmethod
    async def cancel_all_orders(self, symbol: str = None) -> int:
        """Cancel all orders"""
        pass
    
    @abstractmethod
    async def get_trading_fees(self, symbol: str = None) -> Dict[str, float]:
        """Get trading fees"""
        pass
    
    # Common utility methods
    def is_connected(self) -> bool:
        """Check if connected to exchange"""
        return self.connected
    
    def is_authenticated(self) -> bool:
        """Check if authenticated with exchange"""
        return self.authenticated
    
    def get_rate_limit_info(self) -> Dict[str, Any]:
        """Get rate limit information"""
        return {
            'remaining': self.rate_limit_remaining,
            'reset_time': self.rate_limit_reset,
            'seconds_until_reset': (self.rate_limit_reset - datetime.now()).total_seconds()
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        try:
            # Test connection
            if not self.connected:
                await self.connect()
            
            # Test API call
            balances = await self.get_account_balance()
            
            return {
                'status': 'healthy',
                'connected': self.connected,
                'authenticated': self.authenticated,
                'timestamp': datetime.now().isoformat(),
                'balance_count': len(balances) if balances else 0
            }
        
        except Exception as e:
            self.logger.error(f"Health check failed: {str(e)}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'connected': self.connected,
                'authenticated': self.authenticated,
                'timestamp': datetime.now().isoformat()
            }
    
    def validate_symbol(self, symbol: str) -> bool:
        """Validate symbol format"""
        # Basic validation - override in specific exchange implementations
        return '/' in symbol and len(symbol.split('/')) == 2
    
    def validate_order_params(self, symbol: str, side: OrderSide, type: OrderType, 
                            amount: float, price: float = None) -> Tuple[bool, str]:
        """Validate order parameters"""
        # Symbol validation
        if not self.validate_symbol(symbol):
            return False, f"Invalid symbol format: {symbol}"
        
        # Amount validation
        if amount <= 0:
            return False, f"Invalid amount: {amount}"
        
        # Price validation for limit orders
        if type == OrderType.LIMIT and (price is None or price <= 0):
            return False, f"Invalid price for limit order: {price}"
        
        return True, "Valid order parameters"
    
    async def safe_api_call(self, func, *args, **kwargs):
        """Safely execute API call with error handling"""
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            self.logger.error(f"API call failed: {func.__name__} - {str(e)}")
            raise
    
    def format_symbol(self, symbol: str) -> str:
        """Format symbol for exchange (override in implementations)"""
        return symbol
    
    def parse_symbol(self, exchange_symbol: str) -> str:
        """Parse exchange symbol to standard format"""
        return exchange_symbol
