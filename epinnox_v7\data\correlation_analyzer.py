from typing import Dict, List, Optional
import numpy as np
import pandas as pd
from scipy import stats
from utils.logger import get_logger

class CorrelationAnalyzer:
    """Advanced correlation analysis for multi-symbol trading"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # Initialize correlation caches
        self.price_correlations = {}
        self.volume_correlations = {}
        self.volatility_correlations = {}
        self.lead_lag_relationships = {}
        
        # Configuration
        self.correlation_lookback = config.get('correlation_lookback', 100)
        self.min_correlation = config.get('min_correlation', 0.7)
        self.lead_lag_max = config.get('lead_lag_max', 10)  # Maximum lag to check
        
        # Sector mappings (can be expanded)
        self.sector_mappings = {
            'BTC': 'MAJOR',
            'ETH': 'MAJOR',
            'BNB': 'EXCHANGE',
            'SOL': 'L1',
            'MATIC': 'L2',
            'LINK': 'ORACLE',
            'UNI': 'DEX',
            'AAVE': 'DEFI'
        }

    def analyze_correlations(self, market_data: Dict) -> Dict:
        """Perform comprehensive correlation analysis"""
        correlations = {
            'price_correlations': self._analyze_price_correlations(market_data),
            'volume_correlations': self._analyze_volume_correlations(market_data),
            'volatility_correlations': self._analyze_volatility_correlations(market_data),
            'lead_lag': self._analyze_lead_lag_relationships(market_data),
            'sector_analysis': self._analyze_sector_relationships(market_data),
            'divergence_opportunities': self._find_divergence_opportunities(market_data)
        }
        
        return correlations

    def _analyze_price_correlations(self, market_data: Dict) -> Dict:
        """Analyze price correlations between symbols"""
        correlations = {}
        prices = {}
        
        # Extract price series
        for symbol, data in market_data.items():
            if 'close' in data:
                prices[symbol] = pd.Series(data['close'][-self.correlation_lookback:])
        
        if len(prices) < 2:
            return correlations
            
        # Calculate correlation matrix
        price_df = pd.DataFrame(prices)
        corr_matrix = price_df.corr(method='spearman')
        
        # Find significant correlations
        for symbol1 in corr_matrix.index:
            correlations[symbol1] = {}
            for symbol2 in corr_matrix.columns:
                if symbol1 != symbol2:
                    corr = corr_matrix.loc[symbol1, symbol2]
                    if abs(corr) >= self.min_correlation:
                        correlations[symbol1][symbol2] = {
                            'correlation': float(corr),
                            'strength': 'strong' if abs(corr) > 0.8 else 'moderate'
                        }
        
        return correlations

    def _analyze_volume_correlations(self, market_data: Dict) -> Dict:
        """Analyze volume correlations between symbols"""
        correlations = {}
        volumes = {}
        
        # Extract volume series
        for symbol, data in market_data.items():
            if 'volume' in data:
                volumes[symbol] = pd.Series(data['volume'][-self.correlation_lookback:])
        
        if len(volumes) < 2:
            return correlations
            
        # Calculate volume correlation matrix
        volume_df = pd.DataFrame(volumes)
        corr_matrix = volume_df.corr(method='spearman')
        
        # Find significant correlations
        for symbol1 in corr_matrix.index:
            correlations[symbol1] = {}
            for symbol2 in corr_matrix.columns:
                if symbol1 != symbol2:
                    corr = corr_matrix.loc[symbol1, symbol2]
                    if abs(corr) >= self.min_correlation:
                        correlations[symbol1][symbol2] = {
                            'correlation': float(corr),
                            'strength': 'strong' if abs(corr) > 0.8 else 'moderate'
                        }
        
        return correlations

    def _analyze_volatility_correlations(self, market_data: Dict) -> Dict:
        """Analyze volatility correlations between symbols"""
        correlations = {}
        volatilities = {}
        
        # Calculate rolling volatilities
        for symbol, data in market_data.items():
            if 'close' in data:
                prices = pd.Series(data['close'])
                returns = prices.pct_change()
                vol = returns.rolling(window=20).std()
                volatilities[symbol] = vol[-self.correlation_lookback:]
        
        if len(volatilities) < 2:
            return correlations
            
        # Calculate volatility correlation matrix
        vol_df = pd.DataFrame(volatilities)
        corr_matrix = vol_df.corr(method='spearman')
        
        # Find significant correlations
        for symbol1 in corr_matrix.index:
            correlations[symbol1] = {}
            for symbol2 in corr_matrix.columns:
                if symbol1 != symbol2:
                    corr = corr_matrix.loc[symbol1, symbol2]
                    if abs(corr) >= self.min_correlation:
                        correlations[symbol1][symbol2] = {
                            'correlation': float(corr),
                            'strength': 'strong' if abs(corr) > 0.8 else 'moderate'
                        }
        
        return correlations

    def _analyze_lead_lag_relationships(self, market_data: Dict) -> Dict:
        """Analyze lead-lag relationships between symbols"""
        relationships = {}
        
        for symbol1 in market_data:
            if 'close' not in market_data[symbol1]:
                continue
                
            prices1 = pd.Series(market_data[symbol1]['close'])
            returns1 = prices1.pct_change().dropna()
            
            relationships[symbol1] = {}
            
            for symbol2 in market_data:
                if symbol1 == symbol2 or 'close' not in market_data[symbol2]:
                    continue
                    
                prices2 = pd.Series(market_data[symbol2]['close'])
                returns2 = prices2.pct_change().dropna()
                
                # Calculate cross-correlation for different lags
                cross_corr = {}
                for lag in range(-self.lead_lag_max, self.lead_lag_max + 1):
                    if lag >= 0:
                        corr = returns1.corr(returns2.shift(lag))
                    else:
                        corr = returns1.shift(-lag).corr(returns2)
                    
                    if corr and abs(corr) >= self.min_correlation:
                        cross_corr[lag] = float(corr)
                
                if cross_corr:
                    # Find the lag with maximum correlation
                    max_lag = max(cross_corr.items(), key=lambda x: abs(x[1]))
                    relationships[symbol1][symbol2] = {
                        'lag': max_lag[0],
                        'correlation': max_lag[1],
                        'relationship': 'leader' if max_lag[0] > 0 else 'follower'
                    }
        
        return relationships

    def _analyze_sector_relationships(self, market_data: Dict) -> Dict:
        """Analyze relationships within and between sectors"""
        sector_analysis = {
            'sector_correlations': {},
            'sector_strength': {},
            'rotation_signals': []
        }
        
        # Group symbols by sector
        sector_groups = {}
        for symbol in market_data:
            base_symbol = symbol.split('/')[0]
            sector = self.sector_mappings.get(base_symbol, 'OTHER')
            if sector not in sector_groups:
                sector_groups[sector] = []
            sector_groups[sector].append(symbol)
        
        # Calculate sector correlations
        for sector1, symbols1 in sector_groups.items():
            sector_analysis['sector_correlations'][sector1] = {}
            
            # Calculate sector strength (average performance)
            sector_returns = []
            for symbol in symbols1:
                if 'close' in market_data[symbol]:
                    prices = pd.Series(market_data[symbol]['close'])
                    returns = prices.pct_change().mean()
                    sector_returns.append(returns)
            
            if sector_returns:
                sector_analysis['sector_strength'][sector1] = float(np.mean(sector_returns))
            
            # Calculate inter-sector correlations
            for sector2, symbols2 in sector_groups.items():
                if sector1 != sector2:
                    sector_corr = self._calculate_sector_correlation(
                        sector1, sector2, symbols1, symbols2, market_data
                    )
                    if sector_corr:
                        sector_analysis['sector_correlations'][sector1][sector2] = sector_corr
        
        # Detect sector rotation
        sector_momentum = sorted(
            sector_analysis['sector_strength'].items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        if sector_momentum:
            sector_analysis['rotation_signals'].append({
                'strong_sectors': [s[0] for s in sector_momentum[:2]],
                'weak_sectors': [s[0] for s in sector_momentum[-2:]],
                'rotation_type': 'risk_on' if sector_momentum[0][0] in ['MAJOR', 'L1']
                               else 'risk_off'
            })
        
        return sector_analysis

    def _find_divergence_opportunities(self, market_data: Dict) -> List[Dict]:
        """Find divergence opportunities between correlated symbols"""
        opportunities = []
        
        price_correlations = self._analyze_price_correlations(market_data)
        
        for symbol1, correlations in price_correlations.items():
            for symbol2, corr_data in correlations.items():
                if corr_data['correlation'] > self.min_correlation:
                    # Calculate recent performance divergence
                    perf1 = self._calculate_recent_performance(market_data[symbol1])
                    perf2 = self._calculate_recent_performance(market_data[symbol2])
                    
                    # Check for significant divergence
                    divergence = perf1 - perf2
                    if abs(divergence) > 0.1:  # 10% divergence threshold
                        opportunities.append({
                            'symbol1': symbol1,
                            'symbol2': symbol2,
                            'correlation': corr_data['correlation'],
                            'divergence': float(divergence),
                            'type': 'bullish' if divergence < 0 else 'bearish',
                            'strength': abs(divergence) / 0.1  # Normalize to 1.0 base
                        })
        
        return opportunities

    def _calculate_sector_correlation(
        self,
        sector1: str,
        sector2: str,
        symbols1: List[str],
        symbols2: List[str],
        market_data: Dict
    ) -> Optional[Dict]:
        """Calculate correlation between two sectors"""
        sector1_returns = []
        sector2_returns = []
        
        for symbol in symbols1:
            if 'close' in market_data[symbol]:
                prices = pd.Series(market_data[symbol]['close'])
                returns = prices.pct_change().dropna()
                sector1_returns.append(returns)
        
        for symbol in symbols2:
            if 'close' in market_data[symbol]:
                prices = pd.Series(market_data[symbol]['close'])
                returns = prices.pct_change().dropna()
                sector2_returns.append(returns)
        
        if not sector1_returns or not sector2_returns:
            return None
        
        # Calculate average sector returns
        sector1_avg = pd.concat(sector1_returns, axis=1).mean(axis=1)
        sector2_avg = pd.concat(sector2_returns, axis=1).mean(axis=1)
        
        correlation = sector1_avg.corr(sector2_avg)
        if correlation and abs(correlation) >= self.min_correlation:
            return {
                'correlation': float(correlation),
                'strength': 'strong' if abs(correlation) > 0.8 else 'moderate'
            }
        
        return None

    def _calculate_recent_performance(self, symbol_data: Dict) -> float:
        """Calculate recent performance for a symbol"""
        if 'close' not in symbol_data:
            return 0.0
            
        prices = pd.Series(symbol_data['close'])
        if len(prices) < 2:
            return 0.0
            
        return (prices.iloc[-1] / prices.iloc[-20] - 1) * 100  # 20-period performance