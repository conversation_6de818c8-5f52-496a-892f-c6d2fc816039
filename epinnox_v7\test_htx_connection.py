#!/usr/bin/env python3
"""
Test HTX API Connection

Simple test to verify HTX API credentials work correctly.
"""

import asyncio
import aiohttp
import hmac
import hashlib
import base64
from datetime import datetime
from urllib.parse import urlencode

# Your test credentials
API_KEY = "nbtycf4rw2-72d300ec-fb900970-27ef8"
API_SECRET = "b4d92e15-523563a0-72a16ad9-9a275"

async def test_htx_connection():
    """Test HTX API connection and authentication"""
    print("🧪 Testing HTX API Connection...")
    
    # Test 1: Public API (no authentication required)
    print("\n1. Testing public API connection...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("https://api.huobi.pro/v1/common/timestamp") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Public API works: Server time = {data.get('data', 'N/A')}")
                else:
                    print(f"❌ Public API failed: Status {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Public API error: {str(e)}")
        return False
    
    # Test 2: Get trading symbols
    print("\n2. Testing symbols endpoint...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("https://api.huobi.pro/v1/common/symbols") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == 'ok':
                        symbols_count = len(data.get('data', []))
                        print(f"✅ Symbols endpoint works: {symbols_count} symbols available")
                    else:
                        print(f"❌ Symbols endpoint error: {data}")
                        return False
                else:
                    print(f"❌ Symbols endpoint failed: Status {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Symbols endpoint error: {str(e)}")
        return False
    
    # Test 3: Authentication test
    print("\n3. Testing authentication...")
    try:
        # Create signed request for account info
        endpoint = "/v1/account/accounts"
        method = "GET"
        timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S')
        
        # Prepare parameters
        params = {
            'AccessKeyId': API_KEY,
            'SignatureMethod': 'HmacSHA256',
            'SignatureVersion': '2',
            'Timestamp': timestamp
        }
        
        # Sort and encode parameters
        sorted_params = sorted(params.items())
        query_string = urlencode(sorted_params)
        
        # Create signature payload
        payload = f"{method}\napi.huobi.pro\n{endpoint}\n{query_string}"
        
        # Generate signature
        signature = base64.b64encode(
            hmac.new(
                API_SECRET.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        
        params['Signature'] = signature
        
        # Make authenticated request
        async with aiohttp.ClientSession() as session:
            url = f"https://api.huobi.pro{endpoint}"
            async with session.get(url, params=params) as response:
                data = await response.json()
                
                if response.status == 200 and data.get('status') == 'ok':
                    accounts = data.get('data', [])
                    print(f"✅ Authentication successful: {len(accounts)} accounts found")
                    
                    # Show account types
                    for account in accounts:
                        print(f"   Account: {account.get('type', 'unknown')} (ID: {account.get('id', 'N/A')})")
                    
                    return True
                else:
                    print(f"❌ Authentication failed:")
                    print(f"   Status: {response.status}")
                    print(f"   Response: {data}")
                    
                    # Check for common errors
                    if 'api-signature-not-valid' in str(data):
                        print("   💡 Signature validation failed - check API secret")
                    elif 'invalid-access-key' in str(data):
                        print("   💡 Invalid access key - check API key")
                    elif 'api-key-expired' in str(data):
                        print("   💡 API key expired - generate new keys")
                    
                    return False
    
    except Exception as e:
        print(f"❌ Authentication test error: {str(e)}")
        return False

async def test_market_data():
    """Test market data retrieval"""
    print("\n4. Testing market data...")
    try:
        async with aiohttp.ClientSession() as session:
            # Test BTC/USDT ticker
            symbol = "btcusdt"
            async with session.get(f"https://api.huobi.pro/market/detail/merged?symbol={symbol}") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == 'ok':
                        tick = data.get('tick', {})
                        price = tick.get('close', 0)
                        print(f"✅ Market data works: BTC/USDT = ${price}")
                        return True
                    else:
                        print(f"❌ Market data error: {data}")
                        return False
                else:
                    print(f"❌ Market data failed: Status {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Market data error: {str(e)}")
        return False

async def main():
    """Run all tests"""
    print("🔍 HTX API Connection Test")
    print("=" * 40)
    
    # Test basic connection
    if not await test_htx_connection():
        print("\n❌ HTX connection test failed!")
        return False
    
    # Test market data
    if not await test_market_data():
        print("\n❌ Market data test failed!")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 All HTX API tests passed!")
    print("✅ Your credentials are working correctly")
    print("✅ Ready to proceed with live trading setup")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    if not success:
        print("\n💡 Troubleshooting tips:")
        print("1. Verify your API key and secret are correct")
        print("2. Check if your IP is whitelisted on HTX")
        print("3. Ensure API key has trading permissions")
        print("4. Try generating new API keys if issues persist")
    
    exit(0 if success else 1)
