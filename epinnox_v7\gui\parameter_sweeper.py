"""
Parameter Sweeper for Epinnox v7 Dashboard

This module provides strategy parameter optimization through systematic testing
of parameter ranges and combinations. Features include:
- Parameter range configuration
- Batch backtest execution
- Results comparison and ranking
- CSV export of optimization results
- Visual parameter sensitivity analysis
"""

import csv
import asyncio
import yaml
from pathlib import Path
from typing import Dict, <PERSON>, Tuple, Any
from datetime import datetime
from itertools import product

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QGridLayout,
    QPushButton, QLabel, QTableWidget, QTableWidgetItem, QTextEdit,
    QComboBox, QSpinBox, QDoubleSpinBox, QFileDialog, QMessageBox,
    QProgressBar, QTabWidget, QScrollArea, QCheckBox, QLineEdit
)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QColor

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import pandas as pd
import numpy as np

from strategy_builder.backtester import Backtester
from core.strategy_backtester import BacktestResults
from utils.logger import get_logger


class ParameterSweepWorker(QThread):
    """Worker thread for running parameter sweep optimization"""
    
    finished = pyqtSignal(list)  # List of (parameters, results) tuples
    error = pyqtSignal(str)
    progress = pyqtSignal(int, str)  # progress percentage, current test description
    
    def __init__(self, base_strategy_path, symbol, parameter_ranges, initial_balance, leverage, days):
        super().__init__()
        self.base_strategy_path = base_strategy_path
        self.symbol = symbol
        self.parameter_ranges = parameter_ranges
        self.initial_balance = initial_balance
        self.leverage = leverage
        self.days = days
        self.logger = get_logger()
        self.should_stop = False
    
    def stop(self):
        """Stop the parameter sweep"""
        self.should_stop = True
    
    def run(self):
        """Run parameter sweep in separate thread"""
        try:
            # Create event loop for async operations
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run parameter sweep
            results = loop.run_until_complete(self._run_parameter_sweep())
            
            if not self.should_stop:
                self.finished.emit(results)
            
        except Exception as e:
            self.logger.error(f"Parameter sweep error: {str(e)}")
            self.error.emit(str(e))
        finally:
            try:
                loop.close()
            except:
                pass
    
    async def _run_parameter_sweep(self):
        """Execute parameter sweep with all combinations"""
        results = []
        
        # Generate all parameter combinations
        param_names = list(self.parameter_ranges.keys())
        param_values = [self.parameter_ranges[name] for name in param_names]
        combinations = list(product(*param_values))
        
        total_combinations = len(combinations)
        self.logger.info(f"Starting parameter sweep with {total_combinations} combinations")
        
        backtester = Backtester()
        
        # Fetch data once for all tests
        self.progress.emit(0, "Fetching historical data...")
        data = await backtester.fetch_historical_data(
            symbol=self.symbol,
            timeframe='1m',
            days=self.days,
            exchange_id='binance'
        )
        
        if data is None or len(data) < 100:
            raise ValueError("Insufficient historical data")
        
        # Load base strategy
        with open(self.base_strategy_path, 'r') as f:
            base_strategy = yaml.safe_load(f)
        
        for i, combination in enumerate(combinations):
            if self.should_stop:
                break
            
            try:
                # Create parameter dict
                params = dict(zip(param_names, combination))
                
                # Update progress
                progress_pct = int((i / total_combinations) * 100)
                param_str = ", ".join([f"{k}={v}" for k, v in params.items()])
                self.progress.emit(progress_pct, f"Testing: {param_str}")
                
                # Modify strategy with current parameters
                modified_strategy = self._apply_parameters(base_strategy.copy(), params)
                
                # Run backtest
                result = await backtester.run_backtest(
                    symbol=self.symbol,
                    ohlcv_data=data,
                    strategy_config=modified_strategy,
                    use_llm=False,
                    initial_balance=self.initial_balance,
                    leverage=self.leverage
                )
                
                results.append((params, result))
                
            except Exception as e:
                self.logger.warning(f"Error testing combination {params}: {str(e)}")
                continue
        
        return results
    
    def _apply_parameters(self, strategy: dict, params: dict) -> dict:
        """Apply parameter values to strategy configuration"""
        try:
            # Apply parameters to strategy rules
            for param_name, param_value in params.items():
                self._update_strategy_parameter(strategy, param_name, param_value)
            
            return strategy
            
        except Exception as e:
            self.logger.error(f"Error applying parameters: {str(e)}")
            return strategy
    
    def _update_strategy_parameter(self, strategy: dict, param_name: str, param_value: Any):
        """Update a specific parameter in the strategy configuration"""
        # This is a simplified parameter mapping - in a real implementation,
        # you would have a more sophisticated parameter mapping system
        
        if param_name == "rsi_threshold_long":
            # Update RSI threshold for long entries
            for rule in strategy.get('rules', {}).get('long_entry', []):
                if rule.get('type') == 'indicator_above' and rule.get('indicator') == 'rsi':
                    rule['value'] = param_value
        
        elif param_name == "rsi_threshold_short":
            # Update RSI threshold for short entries
            for rule in strategy.get('rules', {}).get('short_entry', []):
                if rule.get('type') == 'indicator_below' and rule.get('indicator') == 'rsi':
                    rule['value'] = param_value
        
        elif param_name == "volume_surge_threshold":
            # Update volume surge threshold
            for rule_group in strategy.get('rules', {}).values():
                if isinstance(rule_group, list):
                    for rule in rule_group:
                        if rule.get('type') == 'volume_surge':
                            rule['value'] = param_value
        
        elif param_name == "stop_loss_pct":
            # Update stop loss percentage
            if 'risk_management' in strategy:
                strategy['risk_management']['stop_loss_pct'] = param_value
        
        elif param_name == "take_profit_pct":
            # Update take profit percentage
            if 'risk_management' in strategy:
                strategy['risk_management']['take_profit_pct'] = param_value


class ParameterSweeper(QWidget):
    """Parameter optimization widget for strategy tuning"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger()
        self.sweep_worker = None
        self.sweep_results = []
        
        self.init_ui()
    
    def init_ui(self):
        """Initialize the parameter sweeper UI"""
        layout = QVBoxLayout(self)
        
        # Configuration section
        config_group = QGroupBox("Optimization Configuration")
        config_layout = QGridLayout(config_group)
        
        # Strategy file selection
        config_layout.addWidget(QLabel("Base Strategy:"), 0, 0)
        strategy_layout = QHBoxLayout()
        self.strategy_path_label = QLabel("No strategy selected")
        self.strategy_path_label.setStyleSheet("border: 1px solid #ccc; padding: 4px;")
        strategy_layout.addWidget(self.strategy_path_label)
        
        browse_btn = QPushButton("Browse...")
        browse_btn.clicked.connect(self.browse_strategy)
        strategy_layout.addWidget(browse_btn)
        config_layout.addLayout(strategy_layout, 0, 1)
        
        # Symbol and basic parameters
        config_layout.addWidget(QLabel("Symbol:"), 1, 0)
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems(['DOGE/USDT:USDT', 'BTC/USDT:USDT', 'ETH/USDT:USDT'])
        config_layout.addWidget(self.symbol_combo, 1, 1)
        
        config_layout.addWidget(QLabel("Initial Balance:"), 2, 0)
        self.balance_spinbox = QDoubleSpinBox()
        self.balance_spinbox.setRange(100, 100000)
        self.balance_spinbox.setValue(500)
        self.balance_spinbox.setSuffix(" USDT")
        config_layout.addWidget(self.balance_spinbox, 2, 1)
        
        config_layout.addWidget(QLabel("Leverage:"), 3, 0)
        self.leverage_spinbox = QSpinBox()
        self.leverage_spinbox.setRange(1, 100)
        self.leverage_spinbox.setValue(20)
        config_layout.addWidget(self.leverage_spinbox, 3, 1)
        
        config_layout.addWidget(QLabel("Days of Data:"), 4, 0)
        self.days_spinbox = QSpinBox()
        self.days_spinbox.setRange(1, 30)
        self.days_spinbox.setValue(3)  # Shorter for optimization
        config_layout.addWidget(self.days_spinbox, 4, 1)
        
        layout.addWidget(config_group)
        
        # Parameter ranges section
        params_group = QGroupBox("Parameter Ranges")
        params_layout = QVBoxLayout(params_group)
        
        # Add parameter configuration widgets
        self.param_widgets = {}
        
        # RSI thresholds
        rsi_layout = QHBoxLayout()
        rsi_layout.addWidget(QLabel("RSI Long Threshold:"))
        
        self.rsi_long_enabled = QCheckBox("Enable")
        self.rsi_long_enabled.setChecked(True)
        rsi_layout.addWidget(self.rsi_long_enabled)
        
        rsi_layout.addWidget(QLabel("Min:"))
        self.rsi_long_min = QSpinBox()
        self.rsi_long_min.setRange(30, 80)
        self.rsi_long_min.setValue(50)
        rsi_layout.addWidget(self.rsi_long_min)
        
        rsi_layout.addWidget(QLabel("Max:"))
        self.rsi_long_max = QSpinBox()
        self.rsi_long_max.setRange(30, 80)
        self.rsi_long_max.setValue(70)
        rsi_layout.addWidget(self.rsi_long_max)
        
        rsi_layout.addWidget(QLabel("Step:"))
        self.rsi_long_step = QSpinBox()
        self.rsi_long_step.setRange(1, 20)
        self.rsi_long_step.setValue(5)
        rsi_layout.addWidget(self.rsi_long_step)
        
        rsi_layout.addStretch()
        params_layout.addLayout(rsi_layout)
        
        # Volume surge threshold
        volume_layout = QHBoxLayout()
        volume_layout.addWidget(QLabel("Volume Surge Threshold:"))
        
        self.volume_enabled = QCheckBox("Enable")
        self.volume_enabled.setChecked(True)
        volume_layout.addWidget(self.volume_enabled)
        
        volume_layout.addWidget(QLabel("Min:"))
        self.volume_min = QSpinBox()
        self.volume_min.setRange(100, 300)
        self.volume_min.setValue(110)
        volume_layout.addWidget(self.volume_min)
        
        volume_layout.addWidget(QLabel("Max:"))
        self.volume_max = QSpinBox()
        self.volume_max.setRange(100, 300)
        self.volume_max.setValue(200)
        volume_layout.addWidget(self.volume_max)
        
        volume_layout.addWidget(QLabel("Step:"))
        self.volume_step = QSpinBox()
        self.volume_step.setRange(5, 50)
        self.volume_step.setValue(20)
        volume_layout.addWidget(self.volume_step)
        
        volume_layout.addStretch()
        params_layout.addLayout(volume_layout)
        
        # Stop loss percentage
        sl_layout = QHBoxLayout()
        sl_layout.addWidget(QLabel("Stop Loss %:"))
        
        self.sl_enabled = QCheckBox("Enable")
        self.sl_enabled.setChecked(True)
        sl_layout.addWidget(self.sl_enabled)
        
        sl_layout.addWidget(QLabel("Min:"))
        self.sl_min = QDoubleSpinBox()
        self.sl_min.setRange(0.1, 2.0)
        self.sl_min.setValue(0.3)
        self.sl_min.setSingleStep(0.1)
        sl_layout.addWidget(self.sl_min)
        
        sl_layout.addWidget(QLabel("Max:"))
        self.sl_max = QDoubleSpinBox()
        self.sl_max.setRange(0.1, 2.0)
        self.sl_max.setValue(1.0)
        self.sl_max.setSingleStep(0.1)
        sl_layout.addWidget(self.sl_max)
        
        sl_layout.addWidget(QLabel("Step:"))
        self.sl_step = QDoubleSpinBox()
        self.sl_step.setRange(0.1, 0.5)
        self.sl_step.setValue(0.2)
        self.sl_step.setSingleStep(0.1)
        sl_layout.addWidget(self.sl_step)
        
        sl_layout.addStretch()
        params_layout.addLayout(sl_layout)
        
        layout.addWidget(params_group)
        
        # Control buttons
        control_layout = QHBoxLayout()
        
        self.start_sweep_btn = QPushButton("🚀 Start Parameter Sweep")
        self.start_sweep_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.start_sweep_btn.clicked.connect(self.start_parameter_sweep)
        control_layout.addWidget(self.start_sweep_btn)
        
        self.stop_sweep_btn = QPushButton("⏹️ Stop Sweep")
        self.stop_sweep_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        self.stop_sweep_btn.clicked.connect(self.stop_parameter_sweep)
        self.stop_sweep_btn.setEnabled(False)
        control_layout.addWidget(self.stop_sweep_btn)
        
        self.export_results_btn = QPushButton("📊 Export Results")
        self.export_results_btn.clicked.connect(self.export_results)
        self.export_results_btn.setEnabled(False)
        control_layout.addWidget(self.export_results_btn)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("")
        self.progress_label.setVisible(False)
        layout.addWidget(self.progress_label)
        
        # Results table
        results_group = QGroupBox("Optimization Results")
        results_layout = QVBoxLayout(results_group)
        
        self.results_table = QTableWidget()
        results_layout.addWidget(self.results_table)
        
        layout.addWidget(results_group)
    
    def browse_strategy(self):
        """Browse for strategy file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Strategy File", "strategy_builder/strategies", 
            "YAML files (*.yaml *.yml);;All files (*)"
        )
        
        if file_path:
            self.strategy_path_label.setText(Path(file_path).name)
            self.strategy_path_label.setProperty("full_path", file_path)
    
    def start_parameter_sweep(self):
        """Start the parameter optimization sweep"""
        try:
            # Validate inputs
            strategy_path = self.strategy_path_label.property("full_path")
            if not strategy_path:
                QMessageBox.warning(self, "Warning", "Please select a strategy file first.")
                return
            
            # Generate parameter ranges
            parameter_ranges = self._generate_parameter_ranges()
            if not parameter_ranges:
                QMessageBox.warning(self, "Warning", "Please enable at least one parameter for optimization.")
                return
            
            # Calculate total combinations
            total_combinations = 1
            for param_range in parameter_ranges.values():
                total_combinations *= len(param_range)
            
            if total_combinations > 1000:
                reply = QMessageBox.question(
                    self, "Large Parameter Space", 
                    f"This will test {total_combinations} combinations. This may take a very long time. Continue?",
                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    return
            
            # Update UI
            self.start_sweep_btn.setEnabled(False)
            self.stop_sweep_btn.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.progress_label.setVisible(True)
            self.progress_bar.setValue(0)
            
            # Start worker
            self.sweep_worker = ParameterSweepWorker(
                strategy_path,
                self.symbol_combo.currentText(),
                parameter_ranges,
                self.balance_spinbox.value(),
                self.leverage_spinbox.value(),
                self.days_spinbox.value()
            )
            
            self.sweep_worker.finished.connect(self.on_sweep_finished)
            self.sweep_worker.error.connect(self.on_sweep_error)
            self.sweep_worker.progress.connect(self.on_sweep_progress)
            self.sweep_worker.start()
            
        except Exception as e:
            self.logger.error(f"Error starting parameter sweep: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to start parameter sweep: {str(e)}")
            self.reset_sweep_controls()
    
    def stop_parameter_sweep(self):
        """Stop the current parameter sweep"""
        if self.sweep_worker:
            self.sweep_worker.stop()
            self.sweep_worker.wait(5000)  # Wait up to 5 seconds
        self.reset_sweep_controls()
    
    def _generate_parameter_ranges(self) -> Dict[str, List]:
        """Generate parameter ranges based on UI settings"""
        ranges = {}
        
        # RSI Long threshold
        if self.rsi_long_enabled.isChecked():
            min_val = self.rsi_long_min.value()
            max_val = self.rsi_long_max.value()
            step = self.rsi_long_step.value()
            ranges['rsi_threshold_long'] = list(range(min_val, max_val + 1, step))
        
        # Volume surge threshold
        if self.volume_enabled.isChecked():
            min_val = self.volume_min.value()
            max_val = self.volume_max.value()
            step = self.volume_step.value()
            ranges['volume_surge_threshold'] = list(range(min_val, max_val + 1, step))
        
        # Stop loss percentage
        if self.sl_enabled.isChecked():
            min_val = self.sl_min.value()
            max_val = self.sl_max.value()
            step = self.sl_step.value()
            ranges['stop_loss_pct'] = [round(x, 2) for x in np.arange(min_val, max_val + step, step)]
        
        return ranges
    
    def on_sweep_progress(self, progress_pct: int, description: str):
        """Handle sweep progress updates"""
        self.progress_bar.setValue(progress_pct)
        self.progress_label.setText(description)
    
    def on_sweep_finished(self, results: List[Tuple]):
        """Handle sweep completion"""
        try:
            self.sweep_results = results
            self.display_results(results)
            self.export_results_btn.setEnabled(True)
            
            QMessageBox.information(self, "Success", 
                                  f"Parameter sweep completed! Tested {len(results)} combinations.")
            
        except Exception as e:
            self.logger.error(f"Error displaying sweep results: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error displaying results: {str(e)}")
        finally:
            self.reset_sweep_controls()
    
    def on_sweep_error(self, error_message: str):
        """Handle sweep errors"""
        QMessageBox.critical(self, "Sweep Error", f"Parameter sweep failed: {error_message}")
        self.reset_sweep_controls()
    
    def reset_sweep_controls(self):
        """Reset sweep controls to normal state"""
        self.start_sweep_btn.setEnabled(True)
        self.stop_sweep_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
    
    def display_results(self, results: List[Tuple]):
        """Display optimization results in table"""
        try:
            if not results:
                return
            
            # Sort results by total PnL (descending)
            sorted_results = sorted(results, key=lambda x: x[1].total_pnl, reverse=True)
            
            # Set up table
            self.results_table.setRowCount(len(sorted_results))
            
            # Get parameter names from first result
            param_names = list(sorted_results[0][0].keys())
            metric_names = ["Total PnL", "Win Rate", "Profit Factor", "Max DD", "Sharpe", "Trades"]
            
            headers = param_names + metric_names
            self.results_table.setColumnCount(len(headers))
            self.results_table.setHorizontalHeaderLabels(headers)
            
            # Fill table
            for i, (params, result) in enumerate(sorted_results):
                col = 0
                
                # Parameter values
                for param_name in param_names:
                    self.results_table.setItem(i, col, QTableWidgetItem(str(params[param_name])))
                    col += 1
                
                # Metrics
                metrics = [
                    f"${result.total_pnl:.2f}",
                    f"{result.win_rate:.1f}%",
                    f"{result.profit_factor:.2f}",
                    f"{result.max_drawdown_pct:.1f}%",
                    f"{result.sharpe_ratio:.2f}",
                    str(result.total_trades)
                ]
                
                for j, metric in enumerate(metrics):
                    item = QTableWidgetItem(metric)
                    
                    # Color coding for PnL
                    if j == 0:  # Total PnL column
                        if result.total_pnl > 0:
                            item.setBackground(QColor(200, 255, 200))
                        elif result.total_pnl < 0:
                            item.setBackground(QColor(255, 200, 200))
                    
                    self.results_table.setItem(i, col + j, item)
            
            # Resize columns
            self.results_table.resizeColumnsToContents()
            
        except Exception as e:
            self.logger.error(f"Error displaying results: {str(e)}")
    
    def export_results(self):
        """Export optimization results to CSV"""
        try:
            if not self.sweep_results:
                QMessageBox.warning(self, "Warning", "No results to export.")
                return
            
            # Get save location
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"parameter_sweep_{timestamp}.csv"
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export Results", default_filename, 
                "CSV files (*.csv);;All files (*)"
            )
            
            if not file_path:
                return
            
            # Sort results by total PnL
            sorted_results = sorted(self.sweep_results, key=lambda x: x[1].total_pnl, reverse=True)
            
            # Write CSV
            with open(file_path, 'w', newline='') as csvfile:
                # Get headers
                param_names = list(sorted_results[0][0].keys())
                metric_names = [
                    "total_pnl", "total_pnl_pct", "win_rate", "profit_factor", 
                    "max_drawdown_pct", "sharpe_ratio", "total_trades", "avg_trade_duration"
                ]
                
                headers = param_names + metric_names
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                
                # Write data
                for params, result in sorted_results:
                    row = []
                    
                    # Parameter values
                    for param_name in param_names:
                        row.append(params[param_name])
                    
                    # Metric values
                    metrics = [
                        result.total_pnl, result.total_pnl_pct, result.win_rate,
                        result.profit_factor, result.max_drawdown_pct, result.sharpe_ratio,
                        result.total_trades, result.avg_trade_duration
                    ]
                    row.extend(metrics)
                    
                    writer.writerow(row)
            
            QMessageBox.information(self, "Success", f"Results exported to {file_path}")
            
        except Exception as e:
            self.logger.error(f"Error exporting results: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")
    
    def refresh_data(self):
        """Refresh parameter sweeper data"""
        # Clear current results
        self.results_table.setRowCount(0)
        self.sweep_results = []
        self.export_results_btn.setEnabled(False)
