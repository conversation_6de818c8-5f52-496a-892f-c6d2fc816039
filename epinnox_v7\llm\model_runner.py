import os
import asyncio
import time
from typing import Dict, Optional
import requests
from utils.logger import get_logger, log_error
from pathlib import Path
from datetime import datetime

# Import PyTorch if available
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

class ModelRunner:
    """Manages local LLM inference through HTTP API"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # LLM API settings - with fallbacks
        llm_config = config.get('llm', {})
        self.api_url = llm_config.get('api_url', "http://localhost:1234/v1/completions")
        self.model_name = llm_config.get('model_name', config.get('llm_model', "Phi-3.1-mini-128k-instruct.Q4_K_M.gguf"))
        
        # Model parameters
        self.max_tokens = llm_config.get('max_tokens', 512)
        self.temperature = llm_config.get('temperature', 0.3)
        self.timeout = llm_config.get('timeout', 30)
        self.retry_attempts = llm_config.get('retry_attempts', 3)
        self.retry_delay = llm_config.get('retry_delay', 1)
        
        # Connection state
        self.api_available = False
        
        # Debug logging setup
        self.debug_mode = config.get('debug_mode', False)
        if self.debug_mode:
            log_dir = Path(config['logging']['log_dir'])
            log_dir.mkdir(parents=True, exist_ok=True)
            self.prompt_log = log_dir / "llm_prompts.log"
            self.response_log = log_dir / "llm_responses.log"
            self._file_lock = asyncio.Lock()
            
    async def initialize(self):
        """Verify API connection with retries"""
        for attempt in range(self.retry_attempts):
            try:
                # Test API availability
                response = requests.get(
                    self.api_url.replace('/completions', '/models'),
                    timeout=self.timeout
                )
                if response.status_code == 200:
                    self.api_available = True
                    self.logger.info(f"Successfully connected to LLM API at {self.api_url}")
                    return True
                else:
                    self.logger.warning(f"API returned status code {response.status_code}")
            except Exception as e:
                self.logger.warning(f"Failed to connect to API (attempt {attempt + 1}): {str(e)}")
                if attempt < self.retry_attempts - 1:
                    await asyncio.sleep(self.retry_delay)
                else:
                    raise ConnectionError(f"Failed to connect to LLM API after {self.retry_attempts} attempts")
        return False

    async def get_trading_decision(self, prompt: str) -> Optional[str]:
        """Get trading decision from the model"""
        if not self.api_available:
            raise RuntimeError("LLM API not initialized or unavailable")
            
        try:
            # Log prompt if in debug mode
            if self.debug_mode:
                await self._log_prompt(prompt)
                
            # Prepare the request
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "max_tokens": self.max_tokens,
                "temperature": self.temperature
            }
            
            response = requests.post(
                self.api_url,
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                model_output = result.get('choices', [{}])[0].get('text', '')
                
                # Log response if in debug mode
                if self.debug_mode:
                    await self._log_response(model_output)
                    
                return model_output
            else:
                error_msg = f"API request failed with status {response.status_code}"
                self.logger.error(error_msg)
                return None
                
        except Exception as e:
            self.logger.error(f"Error during model inference: {str(e)}")
            return None
                
    def _get_fallback_response(self, reason: str) -> str:
        """Generate a conservative fallback response when API fails"""
        fallback = {
            "action": "hold",
            "confidence": 0.5,
            "reason": f"Using fallback response: {reason}"
        }
        return str(fallback).replace("'", '"')

    async def close(self):
        """Clean up model resources"""
        try:
            # Clear CUDA cache if available
            if TORCH_AVAILABLE and hasattr(torch.cuda, 'is_available') and torch.cuda.is_available():
                torch.cuda.empty_cache()
            self.api_available = False
        except Exception as e:
            self.logger.error(f"Error in close: {str(e)}")
            
    async def _log_prompt(self, prompt: str):
        """Log prompt to file in debug mode"""
        async with self._file_lock:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            with open(self.prompt_log, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] {prompt}\n")
                
    async def _log_response(self, response: str):
        """Log model response to file in debug mode"""
        async with self._file_lock:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            with open(self.response_log, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] {response}\n")

    async def _log_debug(self, prompt: str, response: str):
        """Log prompt and response in debug mode"""
        if not self.debug_mode:
            return
            
        try:
            async with self._file_lock:
                timestamp = datetime.now().isoformat()
                
                with open(self.prompt_log, 'a') as f:
                    f.write(f"\n=== {timestamp} ===\n{prompt}\n")
                    
                with open(self.response_log, 'a') as f:
                    f.write(f"\n=== {timestamp} ===\n{response}\n")
                    
        except Exception as e:
            self.logger.error(f"Failed to log debug info: {str(e)}")
