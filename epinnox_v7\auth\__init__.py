"""
Authentication Module for Epinnox v7

Provides secure user authentication, session management, and role-based access control
for the trading system.
"""

from .authentication import Authentication<PERSON>ana<PERSON>, UserRole, SessionManager
from .decorators import require_auth, require_role
from .models import User, UserSession

__all__ = [
    'AuthenticationManager', 
    'UserRole', 
    'SessionManager',
    'require_auth', 
    'require_role',
    'User', 
    'UserSession'
]
