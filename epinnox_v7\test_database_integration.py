#!/usr/bin/env python3
"""
Test Database Integration with GUI Components

This script tests the database integration layer and demonstrates
how GUI components can use persistent storage.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timezone, timedelta
import json

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from utils.config_validator import ConfigValidator
from gui.database_integration import DatabaseIntegration
from utils.logger import get_logger

logger = get_logger()


async def test_backtest_integration():
    """Test backtest result storage and retrieval"""
    print("🧪 Testing Backtest Integration...")
    
    try:
        config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
        db_integration = DatabaseIntegration(config)
        
        # Create sample backtest result
        backtest_data = {
            'strategy_name': 'Advanced Momentum Strategy',
            'symbol': 'BTC/USDT:USDT',
            'initial_balance': 1000.0,
            'leverage': 10,
            'timeframe': '1m',
            'start_date': datetime.now(timezone.utc) - timedelta(days=1),
            'end_date': datetime.now(timezone.utc),
            'strategy_config': {
                'rsi_long_threshold': 25,
                'rsi_short_threshold': 75,
                'volume_surge_threshold': 200,
                'stop_loss_pct': 1.0,
                'take_profit_pct': 2.0
            },
            'total_trades': 45,
            'win_rate': 73.3,
            'total_pnl': 156.75,
            'total_pnl_pct': 15.68,
            'max_drawdown_pct': 3.2,
            'sharpe_ratio': 2.15,
            'trade_list': [
                {
                    'entry_time': '2025-06-14T10:00:00Z',
                    'exit_time': '2025-06-14T10:05:00Z',
                    'side': 'LONG',
                    'entry_price': 67500.0,
                    'exit_price': 67850.0,
                    'pnl': 35.0
                },
                {
                    'entry_time': '2025-06-14T11:30:00Z',
                    'exit_time': '2025-06-14T11:37:00Z',
                    'side': 'SHORT',
                    'entry_price': 67200.0,
                    'exit_price': 66950.0,
                    'pnl': 25.0
                }
            ],
            'equity_curve': [1000, 1035, 1060, 1045, 1080, 1156.75],
            'performance_metrics': {
                'profit_factor': 2.8,
                'avg_win': 15.2,
                'avg_loss': -8.5,
                'max_consecutive_wins': 7,
                'max_consecutive_losses': 3
            },
            'performance_grade': 'A-',
            'recommendations': [
                'Consider reducing position size during high volatility',
                'Optimize stop loss levels for better risk management'
            ],
            'execution_time_seconds': 25.4,
            'data_points_processed': 1440
        }
        
        # Save backtest result
        print("  ├─ Saving backtest result...")
        backtest_id = db_integration.save_backtest_result(backtest_data)
        print(f"    ✅ Backtest saved with ID: {backtest_id}")
        
        # Retrieve recent backtests
        print("  ├─ Retrieving recent backtests...")
        recent_backtests = db_integration.get_recent_backtests(days=7, limit=10)
        print(f"    📊 Found {len(recent_backtests)} recent backtests")
        
        # Get backtests by strategy
        print("  ├─ Getting backtests by strategy...")
        strategy_backtests = db_integration.get_backtests_by_strategy('Advanced Momentum Strategy')
        print(f"    📈 Found {len(strategy_backtests)} backtests for strategy")
        
        # Get best backtests
        print("  ├─ Getting best performing backtests...")
        best_backtests = db_integration.get_best_backtests(metric='total_pnl_pct', limit=5)
        print(f"    🏆 Found {len(best_backtests)} top performing backtests")
        
        # Get summary
        print("  └─ Getting backtest summary...")
        summary = db_integration.get_backtest_summary()
        print(f"    📋 Summary: {summary}")
        
        print("✅ Backtest integration test completed")
        return True
        
    except Exception as e:
        print(f"❌ Backtest integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_llm_integration():
    """Test LLM prediction storage and retrieval"""
    print("\n🤖 Testing LLM Integration...")
    
    try:
        config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
        db_integration = DatabaseIntegration(config)
        
        # Create sample LLM predictions
        predictions = [
            {
                'symbol': 'DOGE/USDT:USDT',
                'timestamp': datetime.now(timezone.utc) - timedelta(minutes=30),
                'current_price': 0.08,
                'market_data': {
                    'volume': 1500000,
                    'volatility': 0.018,
                    'spread': 0.0001,
                    'rsi': 65,
                    'macd': 0.0005
                },
                'predicted_action': 'LONG',
                'confidence': 0.82,
                'reasoning': 'Strong bullish momentum with RSI approaching overbought but volume confirming trend',
                'entry_price': 0.08,
                'stop_loss': 0.0785,
                'take_profit': 0.0825,
                'position_size': 75.0,
                'actual_price_5m': 0.0815,  # Prediction was correct
                'prediction_correct_5m': True
            },
            {
                'symbol': 'DOGE/USDT:USDT',
                'timestamp': datetime.now(timezone.utc) - timedelta(minutes=15),
                'current_price': 0.0815,
                'market_data': {
                    'volume': 800000,
                    'volatility': 0.012,
                    'spread': 0.0001,
                    'rsi': 45,
                    'macd': -0.0002
                },
                'predicted_action': 'HOLD',
                'confidence': 0.65,
                'reasoning': 'Mixed signals, RSI neutral but MACD showing bearish divergence',
                'entry_price': 0.0815,
                'stop_loss': None,
                'take_profit': None,
                'position_size': 0.0,
                'actual_price_5m': 0.0812,  # Small move, HOLD was appropriate
                'prediction_correct_5m': True
            },
            {
                'symbol': 'BTC/USDT:USDT',
                'timestamp': datetime.now(timezone.utc) - timedelta(minutes=10),
                'current_price': 67500.0,
                'market_data': {
                    'volume': 25000000,
                    'volatility': 0.025,
                    'spread': 0.5,
                    'rsi': 25,
                    'macd': 0.015
                },
                'predicted_action': 'LONG',
                'confidence': 0.91,
                'reasoning': 'Oversold RSI with strong MACD bullish crossover, high volume confirmation',
                'entry_price': 67500.0,
                'stop_loss': 66800.0,
                'take_profit': 68500.0,
                'position_size': 100.0,
                'actual_price_5m': 68200.0,  # Strong move up, prediction correct
                'prediction_correct_5m': True
            }
        ]
        
        # Save predictions
        print("  ├─ Saving LLM predictions...")
        prediction_ids = []
        for pred_data in predictions:
            pred_id = db_integration.save_llm_prediction(pred_data)
            prediction_ids.append(pred_id)
        print(f"    ✅ Saved {len(prediction_ids)} predictions")
        
        # Retrieve predictions
        print("  ├─ Retrieving LLM predictions...")
        doge_predictions = db_integration.get_llm_predictions(symbol='DOGE/USDT:USDT')
        print(f"    📊 Found {len(doge_predictions)} DOGE predictions")
        
        all_predictions = db_integration.get_llm_predictions(limit=50)
        print(f"    📊 Found {len(all_predictions)} total predictions")
        
        # Get accuracy stats
        print("  ├─ Getting accuracy statistics...")
        doge_accuracy = db_integration.get_llm_accuracy_stats(symbol='DOGE/USDT:USDT', days=1)
        print(f"    🎯 DOGE accuracy: {doge_accuracy}")
        
        overall_accuracy = db_integration.get_llm_accuracy_stats(days=1)
        print(f"    🎯 Overall accuracy: {overall_accuracy}")
        
        print("✅ LLM integration test completed")
        return True
        
    except Exception as e:
        print(f"❌ LLM integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_trade_integration():
    """Test trade history storage and retrieval"""
    print("\n💰 Testing Trade Integration...")
    
    try:
        config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
        db_integration = DatabaseIntegration(config)
        
        # Create sample trades
        trades = [
            {
                'strategy_name': 'Advanced Momentum Strategy',
                'symbol': 'DOGE/USDT:USDT',
                'side': 'LONG',
                'entry_price': 0.08,
                'exit_price': 0.0825,
                'quantity': 1000,
                'leverage': 20,
                'entry_time': datetime.now(timezone.utc) - timedelta(hours=2),
                'exit_time': datetime.now(timezone.utc) - timedelta(hours=2) + timedelta(minutes=8),
                'duration_seconds': 480,
                'pnl_usd': 50.0,
                'pnl_pct': 3.125,
                'fees_usd': 1.2,
                'stop_loss': 0.078,
                'take_profit': 0.085,
                'market_regime': 'trending',
                'llm_confidence': 0.82,
                'llm_reasoning': 'Strong bullish momentum confirmed'
            },
            {
                'strategy_name': 'Advanced Momentum Strategy',
                'symbol': 'BTC/USDT:USDT',
                'side': 'SHORT',
                'entry_price': 67500.0,
                'exit_price': 67200.0,
                'quantity': 0.1,
                'leverage': 10,
                'entry_time': datetime.now(timezone.utc) - timedelta(hours=1),
                'exit_time': datetime.now(timezone.utc) - timedelta(hours=1) + timedelta(minutes=12),
                'duration_seconds': 720,
                'pnl_usd': 30.0,
                'pnl_pct': 0.44,
                'fees_usd': 2.5,
                'stop_loss': 67800.0,
                'take_profit': 67000.0,
                'market_regime': 'ranging',
                'llm_confidence': 0.75,
                'llm_reasoning': 'Resistance level rejection with volume confirmation'
            }
        ]
        
        # Save trades
        print("  ├─ Saving trade records...")
        trade_ids = []
        for trade_data in trades:
            trade_id = db_integration.save_trade(trade_data)
            trade_ids.append(trade_id)
        print(f"    ✅ Saved {len(trade_ids)} trades")
        
        # Get recent trades
        print("  ├─ Retrieving recent trades...")
        recent_trades = db_integration.get_recent_trades(hours=24, limit=20)
        print(f"    📊 Found {len(recent_trades)} recent trades")
        
        # Get performance summary
        print("  ├─ Getting performance summary...")
        overall_performance = db_integration.get_performance_summary(days=7)
        print(f"    📈 Overall performance: {overall_performance}")
        
        strategy_performance = db_integration.get_performance_summary(
            strategy_name='Advanced Momentum Strategy', days=7
        )
        print(f"    📈 Strategy performance: {strategy_performance}")
        
        print("✅ Trade integration test completed")
        return True
        
    except Exception as e:
        print(f"❌ Trade integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_user_preferences():
    """Test user preferences storage"""
    print("\n👤 Testing User Preferences...")
    
    try:
        config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
        db_integration = DatabaseIntegration(config)
        
        user_id = "test_user_001"
        
        # Set various preferences
        print("  ├─ Setting user preferences...")
        preferences = {
            'default_symbol': 'DOGE/USDT:USDT',
            'default_timeframe': '1m',
            'chart_theme': 'dark',
            'auto_refresh_interval': 30,
            'enable_notifications': True,
            'risk_tolerance': 0.02,
            'favorite_strategies': ['Advanced Momentum Strategy', 'Simple RSI Strategy'],
            'dashboard_layout': {
                'strategy_tab_visible': True,
                'llm_tab_visible': True,
                'optimization_tab_visible': False
            }
        }
        
        for key, value in preferences.items():
            success = db_integration.set_user_preference(user_id, key, value)
            if success:
                print(f"    ✅ Set {key}: {value}")
            else:
                print(f"    ❌ Failed to set {key}")
        
        # Retrieve preferences
        print("  ├─ Retrieving user preferences...")
        for key in preferences.keys():
            retrieved_value = db_integration.get_user_preference(user_id, key)
            print(f"    📋 {key}: {retrieved_value}")
        
        # Test default values
        print("  └─ Testing default values...")
        non_existent = db_integration.get_user_preference(user_id, 'non_existent_key', 'default_value')
        print(f"    📋 Non-existent key: {non_existent}")
        
        print("✅ User preferences test completed")
        return True
        
    except Exception as e:
        print(f"❌ User preferences test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_database_management():
    """Test database management features"""
    print("\n🗄️  Testing Database Management...")
    
    try:
        config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
        db_integration = DatabaseIntegration(config)
        
        # Health check
        print("  ├─ Performing health check...")
        health = db_integration.health_check()
        print(f"    🏥 Health status: {health['status']}")
        
        # Database statistics
        print("  ├─ Getting database statistics...")
        stats = db_integration.get_database_stats()
        print(f"    📊 Table counts: {stats.get('table_counts', {})}")
        print(f"    📊 Recent activity: {stats.get('recent_activity', {})}")
        
        # Backup (optional - only if enabled)
        print("  └─ Testing backup functionality...")
        try:
            backup_path = db_integration.backup_database()
            print(f"    💾 Backup created: {backup_path}")
        except Exception as e:
            print(f"    ⚠️  Backup test skipped: {e}")
        
        print("✅ Database management test completed")
        return True
        
    except Exception as e:
        print(f"❌ Database management test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all database integration tests"""
    print("🚀 Epinnox v7 Database Integration Testing")
    print("=" * 50)
    
    tests = [
        test_backtest_integration,
        test_llm_integration,
        test_trade_integration,
        test_user_preferences,
        test_database_management
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if await test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📋 TEST RESULTS: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Database integration is ready for production.")
        print("\n💡 Next Steps:")
        print("  1. ✅ Update GUI components to use database integration")
        print("  2. 📊 Enable persistent data storage in backtester")
        print("  3. 🤖 Connect LLM simulation to database")
        print("  4. 📈 Implement advanced analytics features")
    else:
        print("⚠️  Some tests failed. Please review and fix issues before proceeding.")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
