"""
LLM Trading Engine for Epinnox v7

Autonomous LLM-powered trading decision system with real market analysis.
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from utils.logger import get_logger
from exchanges.exchange_manager import get_exchange_manager
from core.risk_guard import RiskGuard

logger = get_logger()


class TradingSignal(Enum):
    """Trading signals"""
    STRONG_BUY = "strong_buy"
    BUY = "buy"
    HOLD = "hold"
    SELL = "sell"
    STRONG_SELL = "strong_sell"


@dataclass
class MarketAnalysis:
    """Market analysis result"""
    symbol: str
    timestamp: datetime
    signal: TradingSignal
    confidence: float
    reasoning: str
    technical_indicators: Dict[str, float]
    risk_assessment: Dict[str, float]
    suggested_position_size: float
    stop_loss: Optional[float]
    take_profit: Optional[float]


@dataclass
class TradingDecision:
    """Trading decision with execution parameters"""
    symbol: str
    action: str  # 'buy', 'sell', 'hold', 'close'
    quantity: float
    price_type: str  # 'market', 'limit'
    limit_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    confidence: float
    reasoning: str
    risk_score: float


class LLMTradingEngine:
    """LLM-powered autonomous trading engine"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger()
        
        # Trading parameters
        self.trading_active = False
        self.confidence_threshold = config.get('llm_confidence_threshold', 0.65)
        self.max_position_size_pct = config.get('max_position_size_pct', 2.0)
        self.max_concurrent_positions = config.get('max_concurrent_positions', 3)
        
        # Risk management
        self.risk_guard = RiskGuard(config)
        
        # Exchange manager
        self.exchange_manager = get_exchange_manager(config)
        
        # Market data cache
        self.market_data_cache = {}
        self.analysis_cache = {}
        
        # Trading history
        self.trading_decisions = []
        self.execution_results = []
        
        # Performance tracking
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0
        }
        
        self.logger.info("LLM Trading Engine initialized")
    
    async def start_trading(self) -> bool:
        """Start autonomous trading"""
        try:
            # Verify exchange connections
            if not self.exchange_manager.is_exchange_ready():
                raise Exception("Exchange not ready for trading")
            
            # Verify risk guard is active
            if not self.risk_guard:
                raise Exception("Risk guard not initialized")
            
            self.trading_active = True
            self.logger.info("LLM Trading Engine started")
            
            # Start trading loop
            asyncio.create_task(self.trading_loop())
            
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to start trading: {str(e)}")
            return False
    
    async def stop_trading(self):
        """Stop autonomous trading"""
        self.trading_active = False
        self.logger.info("LLM Trading Engine stopped")
    
    async def trading_loop(self):
        """Main trading loop with enhanced monitoring"""
        loop_count = 0

        print("🔄 LLM TRADING LOOP STARTED")
        print(f"🎯 Confidence threshold: {self.confidence_threshold}")
        print("="*60)

        while self.trading_active:
            loop_count += 1
            try:
                print(f"\n🔄 Trading Loop #{loop_count} - {datetime.now().strftime('%H:%M:%S')}")

                # Get trading symbols
                symbols = self.config.get('trading_symbols', ['BTC/USDT', 'ETH/USDT'])
                print(f"📊 Monitoring symbols: {symbols}")

                for symbol in symbols:
                    if not self.trading_active:
                        break

                    print(f"📈 Analyzing {symbol}...")

                    # Check circuit breakers
                    market_data = await self.get_market_data(symbol)
                    allowed, reason = self.risk_guard.check_circuit_breakers(market_data)

                    if not allowed:
                        print(f"⚠️ Trading halted for {symbol}: {reason}")
                        self.logger.warning(f"Trading halted for {symbol}: {reason}")
                        continue

                    # Analyze market with LLM
                    print(f"🧠 Running LLM analysis for {symbol}...")
                    analysis = await self.analyze_market(symbol)

                    if analysis:
                        print(f"🎯 LLM Analysis Complete:")
                        print(f"   Signal: {analysis.signal}")
                        print(f"   Confidence: {analysis.confidence:.2%}")
                        print(f"   Reasoning: {analysis.reasoning[:100]}...")

                        if analysis.confidence >= self.confidence_threshold:
                            print(f"✅ Confidence threshold met ({analysis.confidence:.2%} >= {self.confidence_threshold:.2%})")

                            # Make trading decision
                            decision = await self.make_trading_decision(analysis)

                            if decision and decision.action != 'hold':
                                print(f"🚀 Executing {decision.action} trade for {symbol}")
                                # Execute trade
                                await self.execute_trade(decision)
                            else:
                                print(f"⏸️ Decision: HOLD for {symbol}")
                        else:
                            print(f"❌ Confidence too low ({analysis.confidence:.2%} < {self.confidence_threshold:.2%})")
                    else:
                        print(f"❌ LLM analysis failed for {symbol}")

                # Wait before next iteration
                interval = self.config.get('trading_interval_seconds', 30)
                print(f"⏱️ Waiting {interval}s before next analysis cycle...")
                await asyncio.sleep(interval)
            
            except Exception as e:
                self.logger.error(f"Error in trading loop: {str(e)}")
                await asyncio.sleep(10)  # Wait before retrying
    
    async def analyze_market(self, symbol: str) -> Optional[MarketAnalysis]:
        """Analyze market using LLM"""
        try:
            # Get market data
            market_data = await self.get_market_data(symbol)
            
            # Get historical data
            historical_data = await self.get_historical_data(symbol)
            
            # Calculate technical indicators
            technical_indicators = self.calculate_technical_indicators(historical_data)
            
            # Prepare market context for LLM
            market_context = {
                'symbol': symbol,
                'current_price': market_data.get('price', 0),
                'bid': market_data.get('bid', 0),
                'ask': market_data.get('ask', 0),
                'volume': market_data.get('volume', 0),
                'change_24h': market_data.get('change_24h', 0),
                'technical_indicators': technical_indicators,
                'timestamp': datetime.now().isoformat()
            }
            
            # Get LLM analysis
            llm_response = await self.get_llm_analysis(market_context)
            
            if llm_response:
                return MarketAnalysis(
                    symbol=symbol,
                    timestamp=datetime.now(),
                    signal=TradingSignal(llm_response.get('signal', 'hold')),
                    confidence=llm_response.get('confidence', 0.0),
                    reasoning=llm_response.get('reasoning', ''),
                    technical_indicators=technical_indicators,
                    risk_assessment=llm_response.get('risk_assessment', {}),
                    suggested_position_size=llm_response.get('position_size', 0.0),
                    stop_loss=llm_response.get('stop_loss'),
                    take_profit=llm_response.get('take_profit')
                )
            
            return None
        
        except Exception as e:
            self.logger.error(f"Error analyzing market for {symbol}: {str(e)}")
            return None
    
    async def get_llm_analysis(self, market_context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get analysis from LLM with detailed monitoring"""
        try:
            print("🧠 LLM INFERENCE STARTING...")
            print(f"   📊 Processing market data for {market_context['symbol']}")
            print(f"   💰 Current price: ${market_context['current_price']:.2f}")
            print(f"   📈 24h change: {market_context['change_24h']:+.2f}%")
            print(f"   📊 Volume: {market_context['volume']:,.0f}")

            # Simulate GPU processing time
            print("   ⚡ GPU processing market patterns...")
            await asyncio.sleep(0.5)  # Simulate inference time

            # This is a simplified simulation of LLM analysis
            # In production, this would call your actual LLM service

            price = market_context['current_price']
            volume = market_context['volume']
            change_24h = market_context['change_24h']

            print("   🔍 Analyzing technical indicators...")
            await asyncio.sleep(0.3)
            
            print("   🤖 LLM reasoning through market conditions...")
            await asyncio.sleep(0.4)  # Simulate reasoning time

            # Simulate LLM decision making based on simple rules
            # Replace this with actual LLM API calls

            confidence = 0.0
            signal = 'hold'
            reasoning = "Market analysis in progress"

            print("   💭 LLM generating trading insights...")
            
            # Simple trend analysis simulation
            if change_24h > 2.0 and volume > 1000000:
                signal = 'buy'
                confidence = 0.75
                reasoning = "Strong upward trend with high volume"
            elif change_24h < -2.0 and volume > 1000000:
                signal = 'sell'
                confidence = 0.70
                reasoning = "Strong downward trend with high volume"
            elif abs(change_24h) < 0.5:
                signal = 'hold'
                confidence = 0.60
                reasoning = "Sideways market, waiting for clear direction"
            else:
                signal = 'hold'
                confidence = 0.50
                reasoning = "Unclear market direction"
            
            # Risk assessment
            risk_score = min(abs(change_24h) / 10.0, 1.0)
            
            # Position sizing based on confidence and risk
            max_position = self.max_position_size_pct / 100.0
            position_size = max_position * confidence * (1 - risk_score)

            print(f"   ✅ LLM ANALYSIS COMPLETE!")
            print(f"   📊 Signal: {signal.upper()}")
            print(f"   🎯 Confidence: {confidence:.1%}")
            print(f"   ⚖️ Risk Score: {risk_score:.1%}")
            print(f"   💰 Position Size: {position_size:.1%}")
            print(f"   🧠 Reasoning: {reasoning}")

            return {
                'signal': signal,
                'confidence': confidence,
                'reasoning': reasoning,
                'risk_assessment': {
                    'volatility_risk': risk_score,
                    'liquidity_risk': 0.1,
                    'overall_risk': risk_score
                },
                'position_size': position_size,
                'stop_loss': price * 0.98 if signal == 'buy' else price * 1.02 if signal == 'sell' else None,
                'take_profit': price * 1.04 if signal == 'buy' else price * 0.96 if signal == 'sell' else None
            }
        
        except Exception as e:
            self.logger.error(f"Error getting LLM analysis: {str(e)}")
            return None
    
    async def make_trading_decision(self, analysis: MarketAnalysis) -> Optional[TradingDecision]:
        """Make trading decision based on analysis"""
        try:
            # Check if we should trade this signal
            if analysis.confidence < self.confidence_threshold:
                return None
            
            # Get current positions
            positions = await self.exchange_manager.get_positions()
            current_positions = len(positions)
            
            # Check position limits
            if current_positions >= self.max_concurrent_positions and analysis.signal in [TradingSignal.BUY, TradingSignal.STRONG_BUY]:
                self.logger.info(f"Maximum concurrent positions reached: {current_positions}")
                return None
            
            # Determine action
            action = 'hold'
            if analysis.signal in [TradingSignal.BUY, TradingSignal.STRONG_BUY]:
                action = 'buy'
            elif analysis.signal in [TradingSignal.SELL, TradingSignal.STRONG_SELL]:
                action = 'sell'
            
            if action == 'hold':
                return None
            
            # Calculate position size
            account_balance = await self.get_account_balance()
            position_value = account_balance * analysis.suggested_position_size
            
            # Get current price
            market_data = await self.get_market_data(analysis.symbol)
            current_price = market_data.get('price', 0)
            
            if current_price <= 0:
                return None
            
            quantity = position_value / current_price
            
            # Risk assessment
            risk_score = analysis.risk_assessment.get('overall_risk', 0.5)
            
            return TradingDecision(
                symbol=analysis.symbol,
                action=action,
                quantity=quantity,
                price_type='market',  # Use market orders for now
                limit_price=None,
                stop_loss=analysis.stop_loss,
                take_profit=analysis.take_profit,
                confidence=analysis.confidence,
                reasoning=analysis.reasoning,
                risk_score=risk_score
            )
        
        except Exception as e:
            self.logger.error(f"Error making trading decision: {str(e)}")
            return None
    
    async def execute_trade(self, decision: TradingDecision) -> bool:
        """Execute trading decision"""
        try:
            # Final risk check
            if not self.risk_guard.can_place_trade(decision.symbol, decision.quantity, decision.action):
                self.logger.warning(f"Trade blocked by risk guard: {decision.symbol}")
                return False
            
            # Place order
            order = await self.exchange_manager.place_order(
                symbol=decision.symbol,
                side=decision.action,
                type=decision.price_type,
                amount=decision.quantity,
                price=decision.limit_price
            )
            
            if order:
                # Record decision and execution
                self.trading_decisions.append(decision)
                self.execution_results.append({
                    'decision': decision,
                    'order': order,
                    'timestamp': datetime.now(),
                    'status': 'executed'
                })
                
                # Update performance metrics
                self.performance_metrics['total_trades'] += 1
                
                self.logger.info(f"Trade executed: {decision.action} {decision.quantity} {decision.symbol} at {order.price}")
                
                return True
            else:
                self.logger.error(f"Failed to execute trade: {decision}")
                return False
        
        except Exception as e:
            self.logger.error(f"Error executing trade: {str(e)}")
            return False
    
    async def get_market_data(self, symbol: str) -> Dict[str, Any]:
        """Get current market data"""
        try:
            market_data = await self.exchange_manager.get_market_data(symbol)
            
            # Convert to dict format
            return {
                'symbol': market_data.symbol,
                'price': market_data.last,
                'bid': market_data.bid,
                'ask': market_data.ask,
                'volume': market_data.volume,
                'change_24h': market_data.change_24h,
                'change_pct_24h': market_data.change_pct_24h,
                'timestamp': market_data.timestamp
            }
        
        except Exception as e:
            self.logger.error(f"Error getting market data for {symbol}: {str(e)}")
            return {}
    
    async def get_historical_data(self, symbol: str, limit: int = 100) -> List[List[float]]:
        """Get historical price data"""
        try:
            exchange = self.exchange_manager.get_exchange()
            if exchange:
                return await exchange.get_klines(symbol, '1h', limit)
            return []
        
        except Exception as e:
            self.logger.error(f"Error getting historical data for {symbol}: {str(e)}")
            return []
    
    def calculate_technical_indicators(self, historical_data: List[List[float]]) -> Dict[str, float]:
        """Calculate technical indicators"""
        try:
            if not historical_data or len(historical_data) < 20:
                return {}
            
            # Extract closing prices
            closes = [float(candle[4]) for candle in historical_data]
            
            # Simple Moving Average (20 periods)
            sma_20 = sum(closes[-20:]) / 20
            
            # Current price
            current_price = closes[-1]
            
            # Price relative to SMA
            price_vs_sma = (current_price - sma_20) / sma_20 * 100
            
            # Simple volatility (standard deviation of last 20 closes)
            avg_price = sum(closes[-20:]) / 20
            variance = sum((price - avg_price) ** 2 for price in closes[-20:]) / 20
            volatility = (variance ** 0.5) / avg_price * 100
            
            return {
                'sma_20': sma_20,
                'current_price': current_price,
                'price_vs_sma_pct': price_vs_sma,
                'volatility_pct': volatility,
                'trend_strength': abs(price_vs_sma)
            }
        
        except Exception as e:
            self.logger.error(f"Error calculating technical indicators: {str(e)}")
            return {}
    
    async def get_account_balance(self) -> float:
        """Get account balance in base currency"""
        try:
            balances = await self.exchange_manager.get_account_balance()
            
            # Find USDT balance (or main trading currency)
            for balance in balances:
                if balance.currency == 'USDT':
                    return balance.free
            
            return 0.0
        
        except Exception as e:
            self.logger.error(f"Error getting account balance: {str(e)}")
            return 0.0
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return {
            **self.performance_metrics,
            'active_decisions': len(self.trading_decisions),
            'executed_trades': len(self.execution_results),
            'last_trade_time': self.execution_results[-1]['timestamp'].isoformat() if self.execution_results else None,
            'trading_active': self.trading_active
        }
    
    async def emergency_stop(self) -> Dict[str, Any]:
        """Emergency stop all trading"""
        try:
            # Stop trading loop
            self.trading_active = False
            
            # Cancel all orders and close positions
            emergency_results = await self.exchange_manager.emergency_stop_all_trading()
            
            self.logger.critical("Emergency stop executed by LLM Trading Engine")
            
            return {
                'status': 'emergency_stopped',
                'timestamp': datetime.now().isoformat(),
                'exchange_results': emergency_results
            }
        
        except Exception as e:
            self.logger.error(f"Error during emergency stop: {str(e)}")
            return {
                'status': 'emergency_stop_failed',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }


# Global trading engine instance
_trading_engine: Optional[LLMTradingEngine] = None


def get_trading_engine(config: Dict[str, Any] = None) -> LLMTradingEngine:
    """Get global trading engine instance"""
    global _trading_engine
    
    if _trading_engine is None and config is not None:
        _trading_engine = LLMTradingEngine(config)
    
    return _trading_engine
