import sys
import re
from datetime import datetime, timedelta
from pathlib import Path
import os
from loguru import logger
import yaml

def setup_logger(config_input):
    """
    Configure loguru logger with settings from config
    
    Parameters
    ----------
    config_input : Union[str, dict]
        Either a path to config file or config dictionary
    """
    # Get config dict
    if isinstance(config_input, str):
        with open(config_input, 'r') as f:
            config = yaml.safe_load(f)
    else:
        config = config_input
    
    log_config = config.get('logging', {})
    
    # Get log settings with defaults
    log_file = log_config.get('file', 'logs/scalper.log')
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Remove default logger
    logger.remove()
    
    # Add file logger with rotation
    logger.add(
        log_file,
        rotation=f"{log_config.get('max_size', 100)} MB",
        retention=log_config.get('backup_count', 5),
        level=log_config.get('level', 'INFO'),
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # Add stdout logger for console output with more visible format
    logger.add(
        sys.stderr,
        colorize=True,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <white>{message}</white>"
    )
    
    logger.info("Logger initialized")
    return logger

def get_logger():
    """Get the configured logger instance"""
    return logger

# Note: Convenience functions moved below redact_sensitive_data function

def redact_sensitive_data(message: str) -> str:
    """Redact sensitive information from log messages"""
    # Patterns for sensitive data
    patterns = [
        # API keys (various formats)
        (r'["\']?api[_-]?key["\']?\s*[:=]\s*["\']?([a-zA-Z0-9]{20,})["\']?', r'api_key: [REDACTED]'),
        (r'["\']?secret["\']?\s*[:=]\s*["\']?([a-zA-Z0-9]{20,})["\']?', r'secret: [REDACTED]'),
        (r'["\']?password["\']?\s*[:=]\s*["\']?([^\s"\']{8,})["\']?', r'password: [REDACTED]'),
        (r'["\']?token["\']?\s*[:=]\s*["\']?([a-zA-Z0-9]{20,})["\']?', r'token: [REDACTED]'),

        # Common API key patterns
        (r'sk-[a-zA-Z0-9]{32,}', '[REDACTED_API_KEY]'),
        (r'pk-[a-zA-Z0-9]{32,}', '[REDACTED_PUBLIC_KEY]'),

        # Email addresses (partial redaction)
        (r'([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', r'\1***@\2'),

        # Credit card numbers
        (r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b', '[REDACTED_CARD]'),

        # IP addresses (partial redaction)
        (r'\b(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})\b', r'\1.\2.***.\4'),
    ]

    redacted_message = message
    for pattern, replacement in patterns:
        redacted_message = re.sub(pattern, replacement, redacted_message, flags=re.IGNORECASE)

    return redacted_message

def log_error(msg: str, exc_info=True):
    """Log errors in red with full traceback"""
    redacted_msg = redact_sensitive_data(msg)
    logger.exception(f"❌ {redacted_msg}") if exc_info else logger.error(f"❌ {redacted_msg}")

def log_trade(msg: str):
    """Log trade-related messages in blue"""
    redacted_msg = redact_sensitive_data(msg)
    logger.info(f"💰 {redacted_msg}")

def log_signal(msg: str):
    """Log signal/strategy messages in yellow"""
    redacted_msg = redact_sensitive_data(msg)
    logger.info(f"🎯 {redacted_msg}")

def log_risk(msg: str):
    """Log risk-related messages in red"""
    redacted_msg = redact_sensitive_data(msg)
    logger.warning(f"⚠️ {redacted_msg}")

def log_performance(msg: str):
    """Log performance metrics in green"""
    redacted_msg = redact_sensitive_data(msg)
    logger.info(f"📈 {redacted_msg}")

def prune_logs(log_dir='logs', retention_days=7):
    """
    Prune log files older than retention period
    
    Parameters
    ----------
    log_dir : str
        Directory where log files are stored
    retention_days : int
        Number of days to retain logs
    """
    cutoff_date = datetime.now() - timedelta(days=retention_days)
    for log_file in Path(log_dir).rglob('*.log'):
        if datetime.fromtimestamp(log_file.stat().st_mtime) < cutoff_date:
            os.remove(log_file)
            logger.info(f"Removed old log file: {log_file}")

def prune_old_logs(config, max_age_days=7):
    """
    Delete log files older than specified number of days
    
    Parameters
    ----------
    config : dict
        Config dictionary containing logging paths
    max_age_days : int
        Maximum age of log files in days
    """
    try:
        log_dir = Path(config['logging']['log_dir'])
        if not log_dir.exists():
            return
            
        # Calculate cutoff date
        cutoff_date = datetime.now() - timedelta(days=max_age_days)
        
        # Find and delete old log files
        deleted_count = 0
        for log_file in log_dir.glob('*.log*'):
            # Skip the current main log file
            if log_file.name == 'scalper.log':
                continue
                
            # Get file modification time
            mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
            
            # Delete if older than cutoff
            if mtime < cutoff_date:
                log_file.unlink()
                deleted_count += 1
                
        # Also check for old JSONL files
        for log_file in log_dir.glob('*.jsonl*'):
            mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
            if mtime < cutoff_date:
                log_file.unlink()
                deleted_count += 1
                
        if deleted_count > 0:
            logger.info(f"Log pruning: removed {deleted_count} old log files")
            
    except Exception as e:
        logger.error(f"Error during log pruning: {str(e)}")
