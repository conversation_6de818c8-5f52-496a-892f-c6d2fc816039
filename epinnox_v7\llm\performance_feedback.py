from typing import Dict, List
import numpy as np
from datetime import datetime, timedelta
from utils.logger import get_logger, log_performance

class PerformanceFeedback:
    """Analyzes trading performance and generates LLM feedback"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # Performance window
        self.window_size = config['performance_window']
        
        # Performance thresholds
        self.min_win_rate = 0.35
        self.max_drawdown = config['risk_limits']['max_drawdown_pct']
        
        # Recent trades memory
        self.recent_trades: List[Dict] = []
        
    def add_trade(self, trade: Dict):
        """Add a trade to recent memory"""
        self.recent_trades.append(trade)
        
        # Keep only recent window
        if len(self.recent_trades) > self.window_size:
                        self.recent_trades.pop(0)

    def get_performance_summary(self) -> Dict:
        """Get current performance metrics"""
        if not self.recent_trades:
            return {
                'win_rate': 0,
                'avg_win': 0,
                'avg_loss': 0,
                'profit_factor': 0,
                'total_pnl': 0,
                'drawdown': 0,  # Changed from max_drawdown for consistency
                'cumulative_pnl': 0,
                'quality_score': 0  # Added missing field
            }
            
        # Calculate basic metrics
        wins = [t for t in self.recent_trades if t['pnl'] > 0]
        losses = [t for t in self.recent_trades if t['pnl'] <= 0]
        
        win_rate = len(wins) / len(self.recent_trades)
        cumulative_pnl = sum(t['pnl'] for t in self.recent_trades)
        
        avg_win = np.mean([t['pnl'] for t in wins]) if wins else 0
        avg_loss = abs(np.mean([t['pnl'] for t in losses])) if losses else 0
        
        # Calculate drawdown
        running_pnl = 0
        peak = 0
        drawdown = 0
        
        for trade in self.recent_trades:
            running_pnl += trade['pnl']
            peak = max(peak, running_pnl)
            drawdown = min(drawdown, running_pnl - peak)
        
        # Calculate profit factor
        total_wins = sum(t['pnl'] for t in wins) if wins else 0
        total_losses = abs(sum(t['pnl'] for t in losses)) if losses else 0
        profit_factor = (total_wins / total_losses) if total_losses > 0 else total_wins
        
        return {
            'win_rate': win_rate * 100,  # as percentage
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'cumulative_pnl': cumulative_pnl,
            'total_pnl': cumulative_pnl,
            'drawdown': abs(drawdown),  # Changed from max_drawdown for consistency
            'quality_score': profit_factor * win_rate * 0.01 if profit_factor > 0 else 0  # Overall quality metric
        }
        
    def get_trading_feedback(self) -> str:
        """
        Generate performance feedback for LLM context
        This helps the model understand recent performance and adjust accordingly
        """
        metrics = self.get_performance_summary()
        
        feedback = []
        
        # Win rate feedback
        if metrics['win_rate'] < self.min_win_rate * 100:
            feedback.append(
                "Win rate is below minimum threshold. "
                "Consider more conservative entry criteria."
            )
        elif metrics['win_rate'] > 60:
            feedback.append(
                "Strong win rate. "
                "Consider increasing position sizes if volatility permits."
            )
            
        # Risk management feedback
        if metrics['drawdown'] > self.max_drawdown * 0.8:
            feedback.append(
                "Approaching maximum drawdown limit. "
                "Reduce position sizes and tighten stop losses."
            )
            
        # Profit factor feedback
        if metrics['profit_factor'] < 1.2:
            feedback.append(
                "Low profit factor. "
                "Review take-profit levels and trade duration."
            )
        elif metrics['profit_factor'] > 2.0:
            feedback.append(
                "Strong profit factor. "
                "Current strategy is working well."
            )
            
        # Return formatted feedback
        if not feedback:
            return "Performance within normal parameters."
        
        return " ".join(feedback)
        
    def analyze_trade_quality(self, trade: Dict) -> float:
        """
        Calculate quality score for a single trade
        Used to help LLM understand what makes a good trade
        
        Parameters
        ----------
        trade : Dict
            Trade data including:
            - entry_price: float
            - exit_price: float
            - duration: float (seconds)
            - pnl: float
            - max_adverse_excursion: float
            
        Returns
        -------
        float
            Quality score between 0 and 1
        """
        # Initialize scoring components
        timing_score = 0.0  # Based on trade duration
        risk_score = 0.0    # Based on max adverse excursion
        pnl_score = 0.0     # Based on profitability
        
        # Score trade duration (prefer 1-5 minute trades for scalping)
        duration_minutes = trade['duration'] / 60
        if 1 <= duration_minutes <= 5:
            timing_score = 1.0
        elif duration_minutes < 1:
            timing_score = duration_minutes
        else:
            timing_score = max(0, 1 - (duration_minutes - 5) / 15)
            
        # Score risk management
        mae_pct = abs(trade.get('max_adverse_excursion', 0) / trade['entry_price'])
        risk_score = max(0, 1 - mae_pct * 20)  # Penalize large drawdowns
        
        # Score profitability
        pnl_pct = trade['pnl'] / (trade['entry_price'] * trade.get('position_size', 1))
        pnl_score = 1 / (1 + np.exp(-10 * pnl_pct))  # Sigmoid function
        
        # Weighted average of components
        quality_score = (
            timing_score * 0.3 +
            risk_score * 0.4 +
            pnl_score * 0.3
        )
        
        return min(1.0, max(0.0, quality_score))
        
    def get_strategy_adjustments(self) -> Dict:
        """
        Generate strategy adjustment recommendations
        based on recent performance
        """
        metrics = self.get_performance_summary()
        
        adjustments = {
            'entry_threshold': 0.0,  # Higher = more selective
            'position_size': 0.0,    # Higher = larger sizes
            'stop_distance': 0.0,    # Higher = wider stops
            'take_profit': 0.0       # Higher = larger targets
        }
        
        # Adjust based on win rate
        if metrics['win_rate'] < 40:
            adjustments['entry_threshold'] += 0.2
            adjustments['position_size'] -= 0.2
        elif metrics['win_rate'] > 60:
            adjustments['position_size'] += 0.1
            
        # Adjust based on profit factor
        if metrics['profit_factor'] < 1.2:
            adjustments['take_profit'] += 0.1
            adjustments['stop_distance'] -= 0.1
        elif metrics['profit_factor'] > 2.0:
            adjustments['position_size'] += 0.1
            
        # Adjust based on drawdown
        if metrics['drawdown'] > self.max_drawdown * 0.7:
            adjustments['position_size'] -= 0.3
            adjustments['stop_distance'] -= 0.2
            
        return adjustments
