"""
Main Dashboard for Epinnox v7

This module provides the main PyQt5 dashboard interface with tabs for:
- Strategy visualization and backtesting
- Parameter sweeping and optimization
- Live LLM simulation monitoring
- System status and controls
"""

import sys
import asyncio
from pathlib import Path
from typing import Dict, Any
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTabWidget, QVBoxLayout, QHBoxLayout,
    QWidget, QPushButton, QLabel, QStatusBar, QMenuBar, QAction,
    QMessageBox, QSplashScreen, QProgressBar
)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont, QIcon, QPixmap

from .strategy_tab import StrategyTab
from .parameter_sweeper import ParameterSweeper
from .llm_sim_tab import LLMSimTab
from utils.logger import get_logger
from utils.config_validator import ConfigValidator
from auth.login_dialog import show_login_dialog
from auth.authentication import session_manager
from auth.decorators import require_auth, admin_required


class EpinnoxDashboard(QMainWindow):
    """
    Main dashboard window for Epinnox v7
    
    Features:
    - Tabbed interface for different modules
    - Real-time status monitoring
    - System controls and configuration
    - Integrated logging and error handling
    """
    
    def __init__(self, config: dict = None):
        super().__init__()
        self.logger = get_logger()
        self.logger.info("Initializing Epinnox v7 Dashboard...")

        # Load configuration
        if config is None:
            self.config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
        else:
            self.config = config

        # Initialize UI components
        self.init_ui()
        self.init_tabs()
        self.init_status_bar()
        self.init_menu_bar()

        # Start status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # Update every 5 seconds

        self.logger.info("Dashboard initialized successfully")
    
    def init_ui(self):
        """Initialize the main UI layout"""
        self.setWindowTitle("Epinnox v7 - LLM Crypto Scalping Dashboard")
        self.setGeometry(100, 100, 1400, 900)
        
        # Set application icon (if available)
        try:
            self.setWindowIcon(QIcon("assets/epinnox_icon.png"))
        except:
            pass  # Icon file not found, continue without it
        
        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        
        # Create header with title and controls
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Epinnox v7 Dashboard")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # User info and logout
        if session_manager.is_authenticated():
            user_label = QLabel(f"User: {session_manager.current_user.username} ({session_manager.current_user.role.value})")
            user_label.setStyleSheet("color: #333; font-weight: bold;")
            header_layout.addWidget(user_label)

            logout_button = QPushButton("Logout")
            logout_button.clicked.connect(self.logout)
            header_layout.addWidget(logout_button)

        # System control buttons
        self.start_button = QPushButton("Start System")
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        self.start_button.clicked.connect(self.start_system)
        header_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("Stop System")
        self.stop_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        self.stop_button.clicked.connect(self.stop_system)
        self.stop_button.setEnabled(False)
        header_layout.addWidget(self.stop_button)

        # Emergency kill switch (admin only)
        if session_manager.has_role(session_manager.current_user.role if session_manager.current_user else None):
            self.kill_switch_button = QPushButton("🛑 EMERGENCY STOP")
            self.kill_switch_button.setStyleSheet("QPushButton { background-color: #d32f2f; color: white; font-weight: bold; }")
            self.kill_switch_button.clicked.connect(self.emergency_stop)
            header_layout.addWidget(self.kill_switch_button)
        
        main_layout.addLayout(header_layout)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Apply styling
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #2196F3;
            }
            QPushButton {
                padding: 8px 16px;
                border: 1px solid #ccc;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
        """)
    
    def init_tabs(self):
        """Initialize all dashboard tabs"""
        try:
            # Strategy Visualization Tab
            self.strategy_tab = StrategyTab()
            self.tab_widget.addTab(self.strategy_tab, "📊 Strategy Analysis")
            
            # Parameter Sweeper Tab
            self.parameter_sweeper = ParameterSweeper()
            self.tab_widget.addTab(self.parameter_sweeper, "🔧 Parameter Optimization")
            
            # LLM Simulation Tab
            self.llm_sim_tab = LLMSimTab()
            self.tab_widget.addTab(self.llm_sim_tab, "🤖 LLM Simulation")
            
            # System Status Tab (placeholder for now)
            status_widget = QWidget()
            status_layout = QVBoxLayout(status_widget)
            status_layout.addWidget(QLabel("System Status and Logs"))
            status_layout.addWidget(QLabel("Coming soon..."))
            self.tab_widget.addTab(status_widget, "⚙️ System Status")
            
            self.logger.info("All dashboard tabs initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing tabs: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to initialize dashboard tabs: {str(e)}")
    
    def init_status_bar(self):
        """Initialize the status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Add status indicators
        self.system_status_label = QLabel("System: Stopped")
        self.system_status_label.setStyleSheet("color: red; font-weight: bold;")
        self.status_bar.addWidget(self.system_status_label)
        
        self.status_bar.addPermanentWidget(QLabel("Epinnox v7"))
    
    def init_menu_bar(self):
        """Initialize the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        
        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu('Tools')
        
        refresh_action = QAction('Refresh Data', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_data)
        tools_menu.addAction(refresh_action)
        
        # Help menu
        help_menu = menubar.addMenu('Help')
        
        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    @require_auth
    def start_system(self):
        """Start the Epinnox trading system"""
        try:
            self.logger.info("Starting Epinnox system...")
            # TODO: Implement actual system startup
            self.system_status_label.setText("System: Starting...")
            self.system_status_label.setStyleSheet("color: orange; font-weight: bold;")

            # Simulate startup delay
            QTimer.singleShot(2000, self.system_started)

            self.start_button.setEnabled(False)

        except Exception as e:
            self.logger.error(f"Error starting system: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to start system: {str(e)}")
    
    def system_started(self):
        """Called when system startup is complete"""
        self.system_status_label.setText("System: Running")
        self.system_status_label.setStyleSheet("color: green; font-weight: bold;")
        self.stop_button.setEnabled(True)
        self.logger.info("Epinnox system started successfully")
    
    @require_auth
    def stop_system(self):
        """Stop the Epinnox trading system"""
        try:
            self.logger.info("Stopping Epinnox system...")
            # TODO: Implement actual system shutdown
            self.system_status_label.setText("System: Stopped")
            self.system_status_label.setStyleSheet("color: red; font-weight: bold;")

            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)

        except Exception as e:
            self.logger.error(f"Error stopping system: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to stop system: {str(e)}")

    @admin_required
    def emergency_stop(self):
        """Emergency stop - immediately halt all trading"""
        reply = QMessageBox.question(
            self,
            'Emergency Stop Confirmation',
            '⚠️ This will immediately stop all trading and close positions!\n\nAre you sure?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.logger.critical("EMERGENCY STOP ACTIVATED")
                # TODO: Implement emergency stop logic
                # - Close all positions
                # - Cancel all orders
                # - Stop all trading
                self.system_status_label.setText("System: EMERGENCY STOPPED")
                self.system_status_label.setStyleSheet("color: red; font-weight: bold; background-color: yellow;")

                self.start_button.setEnabled(True)
                self.stop_button.setEnabled(False)

                QMessageBox.warning(self, "Emergency Stop", "Emergency stop activated. All trading halted.")

            except Exception as e:
                self.logger.error(f"Error during emergency stop: {str(e)}")
                QMessageBox.critical(self, "Error", f"Emergency stop failed: {str(e)}")

    def logout(self):
        """Logout current user"""
        reply = QMessageBox.question(
            self,
            'Logout Confirmation',
            'Are you sure you want to logout?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            session_manager.logout()
            self.logger.info("User logged out")
            self.close()  # Close dashboard and return to login
    
    def refresh_data(self):
        """Refresh all dashboard data"""
        try:
            self.logger.info("Refreshing dashboard data...")
            
            # Refresh strategy tab
            if hasattr(self.strategy_tab, 'refresh_data'):
                self.strategy_tab.refresh_data()
            
            # Refresh parameter sweeper
            if hasattr(self.parameter_sweeper, 'refresh_data'):
                self.parameter_sweeper.refresh_data()
            
            # Refresh LLM sim tab
            if hasattr(self.llm_sim_tab, 'refresh_data'):
                self.llm_sim_tab.refresh_data()
            
            self.status_bar.showMessage("Data refreshed", 3000)
            
        except Exception as e:
            self.logger.error(f"Error refreshing data: {str(e)}")
            QMessageBox.warning(self, "Warning", f"Failed to refresh some data: {str(e)}")
    
    def update_status(self):
        """Update status indicators"""
        try:
            # Update timestamp in status bar
            from datetime import datetime
            current_time = datetime.now().strftime("%H:%M:%S")
            self.status_bar.showMessage(f"Last update: {current_time}", 4000)
            
        except Exception as e:
            self.logger.error(f"Error updating status: {str(e)}")
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About Epinnox v7", 
                         "Epinnox v7 - LLM-Powered Crypto Futures Scalping System\n\n"
                         "Features:\n"
                         "• Real-time scalping engine\n"
                         "• LLM-driven decision making\n"
                         "• Advanced risk management\n"
                         "• Strategy backtesting and optimization\n"
                         "• Live simulation and monitoring\n\n"
                         "Version: 7.0\n"
                         "Built with PyQt5 and Python")
    
    def closeEvent(self, event):
        """Handle application close event"""
        reply = QMessageBox.question(self, 'Exit Confirmation', 
                                   'Are you sure you want to exit Epinnox v7?',
                                   QMessageBox.Yes | QMessageBox.No, 
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.logger.info("Shutting down Epinnox v7 Dashboard")
            event.accept()
        else:
            event.ignore()


def main():
    """Main entry point for the dashboard"""
    app = QApplication(sys.argv)
    app.setApplicationName("Epinnox v7")
    app.setApplicationVersion("7.0")

    # Load configuration
    try:
        config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
    except Exception as e:
        QMessageBox.critical(None, "Configuration Error", f"Failed to load configuration: {str(e)}")
        sys.exit(1)

    # Show login dialog
    if not show_login_dialog(config):
        sys.exit(0)  # User cancelled login

    # Show splash screen
    try:
        splash_pix = QPixmap("assets/splash.png")
        splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
        splash.show()
        app.processEvents()

        # Simulate loading time
        import time
        time.sleep(2)

        splash.close()
    except:
        pass  # No splash screen available

    # Create and show main window
    dashboard = EpinnoxDashboard(config)
    dashboard.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
