"""
Database Integration for Epinnox v7 GUI Dashboard

This module provides database integration for the GUI dashboard,
enabling persistent storage and retrieval of:
- Backtest results
- Trade history
- LLM predictions
- Strategy performance
- User preferences

Usage:
    from gui.database_integration import DatabaseIntegration
    
    db_integration = DatabaseIntegration(config)
    backtests = db_integration.get_recent_backtests()
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timezone, timedelta
from pathlib import Path
import json

from database.database_manager import DatabaseManager, get_database_manager
from database.repositories import (
    TradeRepository, BacktestRepository, 
    OptimizationRepository, LLMRepository, UserRepository
)
from database.models import BacktestResult, TradeRecord, LLMPrediction
from utils.logger import get_logger

logger = get_logger()


class DatabaseIntegration:
    """
    Database integration layer for GUI dashboard
    
    Provides high-level methods for GUI components to interact
    with the database without dealing with low-level details.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize database integration"""
        self.config = config
        self.db_manager = get_database_manager(config)
        logger.info("Database integration initialized")
    
    # Backtest Results Methods
    def save_backtest_result(self, backtest_data: Dict[str, Any]) -> str:
        """
        Save backtest result to database
        
        Args:
            backtest_data: Backtest result data
            
        Returns:
            Backtest ID
        """
        try:
            with self.db_manager.get_session() as session:
                backtest_repo = BacktestRepository(session)
                
                # Generate unique ID if not provided
                if 'backtest_id' not in backtest_data:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    backtest_data['backtest_id'] = f"BT_{timestamp}"
                
                backtest = backtest_repo.create_backtest(backtest_data)
                session.commit()
                
                logger.info(f"Backtest result saved: {backtest.backtest_id}")
                return backtest.backtest_id
                
        except Exception as e:
            logger.error(f"Failed to save backtest result: {str(e)}")
            raise
    
    def get_recent_backtests(self, days: int = 30, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get recent backtest results
        
        Args:
            days: Number of days to look back
            limit: Maximum number of results
            
        Returns:
            List of backtest result dictionaries
        """
        try:
            with self.db_manager.get_session() as session:
                backtest_repo = BacktestRepository(session)
                backtests = backtest_repo.get_recent_backtests(days=days, limit=limit)
                
                return [self._backtest_to_dict(bt) for bt in backtests]
                
        except Exception as e:
            logger.error(f"Failed to get recent backtests: {str(e)}")
            return []
    
    def get_backtests_by_strategy(self, strategy_name: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get backtests for specific strategy"""
        try:
            with self.db_manager.get_session() as session:
                backtest_repo = BacktestRepository(session)
                backtests = backtest_repo.get_backtests_by_strategy(strategy_name, limit)
                
                return [self._backtest_to_dict(bt) for bt in backtests]
                
        except Exception as e:
            logger.error(f"Failed to get backtests for strategy {strategy_name}: {str(e)}")
            return []
    
    def get_best_backtests(self, metric: str = 'total_pnl_pct', limit: int = 10) -> List[Dict[str, Any]]:
        """Get best performing backtests"""
        try:
            with self.db_manager.get_session() as session:
                backtest_repo = BacktestRepository(session)
                backtests = backtest_repo.get_best_backtests(metric=metric, limit=limit)
                
                return [self._backtest_to_dict(bt) for bt in backtests]
                
        except Exception as e:
            logger.error(f"Failed to get best backtests: {str(e)}")
            return []
    
    def get_backtest_summary(self) -> Dict[str, Any]:
        """Get backtest summary statistics"""
        try:
            with self.db_manager.get_session() as session:
                backtest_repo = BacktestRepository(session)
                return backtest_repo.get_backtest_summary()
                
        except Exception as e:
            logger.error(f"Failed to get backtest summary: {str(e)}")
            return {}
    
    # Trade History Methods
    def save_trade(self, trade_data: Dict[str, Any]) -> str:
        """Save trade record to database"""
        try:
            with self.db_manager.get_session() as session:
                trade_repo = TradeRepository(session)
                
                # Generate unique ID if not provided
                if 'trade_id' not in trade_data:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                    trade_data['trade_id'] = f"TRADE_{timestamp}"
                
                trade = trade_repo.create_trade(trade_data)
                session.commit()
                
                logger.info(f"Trade saved: {trade.trade_id}")
                return trade.trade_id
                
        except Exception as e:
            logger.error(f"Failed to save trade: {str(e)}")
            raise
    
    def get_recent_trades(self, hours: int = 24, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent trades"""
        try:
            with self.db_manager.get_session() as session:
                trade_repo = TradeRepository(session)
                trades = trade_repo.get_recent_trades(hours=hours, limit=limit)
                
                return [self._trade_to_dict(trade) for trade in trades]
                
        except Exception as e:
            logger.error(f"Failed to get recent trades: {str(e)}")
            return []
    
    def get_performance_summary(self, strategy_name: Optional[str] = None, 
                              symbol: Optional[str] = None, days: int = 30) -> Dict[str, Any]:
        """Get trading performance summary"""
        try:
            with self.db_manager.get_session() as session:
                trade_repo = TradeRepository(session)
                return trade_repo.get_performance_summary(
                    strategy_name=strategy_name,
                    symbol=symbol,
                    days=days
                )
                
        except Exception as e:
            logger.error(f"Failed to get performance summary: {str(e)}")
            return {}
    
    # LLM Prediction Methods
    def save_llm_prediction(self, prediction_data: Dict[str, Any]) -> str:
        """Save LLM prediction to database"""
        try:
            with self.db_manager.get_session() as session:
                llm_repo = LLMRepository(session)
                
                # Generate unique ID if not provided
                if 'prediction_id' not in prediction_data:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                    prediction_data['prediction_id'] = f"PRED_{timestamp}"
                
                prediction = llm_repo.create_prediction(prediction_data)
                session.commit()
                
                logger.info(f"LLM prediction saved: {prediction.prediction_id}")
                return prediction.prediction_id
                
        except Exception as e:
            logger.error(f"Failed to save LLM prediction: {str(e)}")
            raise
    
    def get_llm_predictions(self, symbol: Optional[str] = None, 
                           limit: int = 100) -> List[Dict[str, Any]]:
        """Get LLM predictions"""
        try:
            with self.db_manager.get_session() as session:
                llm_repo = LLMRepository(session)
                
                if symbol:
                    predictions = llm_repo.get_predictions_by_symbol(symbol, limit)
                else:
                    predictions = llm_repo.get_all(limit)
                
                return [self._prediction_to_dict(pred) for pred in predictions]
                
        except Exception as e:
            logger.error(f"Failed to get LLM predictions: {str(e)}")
            return []
    
    def get_llm_accuracy_stats(self, symbol: Optional[str] = None, 
                              days: int = 7) -> Dict[str, Any]:
        """Get LLM prediction accuracy statistics"""
        try:
            with self.db_manager.get_session() as session:
                llm_repo = LLMRepository(session)
                return llm_repo.get_accuracy_stats(symbol=symbol, days=days)
                
        except Exception as e:
            logger.error(f"Failed to get LLM accuracy stats: {str(e)}")
            return {}
    
    # Optimization Results Methods
    def save_optimization_result(self, optimization_data: Dict[str, Any]) -> str:
        """Save optimization result to database"""
        try:
            with self.db_manager.get_session() as session:
                opt_repo = OptimizationRepository(session)
                
                # Generate unique ID if not provided
                if 'optimization_id' not in optimization_data:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    optimization_data['optimization_id'] = f"OPT_{timestamp}"
                
                optimization = opt_repo.create_optimization(optimization_data)
                session.commit()
                
                logger.info(f"Optimization result saved: {optimization.optimization_id}")
                return optimization.optimization_id
                
        except Exception as e:
            logger.error(f"Failed to save optimization result: {str(e)}")
            raise
    
    def get_best_parameters(self, strategy_name: str, symbol: str) -> Optional[Dict[str, Any]]:
        """Get best parameters for strategy-symbol combination"""
        try:
            with self.db_manager.get_session() as session:
                opt_repo = OptimizationRepository(session)
                return opt_repo.get_best_parameters(strategy_name, symbol)
                
        except Exception as e:
            logger.error(f"Failed to get best parameters: {str(e)}")
            return None
    
    # User Preferences Methods
    def set_user_preference(self, user_id: str, key: str, value: Any) -> bool:
        """Set user preference"""
        try:
            with self.db_manager.get_session() as session:
                user_repo = UserRepository(session)
                
                # Determine value type
                if isinstance(value, bool):
                    value_type = 'boolean'
                elif isinstance(value, (int, float)):
                    value_type = 'number'
                elif isinstance(value, (dict, list)):
                    value_type = 'json'
                    value = json.dumps(value)
                else:
                    value_type = 'string'
                
                user_repo.set_preference(user_id, key, value, value_type)
                session.commit()
                
                return True
                
        except Exception as e:
            logger.error(f"Failed to set user preference: {str(e)}")
            return False
    
    def get_user_preference(self, user_id: str, key: str, default: Any = None) -> Any:
        """Get user preference"""
        try:
            with self.db_manager.get_session() as session:
                user_repo = UserRepository(session)
                return user_repo.get_preference(user_id, key, default)
                
        except Exception as e:
            logger.error(f"Failed to get user preference: {str(e)}")
            return default
    
    # Database Management Methods
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        return self.db_manager.get_database_stats()
    
    def backup_database(self, backup_path: Optional[str] = None) -> str:
        """Create database backup"""
        return self.db_manager.backup_database(backup_path)
    
    def health_check(self) -> Dict[str, Any]:
        """Perform database health check"""
        return self.db_manager.health_check()
    
    # Helper Methods
    def _backtest_to_dict(self, backtest: BacktestResult) -> Dict[str, Any]:
        """Convert BacktestResult to dictionary"""
        return {
            'id': backtest.id,
            'backtest_id': backtest.backtest_id,
            'strategy_name': backtest.strategy_name,
            'symbol': backtest.symbol,
            'initial_balance': backtest.initial_balance,
            'leverage': backtest.leverage,
            'timeframe': backtest.timeframe,
            'start_date': backtest.start_date.isoformat() if backtest.start_date else None,
            'end_date': backtest.end_date.isoformat() if backtest.end_date else None,
            'strategy_config': backtest.strategy_config,
            'total_trades': backtest.total_trades,
            'win_rate': backtest.win_rate,
            'total_pnl': backtest.total_pnl,
            'total_pnl_pct': backtest.total_pnl_pct,
            'max_drawdown_pct': backtest.max_drawdown_pct,
            'sharpe_ratio': backtest.sharpe_ratio,
            'trade_list': backtest.trade_list,
            'equity_curve': backtest.equity_curve,
            'performance_metrics': backtest.performance_metrics,
            'performance_grade': backtest.performance_grade,
            'recommendations': backtest.recommendations,
            'execution_time_seconds': backtest.execution_time_seconds,
            'data_points_processed': backtest.data_points_processed,
            'created_at': backtest.created_at.isoformat() if backtest.created_at else None
        }
    
    def _trade_to_dict(self, trade: TradeRecord) -> Dict[str, Any]:
        """Convert TradeRecord to dictionary"""
        return {
            'id': trade.id,
            'trade_id': trade.trade_id,
            'strategy_name': trade.strategy_name,
            'symbol': trade.symbol,
            'side': trade.side,
            'entry_price': trade.entry_price,
            'exit_price': trade.exit_price,
            'quantity': trade.quantity,
            'leverage': trade.leverage,
            'entry_time': trade.entry_time.isoformat() if trade.entry_time else None,
            'exit_time': trade.exit_time.isoformat() if trade.exit_time else None,
            'duration_seconds': trade.duration_seconds,
            'pnl_usd': trade.pnl_usd,
            'pnl_pct': trade.pnl_pct,
            'fees_usd': trade.fees_usd,
            'stop_loss': trade.stop_loss,
            'take_profit': trade.take_profit,
            'market_regime': trade.market_regime,
            'llm_confidence': trade.llm_confidence,
            'llm_reasoning': trade.llm_reasoning,
            'created_at': trade.created_at.isoformat() if trade.created_at else None
        }
    
    def _prediction_to_dict(self, prediction: LLMPrediction) -> Dict[str, Any]:
        """Convert LLMPrediction to dictionary"""
        return {
            'id': prediction.id,
            'prediction_id': prediction.prediction_id,
            'symbol': prediction.symbol,
            'timestamp': prediction.timestamp.isoformat() if prediction.timestamp else None,
            'current_price': prediction.current_price,
            'market_data': prediction.market_data,
            'predicted_action': prediction.predicted_action,
            'confidence': prediction.confidence,
            'reasoning': prediction.reasoning,
            'entry_price': prediction.entry_price,
            'stop_loss': prediction.stop_loss,
            'take_profit': prediction.take_profit,
            'position_size': prediction.position_size,
            'actual_price_1m': prediction.actual_price_1m,
            'actual_price_5m': prediction.actual_price_5m,
            'actual_price_15m': prediction.actual_price_15m,
            'prediction_correct_1m': prediction.prediction_correct_1m,
            'prediction_correct_5m': prediction.prediction_correct_5m,
            'prediction_correct_15m': prediction.prediction_correct_15m,
            'created_at': prediction.created_at.isoformat() if prediction.created_at else None
        }
