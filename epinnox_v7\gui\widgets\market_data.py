"""
Market Data Widget for Epinnox v7

Live market data feeds with price charts and technical indicators.
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QComboBox, QTableWidget, QTableWidgetItem,
    QGroupBox, QFrame, QTabWidget, QPushButton,
    QHeaderView, QProgressBar
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QBrush
from typing import Dict, Any, List
from datetime import datetime, timedelta
import random

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.dates as mdates
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from utils.logger import get_logger

logger = get_logger()


class MarketDataWidget(QWidget):
    """Live market data display with charts"""
    
    # Signals
    symbol_selected = pyqtSignal(str)
    timeframe_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger()
        
        # Data storage
        self.market_data: Dict[str, Dict[str, Any]] = {}
        self.price_history: Dict[str, List[Dict[str, Any]]] = {}
        self.symbols = ["BTC-USDT", "ETH-USDT"]  # HTX Linear Swap format
        self.current_symbol = self.symbols[0]
        self.current_timeframe = "1m"

        # Connect to data manager for live data
        from gui.data_manager import get_data_manager
        self.data_manager = get_data_manager()
        self.data_manager.market_data_updated.connect(self.on_live_data_update)

        # UI Setup
        self.setup_ui()
        self.setup_styling()

        # Initialize sample data (fallback)
        self.initialize_sample_data()
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_market_data)
        self.update_timer.start(1000)  # Update every second
        
        self.logger.info("Market Data Widget initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Header with controls
        header_layout = QHBoxLayout()
        
        # Title
        title = QLabel("Market Data")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Symbol selector
        header_layout.addWidget(QLabel("Symbol:"))
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems(self.symbols)
        self.symbol_combo.currentTextChanged.connect(self.on_symbol_changed)
        header_layout.addWidget(self.symbol_combo)
        
        # Timeframe selector
        header_layout.addWidget(QLabel("Timeframe:"))
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems(["1m", "5m", "15m", "30m", "1h", "4h", "1d"])
        self.timeframe_combo.setCurrentText(self.current_timeframe)
        self.timeframe_combo.currentTextChanged.connect(self.on_timeframe_changed)
        header_layout.addWidget(self.timeframe_combo)
        
        # Refresh button
        refresh_btn = QPushButton("🔄")
        refresh_btn.setMaximumWidth(30)
        refresh_btn.clicked.connect(self.refresh_data)
        header_layout.addWidget(refresh_btn)
        
        layout.addLayout(header_layout)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        
        # Price Overview Tab
        self.overview_tab = self.create_overview_tab()
        self.tab_widget.addTab(self.overview_tab, "Overview")
        
        # Price Chart Tab
        self.chart_tab = self.create_chart_tab()
        self.tab_widget.addTab(self.chart_tab, "Chart")
        
        # Order Book Tab
        self.orderbook_tab = self.create_orderbook_tab()
        self.tab_widget.addTab(self.orderbook_tab, "Order Book")
        
        layout.addWidget(self.tab_widget)
    
    def create_overview_tab(self) -> QWidget:
        """Create market overview tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Current price display
        self.price_display = self.create_price_display()
        layout.addWidget(self.price_display)
        
        # Market stats
        self.stats_display = self.create_stats_display()
        layout.addWidget(self.stats_display)
        
        # Symbols table
        self.symbols_table = self.create_symbols_table()
        layout.addWidget(self.symbols_table)
        
        return widget
    
    def create_price_display(self) -> QGroupBox:
        """Create current price display"""
        group = QGroupBox("Current Price")
        layout = QGridLayout(group)
        
        # Main price
        self.current_price_label = QLabel("$0.00")
        self.current_price_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.current_price_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.current_price_label, 0, 0, 1, 2)
        
        # Price change
        self.price_change_label = QLabel("$0.00 (0.00%)")
        self.price_change_label.setFont(QFont("Arial", 14))
        self.price_change_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.price_change_label, 1, 0, 1, 2)
        
        # Bid/Ask
        layout.addWidget(QLabel("Bid:"), 2, 0)
        self.bid_label = QLabel("$0.00")
        self.bid_label.setStyleSheet("color: red; font-weight: bold;")
        layout.addWidget(self.bid_label, 2, 1)
        
        layout.addWidget(QLabel("Ask:"), 3, 0)
        self.ask_label = QLabel("$0.00")
        self.ask_label.setStyleSheet("color: green; font-weight: bold;")
        layout.addWidget(self.ask_label, 3, 1)
        
        # Spread
        layout.addWidget(QLabel("Spread:"), 4, 0)
        self.spread_label = QLabel("$0.00 (0.00%)")
        layout.addWidget(self.spread_label, 4, 1)
        
        return group
    
    def create_stats_display(self) -> QGroupBox:
        """Create market statistics display"""
        group = QGroupBox("24h Statistics")
        layout = QGridLayout(group)
        
        # High
        layout.addWidget(QLabel("24h High:"), 0, 0)
        self.high_24h_label = QLabel("$0.00")
        layout.addWidget(self.high_24h_label, 0, 1)
        
        # Low
        layout.addWidget(QLabel("24h Low:"), 1, 0)
        self.low_24h_label = QLabel("$0.00")
        layout.addWidget(self.low_24h_label, 1, 1)
        
        # Volume
        layout.addWidget(QLabel("24h Volume:"), 0, 2)
        self.volume_24h_label = QLabel("0")
        layout.addWidget(self.volume_24h_label, 0, 3)
        
        # Volume USD
        layout.addWidget(QLabel("Volume (USD):"), 1, 2)
        self.volume_usd_label = QLabel("$0")
        layout.addWidget(self.volume_usd_label, 1, 3)
        
        return group
    
    def create_symbols_table(self) -> QTableWidget:
        """Create symbols overview table"""
        table = QTableWidget()
        
        # Set columns
        columns = ["Symbol", "Price", "Change", "Change %", "Volume", "Spread"]
        table.setColumnCount(len(columns))
        table.setHorizontalHeaderLabels(columns)
        
        # Configure table
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # Set row count
        table.setRowCount(len(self.symbols))
        
        return table
    
    def create_chart_tab(self) -> QWidget:
        """Create price chart tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        if MATPLOTLIB_AVAILABLE:
            # Create matplotlib chart
            self.figure = Figure(figsize=(12, 8))
            self.canvas = FigureCanvas(self.figure)
            layout.addWidget(self.canvas)
            
            # Chart controls
            controls_layout = QHBoxLayout()
            
            # Indicators
            controls_layout.addWidget(QLabel("Indicators:"))
            self.indicators_combo = QComboBox()
            self.indicators_combo.addItems(["None", "SMA 20", "EMA 20", "Bollinger Bands", "RSI"])
            self.indicators_combo.currentTextChanged.connect(self.update_chart)
            controls_layout.addWidget(self.indicators_combo)
            
            controls_layout.addStretch()
            
            # Auto-refresh
            self.auto_refresh_chart = QPushButton("Auto Refresh: ON")
            self.auto_refresh_chart.setCheckable(True)
            self.auto_refresh_chart.setChecked(True)
            self.auto_refresh_chart.clicked.connect(self.toggle_auto_refresh)
            controls_layout.addWidget(self.auto_refresh_chart)
            
            layout.addLayout(controls_layout)
            
            # Initialize chart
            self.update_chart()
        else:
            # Fallback if matplotlib not available
            fallback_label = QLabel("📈 Price Chart\n\nMatplotlib not available.\nInstall with: pip install matplotlib")
            fallback_label.setAlignment(Qt.AlignCenter)
            fallback_label.setStyleSheet("font-size: 16px; color: #666; padding: 50px;")
            layout.addWidget(fallback_label)
        
        return widget
    
    def create_orderbook_tab(self) -> QWidget:
        """Create order book tab"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # Bids table
        bids_group = QGroupBox("Bids")
        bids_layout = QVBoxLayout(bids_group)
        
        self.bids_table = QTableWidget()
        self.bids_table.setColumnCount(3)
        self.bids_table.setHorizontalHeaderLabels(["Price", "Size", "Total"])
        self.bids_table.setRowCount(10)
        bids_layout.addWidget(self.bids_table)
        
        layout.addWidget(bids_group)
        
        # Current price in middle
        middle_layout = QVBoxLayout()
        middle_layout.addStretch()
        
        self.orderbook_price_label = QLabel("$0.00")
        self.orderbook_price_label.setFont(QFont("Arial", 18, QFont.Bold))
        self.orderbook_price_label.setAlignment(Qt.AlignCenter)
        self.orderbook_price_label.setStyleSheet("border: 2px solid #2196F3; padding: 10px; background: #f0f8ff;")
        middle_layout.addWidget(self.orderbook_price_label)
        
        middle_layout.addStretch()
        layout.addLayout(middle_layout)
        
        # Asks table
        asks_group = QGroupBox("Asks")
        asks_layout = QVBoxLayout(asks_group)
        
        self.asks_table = QTableWidget()
        self.asks_table.setColumnCount(3)
        self.asks_table.setHorizontalHeaderLabels(["Price", "Size", "Total"])
        self.asks_table.setRowCount(10)
        asks_layout.addWidget(self.asks_table)
        
        layout.addWidget(asks_group)
        
        # Initialize order book
        self.update_orderbook()
        
        return widget
    
    def setup_styling(self):
        """Setup widget styling"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f5f5f5;
            }
            QTableWidget::item {
                padding: 4px;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 6px;
                border: none;
                border-bottom: 2px solid #2196F3;
                font-weight: bold;
            }
        """)
    
    def initialize_sample_data(self):
        """Initialize sample market data"""
        base_prices = {
            "BTC/USDT:USDT": 50000.0,
            "ETH/USDT:USDT": 3000.0,
            "DOGE/USDT:USDT": 0.08,
            "ADA/USDT:USDT": 0.45
        }
        
        for symbol in self.symbols:
            base_price = base_prices.get(symbol, 1.0)
            
            self.market_data[symbol] = {
                "price": base_price,
                "bid": base_price * 0.9995,
                "ask": base_price * 1.0005,
                "change_24h": base_price * 0.02,
                "change_pct": 2.0,
                "high_24h": base_price * 1.05,
                "low_24h": base_price * 0.95,
                "volume_24h": 1000000.0,
                "volume_usd": base_price * 1000000.0,
                "timestamp": datetime.now()
            }
            
            # Initialize price history
            self.price_history[symbol] = []
            for i in range(100):
                timestamp = datetime.now() - timedelta(minutes=100-i)
                price = base_price * (1 + (random.random() - 0.5) * 0.1)
                self.price_history[symbol].append({
                    "timestamp": timestamp,
                    "price": price,
                    "volume": random.uniform(10000, 50000)
                })
    
    def update_market_data(self):
        """Update market data with simulated changes"""
        for symbol in self.symbols:
            if symbol in self.market_data:
                data = self.market_data[symbol]
                
                # Simulate price movement
                price_change = (random.random() - 0.5) * 0.002  # ±0.2%
                new_price = data["price"] * (1 + price_change)
                
                # Update data
                data["price"] = new_price
                data["bid"] = new_price * 0.9995
                data["ask"] = new_price * 1.0005
                data["timestamp"] = datetime.now()
                
                # Add to price history
                self.price_history[symbol].append({
                    "timestamp": datetime.now(),
                    "price": new_price,
                    "volume": random.uniform(10000, 50000)
                })
                
                # Keep only last 100 points
                if len(self.price_history[symbol]) > 100:
                    self.price_history[symbol] = self.price_history[symbol][-100:]
        
        # Update displays
        self.update_price_display()
        self.update_symbols_table()
        self.update_orderbook()
        
        # Update chart if auto-refresh is on
        if MATPLOTLIB_AVAILABLE and hasattr(self, 'auto_refresh_chart') and self.auto_refresh_chart.isChecked():
            self.update_chart()
    
    def update_price_display(self):
        """Update current price display"""
        if self.current_symbol in self.market_data:
            data = self.market_data[self.current_symbol]
            
            # Current price
            self.current_price_label.setText(f"${data['price']:.2f}")
            
            # Price change
            change = data["change_24h"]
            change_pct = data["change_pct"]
            
            change_text = f"${change:+.2f} ({change_pct:+.2f}%)"
            self.price_change_label.setText(change_text)
            
            if change >= 0:
                self.price_change_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.price_change_label.setStyleSheet("color: red; font-weight: bold;")
            
            # Bid/Ask
            self.bid_label.setText(f"${data['bid']:.2f}")
            self.ask_label.setText(f"${data['ask']:.2f}")
            
            # Spread
            spread = data['ask'] - data['bid']
            spread_pct = (spread / data['price']) * 100
            self.spread_label.setText(f"${spread:.2f} ({spread_pct:.3f}%)")
            
            # 24h stats
            self.high_24h_label.setText(f"${data['high_24h']:.2f}")
            self.low_24h_label.setText(f"${data['low_24h']:.2f}")
            self.volume_24h_label.setText(f"{data['volume_24h']:,.0f}")
            self.volume_usd_label.setText(f"${data['volume_usd']:,.0f}")
    
    def update_symbols_table(self):
        """Update symbols overview table"""
        for row, symbol in enumerate(self.symbols):
            if symbol in self.market_data:
                data = self.market_data[symbol]
                
                # Symbol
                self.symbols_table.setItem(row, 0, QTableWidgetItem(symbol))
                
                # Price
                self.symbols_table.setItem(row, 1, QTableWidgetItem(f"${data['price']:.2f}"))
                
                # Change
                change_item = QTableWidgetItem(f"${data['change_24h']:+.2f}")
                if data['change_24h'] >= 0:
                    change_item.setForeground(QBrush(QColor(0, 150, 0)))
                else:
                    change_item.setForeground(QBrush(QColor(200, 0, 0)))
                self.symbols_table.setItem(row, 2, change_item)
                
                # Change %
                change_pct_item = QTableWidgetItem(f"{data['change_pct']:+.2f}%")
                if data['change_pct'] >= 0:
                    change_pct_item.setForeground(QBrush(QColor(0, 150, 0)))
                else:
                    change_pct_item.setForeground(QBrush(QColor(200, 0, 0)))
                self.symbols_table.setItem(row, 3, change_pct_item)
                
                # Volume
                self.symbols_table.setItem(row, 4, QTableWidgetItem(f"{data['volume_24h']:,.0f}"))
                
                # Spread
                spread = data['ask'] - data['bid']
                spread_pct = (spread / data['price']) * 100
                self.symbols_table.setItem(row, 5, QTableWidgetItem(f"{spread_pct:.3f}%"))
    
    def update_orderbook(self):
        """Update order book display"""
        if self.current_symbol in self.market_data:
            data = self.market_data[self.current_symbol]
            current_price = data["price"]
            
            # Update center price
            self.orderbook_price_label.setText(f"${current_price:.2f}")
            
            # Generate sample order book data
            # Bids (below current price)
            for i in range(10):
                price = current_price * (1 - (i + 1) * 0.001)
                size = random.uniform(0.1, 10.0)
                total = price * size
                
                self.bids_table.setItem(i, 0, QTableWidgetItem(f"${price:.2f}"))
                self.bids_table.setItem(i, 1, QTableWidgetItem(f"{size:.3f}"))
                self.bids_table.setItem(i, 2, QTableWidgetItem(f"${total:.2f}"))
                
                # Color coding
                for col in range(3):
                    item = self.bids_table.item(i, col)
                    if item:
                        item.setBackground(QBrush(QColor(255, 240, 240)))
            
            # Asks (above current price)
            for i in range(10):
                price = current_price * (1 + (i + 1) * 0.001)
                size = random.uniform(0.1, 10.0)
                total = price * size
                
                self.asks_table.setItem(i, 0, QTableWidgetItem(f"${price:.2f}"))
                self.asks_table.setItem(i, 1, QTableWidgetItem(f"{size:.3f}"))
                self.asks_table.setItem(i, 2, QTableWidgetItem(f"${total:.2f}"))
                
                # Color coding
                for col in range(3):
                    item = self.asks_table.item(i, col)
                    if item:
                        item.setBackground(QBrush(QColor(240, 255, 240)))
    
    def update_chart(self):
        """Update price chart"""
        if not MATPLOTLIB_AVAILABLE or self.current_symbol not in self.price_history:
            return
        
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        
        # Get price history
        history = self.price_history[self.current_symbol]
        if not history:
            return
        
        timestamps = [point["timestamp"] for point in history]
        prices = [point["price"] for point in history]
        
        # Plot price line
        ax.plot(timestamps, prices, 'b-', linewidth=2, label='Price')
        
        # Add indicators based on selection
        indicator = self.indicators_combo.currentText() if hasattr(self, 'indicators_combo') else "None"
        
        if indicator == "SMA 20" and len(prices) >= 20:
            sma = []
            for i in range(len(prices)):
                if i >= 19:
                    sma.append(sum(prices[i-19:i+1]) / 20)
                else:
                    sma.append(None)
            ax.plot(timestamps, sma, 'r--', linewidth=1, label='SMA 20')
        
        # Format chart
        ax.set_title(f"{self.current_symbol} - {self.current_timeframe}")
        ax.set_xlabel("Time")
        ax.set_ylabel("Price ($)")
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # Format x-axis
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=10))
        
        # Rotate labels
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        self.figure.tight_layout()
        self.canvas.draw()
    
    def on_symbol_changed(self, symbol: str):
        """Handle symbol change"""
        self.current_symbol = symbol
        self.update_price_display()
        if MATPLOTLIB_AVAILABLE:
            self.update_chart()
        self.symbol_selected.emit(symbol)
    
    def on_timeframe_changed(self, timeframe: str):
        """Handle timeframe change"""
        self.current_timeframe = timeframe
        if MATPLOTLIB_AVAILABLE:
            self.update_chart()
        self.timeframe_changed.emit(timeframe)
    
    def toggle_auto_refresh(self):
        """Toggle auto refresh for chart"""
        if self.auto_refresh_chart.isChecked():
            self.auto_refresh_chart.setText("Auto Refresh: ON")
        else:
            self.auto_refresh_chart.setText("Auto Refresh: OFF")
    
    def refresh_data(self):
        """Manually refresh data"""
        self.update_market_data()
        if MATPLOTLIB_AVAILABLE:
            self.update_chart()
    
    def on_live_data_update(self, symbol: str, data: Dict[str, Any]):
        """Handle live market data update from data manager"""
        try:
            # Update market data with live data
            self.market_data[symbol] = {
                "price": data.get('price', 0),
                "bid": data.get('bid', 0),
                "ask": data.get('ask', 0),
                "change_24h": data.get('change_24h', 0),
                "change_pct": data.get('change_pct', 0),
                "high_24h": data.get('price', 0) * 1.02,  # Estimate
                "low_24h": data.get('price', 0) * 0.98,   # Estimate
                "volume_24h": data.get('volume', 0),
                "volume_usd": data.get('price', 0) * data.get('volume', 0),
                "timestamp": datetime.now()
            }

            # Add to price history
            if symbol not in self.price_history:
                self.price_history[symbol] = []

            self.price_history[symbol].append({
                "timestamp": datetime.now(),
                "price": data.get('price', 0),
                "volume": data.get('volume', 0)
            })

            # Keep only last 100 points
            if len(self.price_history[symbol]) > 100:
                self.price_history[symbol] = self.price_history[symbol][-100:]

            # Update displays if this is the current symbol
            if symbol == self.current_symbol:
                self.update_price_display()
                self.update_symbols_table()
                if MATPLOTLIB_AVAILABLE:
                    self.update_chart()

            self.logger.debug(f"Updated live market data for {symbol}: ${data.get('price', 0):.2f}")

        except Exception as e:
            self.logger.error(f"Error updating live data for {symbol}: {str(e)}")

    def get_current_price(self, symbol: str = None) -> float:
        """Get current price for symbol"""
        symbol = symbol or self.current_symbol
        if symbol in self.market_data:
            return self.market_data[symbol]["price"]
        return 0.0
    
    def get_market_data(self, symbol: str = None) -> Dict[str, Any]:
        """Get market data for symbol"""
        symbol = symbol or self.current_symbol
        return self.market_data.get(symbol, {})
