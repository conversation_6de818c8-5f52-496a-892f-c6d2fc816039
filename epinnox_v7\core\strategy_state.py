from datetime import datetime, timedelta
from typing import Dict, Optional
import numpy as np
from utils.enums import MarketRegime, RiskLevel
from utils.logger import get_logger

logger = get_logger()

class StrategyState:
    """Tracks and analyzes market regime, risk levels, and trading state"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # State tracking
        self.market_regime: MarketRegime = MarketRegime.UNKNOWN
        self.risk_level: RiskLevel = RiskLevel.MEDIUM
        self.confidence: float = 0.0
        self.last_trade_time: Optional[datetime] = None
        
        # Market metrics with longer memory
        self.volatility_window = []
        self.price_memory = []
        self.volume_memory = []
        self.max_memory_size = config.get('state_memory_size', 500)  # Store more data points
        
        # Regime tracking enhancements
        self.regime_history = []
        self.regime_change_timestamps = []
        self.trend_strength_history = []
        self.volatility_history = []
        
        # Performance tracking
        self.trades_history = []
        self.current_drawdown = 0.0
        
    def update_market_regime(self, price_data: list, volume_data: list) -> MarketRegime:
        """
        Analyze price action to determine current market regime
        """
        try:
            # Validate input data
            if not price_data or not volume_data or len(price_data) < 2 or len(volume_data) < 2:
                self.logger.warning("Insufficient data for market regime analysis")
                return MarketRegime.UNKNOWN            
            
            # Convert input to numpy arrays with proper handling of dictionaries
            try:
                # Convert price data, handling both direct prices and dictionaries
                price_array = []
                for p in price_data:
                    if isinstance(p, dict):
                        # Extract price from dictionary assuming common field names
                        for key in ['price', 'last', 'close']:
                            if key in p:
                                price_array.append(float(p[key]))
                                break
                    else:
                        price_array.append(float(p))
                
                # Convert volume data similarly
                volume_array = []
                for v in volume_data:
                    if isinstance(v, dict):
                        # Extract volume from dictionary
                        for key in ['volume', 'amount', 'quantity']:
                            if key in v:
                                volume_array.append(float(v[key]))
                                break
                    else:
                        volume_array.append(float(v))
                        
                price_data = np.array(price_array, dtype=float)
                volume_data = np.array(volume_array, dtype=float)
                
            except (ValueError, TypeError) as e:
                self.logger.error(f"Data conversion error: {str(e)}")
                return MarketRegime.UNKNOWN
                
            # Remove any NaN or infinite values
            price_mask = np.isfinite(price_data)
            volume_mask = np.isfinite(volume_data)
            
            # Ensure both arrays have same length
            min_len = min(len(price_mask), len(volume_mask))
            if min_len == 0:
                self.logger.warning("No valid data points after cleaning")
                return MarketRegime.UNKNOWN
                
            combined_mask = price_mask[:min_len] & volume_mask[:min_len]
            price_data = price_data[:min_len][combined_mask]
            volume_data = volume_data[:min_len][combined_mask]
            
            if len(price_data) < 2:
                self.logger.warning("Insufficient valid data points after cleaning")
                return MarketRegime.UNKNOWN

            # Calculate returns and volatility
            returns = np.diff(price_data) / price_data[:-1]
            volatility = np.std(returns[np.isfinite(returns)]) * np.sqrt(len(returns))
            
            # Calculate trend strength using robust linear regression
            try:
                x = np.arange(len(price_data))
                x = x.reshape(-1, 1)  # Reshape for numerical stability
                
                # Add bias term
                X = np.column_stack([x, np.ones(len(x))])
                
                # Use np.linalg.lstsq which is more stable than polyfit
                slope, _ = np.linalg.lstsq(X, price_data, rcond=None)[0]
                
                # Normalize slope to recent price level
                norm_slope = slope / np.mean(price_data)
                
            except np.linalg.LinAlgError as e:
                self.logger.warning(f"Linear regression failed: {e}")
                norm_slope = 0
        
            # Volume analysis with validation
            recent_vol = volume_data[-min(5, len(volume_data)):]
            vol_ratio = (np.mean(recent_vol) / np.mean(volume_data)) if len(volume_data) > 0 else 1
            
            # Regime classification with thresholds
            if volatility > 0.002 and np.abs(norm_slope) > 0.0002:
                self.market_regime = MarketRegime.VOLATILE
            elif vol_ratio > 1.5 and np.abs(norm_slope) > 0.0001:
                self.market_regime = MarketRegime.TRENDING
            elif volatility < 0.001 and np.abs(norm_slope) < 0.0001:
                self.market_regime = MarketRegime.RANGING
            else:
                self.market_regime = MarketRegime.UNKNOWN
                
            return self.market_regime
            
        except Exception as e:
            self.logger.error(f"Error updating market regime: {str(e)}")
            return MarketRegime.UNKNOWN
            
    def update_risk_level(self, 
                         current_drawdown: float,
                         win_rate: float,
                         volatility: float) -> RiskLevel:
        """
        Update risk level based on performance and market conditions
        """
        try:
            # Start with base risk level
            risk_points = 0
            
            # Drawdown impact
            if current_drawdown > self.config['risk_limits']['max_drawdown_pct'] * 0.8:
                risk_points += 3
            elif current_drawdown > self.config['risk_limits']['max_drawdown_pct'] * 0.5:
                risk_points += 2
                
            # Win rate impact    
            if win_rate < self.config['risk_limits']['min_win_rate']:
                risk_points += 2
                
            # Volatility impact
            vol_threshold = 0.001  # 0.1% baseline volatility
            if volatility > vol_threshold * 3:
                risk_points += 2
            elif volatility > vol_threshold * 2:
                risk_points += 1
                
            # Set risk level
            if risk_points >= 5:
                self.risk_level = RiskLevel.EXTREME
            elif risk_points >= 3:
                self.risk_level = RiskLevel.HIGH
            elif risk_points >= 1:
                self.risk_level = RiskLevel.MEDIUM
            else:
                self.risk_level = RiskLevel.LOW
                
            return self.risk_level
            
        except Exception as e:
            self.logger.error(f"Error updating risk level: {str(e)}")
            return RiskLevel.HIGH  # Conservative fallback
            
    def is_in_cooldown(self) -> bool:
        """Check if we're in post-trade cooldown period"""
        if not self.last_trade_time:
            return False
            
        elapsed = datetime.now() - self.last_trade_time
        return elapsed.total_seconds() < self.config['cooldown_seconds']
        
    def record_trade(self, trade_data: Dict):
        """Record trade for performance tracking"""
        self.trades_history.append(trade_data)
        self.last_trade_time = datetime.now()
        
        # Update drawdown
        if trade_data['pnl'] < 0:
            self.current_drawdown = max(
                self.current_drawdown,
                abs(trade_data['pnl'])
            )
        else:
            # Reset drawdown if profitable
            self.current_drawdown = 0
            
    def get_state_summary(self) -> Dict:
        """Get current strategy state summary"""
        return {
            'market_regime': self.market_regime.value,
            'risk_mode': self.risk_level.value,
            'confidence': self.confidence,
            'cooldown_active': self.is_in_cooldown(),
            'current_drawdown': self.current_drawdown
        }
