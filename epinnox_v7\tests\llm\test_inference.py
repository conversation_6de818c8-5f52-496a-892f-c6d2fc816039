import asyncio
import pytest
from llm.model_runner import <PERSON><PERSON><PERSON><PERSON>
from core.llm_prompt_builder import LLMPromptBuilder
from core.llm_response_parser import LLMResponseValidator
from utils.enums import MarketRegime, ActionType

@pytest.mark.asyncio
async def test_trading_inference():
    """Test trading-specific inference with local Phi-3.1-mini model"""
    config = {
        'llm': {
            'api_url': 'http://localhost:1234/v1/completions',
            'model_name': 'Phi-3.1-mini-128k-instruct.Q4_K_M.gguf',
            'max_tokens': 512,
            'temperature': 0.3,
            'timeout': 30,
            'retry_attempts': 3,
            'retry_delay': 1
        },
        'debug_mode': True,
        'logging': {
            'log_dir': 'tests/logs'
        }
    }

    model_runner = ModelRunner(config)
    prompt_builder = LLMPromptBuilder()
    response_validator = LLMResponseValidator()
    
    await model_runner.initialize()

    # Test market data with all required fields
    market_data = {
        'symbol': 'BTC/USDT',
        'price': 50000.0,
        'bid': 49990.0,
        'ask': 50010.0,
        'spread_pct': 0.04,
        'min_spread_pct': 0.05,        # Added required field
        'min_sl_distance_pct': 0.5,    # Added required field
        'delta_1m': 0.1,
        'delta_5m': 0.5,
        'buy_pressure_pct': 55.0,
        'sell_wall_size': 100000,
        'volume_surge': 120.0
    }
    
    # Test position data
    position_data = {
        'side': 'LONG',
        'entry_price': 49800.0,
        'leverage': 2,
        'pnl_usd': 400.0,
        'pnl_pct': 0.8,
        'liq_buffer_pct': 95.0,
        'position_size': 10000
    }
    
    # Test strategy data
    strategy_data = {
        'market_regime': MarketRegime.TRENDING,
        'risk_mode': 'NORMAL',
        'confidence': 0.85,
        'cooldown_active': False
    }
    
    # Test performance data
    performance_data = {
        'cumulative_pnl': 5000.0,
        'win_rate': 0.65,
        'drawdown': -0.02,
        'quality_score': 0.78
    }

    # Build prompt using the actual prompt builder
    prompt = prompt_builder.build_prompt(
        market_data=market_data,
        position_data=position_data,
        strategy_data=strategy_data,
        performance_data=performance_data
    )

    # Get model response
    response = await model_runner.get_trading_decision(prompt)
    
    # Validation assertions
    assert response is not None, "Model response should not be None"
    assert len(response.strip()) > 0, "Model response should not be empty"
    
    # Print response for analysis
    print(f"\nPrompt:\n{prompt}\n")
    print(f"Response:\n{response}\n")
    
    # Try to extract and validate the action
    action = None
    for line in response.split('\n'):
        if 'action:' in line.lower() or '"action":' in line.lower():
            action = line.split(':')[1].strip().lower().replace('"', '').replace(',', '')
            break
    
    if action:
        assert response_validator.validate_action(action), f"Invalid action: {action}"
        
    await model_runner.close()

if __name__ == "__main__":
    asyncio.run(test_trading_inference())
