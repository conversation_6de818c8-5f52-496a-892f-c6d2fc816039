from typing import Dict, Optional, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
from utils.enums import ActionType, RiskLevel
from utils.logger import get_logger, log_risk

class RiskGuard:
    """Validates trades against risk parameters and maintains risk limits"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()

        # Risk limits from config
        self.max_drawdown_pct = config['risk_limits']['max_drawdown_pct']
        self.min_liq_buffer_pct = config['risk_limits']['min_liquidation_buffer_pct']
        self.min_win_rate = config['risk_limits']['min_win_rate']

        # Position sizing
        self.position_size_pct = config['position_size_pct']
        self.leverage = config['leverage']

        # Operation mode
        self.dry_run = config.get('dry_run', True)
        self.live_mode = config.get('live_mode', False)

        # Maximum allowed action size in live mode
        self.max_live_position_size = config.get('max_live_position_size_usd', 50)

        # Circuit breaker parameters
        self.max_daily_loss_usd = config.get('max_daily_loss_usd', 1000)
        self.max_daily_loss_pct = config.get('max_daily_loss_pct', 5.0)
        self.max_consecutive_losses = config.get('max_consecutive_losses', 5)
        self.volatility_halt_threshold = config.get('volatility_halt_threshold', 0.1)
        self.spread_halt_threshold = config.get('spread_halt_threshold', 0.01)
        self.volume_spike_threshold = config.get('volume_spike_threshold', 3.0)

        # Circuit breaker state
        self.emergency_stop_active = False
        self.daily_loss_tracker = {}
        self.consecutive_losses = 0
        self.last_reset_date = datetime.now().date()
        self.trading_halted_until = None

        # Position correlation tracking
        self.max_correlation_exposure = config.get('max_correlation_exposure', 0.3)
        self.active_positions = {}
        
    def check_market_quality(self, analysis: Dict) -> Tuple[bool, str]:
        """Check if market conditions are suitable for trading"""
        
        # Get manipulation metrics
        manipulation = analysis.get('manipulation_metrics', {})
        if manipulation.get('overall_score', 1.0) > 0.3:
            return False, "High manipulation risk detected"
            
        # Check order flow quality
        order_flow = analysis.get('order_flow', {})
        if order_flow:
            # Check for excessive institutional activity
            if order_flow.get('large_orders_count', 0) > 5:
                return False, "High institutional activity - potential manipulation"
                
            # Check for balanced market
            buying_pressure = order_flow.get('buying_pressure', 0)
            selling_pressure = order_flow.get('selling_pressure', 0)
            if max(buying_pressure, selling_pressure) / (min(buying_pressure, selling_pressure) + 1e-10) > 3:
                return False, "Imbalanced order flow - wait for stabilization"
                
            # Check for clear directional bias
            if 0.4 <= order_flow.get('delta_momentum', 0.5) <= 0.6:
                return False, "No clear directional bias"
        
        # Market structure checks
        if 'market_structure' in analysis:
            structure = analysis['market_structure']
            if structure.get('trend') == 'sideways' and structure.get('strength', 0) < 0.3:
                return False, "Choppy market conditions"
        
        # Volume quality check
        natural_trading = manipulation.get('natural_trading', {})
        if natural_trading.get('volume_consistency', 0) < 0.5:
            return False, "Inconsistent volume patterns"
        
        # Spread and liquidity check
        if natural_trading.get('spread_stability', 0) < 0.4:
            return False, "Unstable spreads - poor liquidity"
        
        return True, "Market conditions acceptable"

    def check_circuit_breakers(self, market_data: Dict, current_pnl: float = 0) -> Tuple[bool, str]:
        """
        Check all circuit breaker conditions

        Returns:
            Tuple[bool, str]: (trading_allowed, reason_if_halted)
        """
        # Check emergency stop
        if self.emergency_stop_active:
            return False, "Emergency stop is active"

        # Check if trading is temporarily halted
        if self.trading_halted_until and datetime.now() < self.trading_halted_until:
            return False, f"Trading halted until {self.trading_halted_until}"

        # Reset daily tracking if new day
        self._reset_daily_tracking_if_needed()

        # Check daily loss limits
        daily_loss_check = self._check_daily_loss_limits(current_pnl)
        if not daily_loss_check[0]:
            return daily_loss_check

        # Check consecutive losses
        if self.consecutive_losses >= self.max_consecutive_losses:
            self._halt_trading(minutes=30)
            return False, f"Trading halted: {self.consecutive_losses} consecutive losses"

        # Check market volatility
        volatility_check = self._check_volatility_halt(market_data)
        if not volatility_check[0]:
            return volatility_check

        # Check spread conditions
        spread_check = self._check_spread_halt(market_data)
        if not spread_check[0]:
            return spread_check

        # Check volume spikes
        volume_check = self._check_volume_spike(market_data)
        if not volume_check[0]:
            return volume_check

        return True, "All circuit breaker checks passed"

    def _reset_daily_tracking_if_needed(self):
        """Reset daily tracking if it's a new day"""
        current_date = datetime.now().date()
        if current_date != self.last_reset_date:
            self.daily_loss_tracker = {}
            self.last_reset_date = current_date
            self.logger.info("Daily loss tracking reset for new day")

    def _check_daily_loss_limits(self, current_pnl: float) -> Tuple[bool, str]:
        """Check daily loss limits"""
        today = datetime.now().date()
        daily_loss = self.daily_loss_tracker.get(today, 0) + current_pnl

        # Check absolute USD limit
        if abs(daily_loss) >= self.max_daily_loss_usd:
            self._halt_trading(hours=24)
            return False, f"Daily loss limit reached: ${abs(daily_loss):.2f}"

        # Check percentage limit (would need account balance)
        # This is a simplified check - in production, get actual account balance
        estimated_balance = 10000  # Placeholder
        daily_loss_pct = abs(daily_loss) / estimated_balance * 100

        if daily_loss_pct >= self.max_daily_loss_pct:
            self._halt_trading(hours=24)
            return False, f"Daily loss percentage limit reached: {daily_loss_pct:.2f}%"

        return True, "Daily loss limits OK"

    def _check_volatility_halt(self, market_data: Dict) -> Tuple[bool, str]:
        """Check if volatility is too high"""
        volatility = market_data.get('volatility', 0)

        if volatility > self.volatility_halt_threshold:
            self._halt_trading(minutes=15)
            return False, f"High volatility halt: {volatility:.3f} > {self.volatility_halt_threshold}"

        return True, "Volatility OK"

    def _check_spread_halt(self, market_data: Dict) -> Tuple[bool, str]:
        """Check if spread is too wide"""
        spread_pct = market_data.get('spread_pct', 0)

        if spread_pct > self.spread_halt_threshold:
            self._halt_trading(minutes=10)
            return False, f"Wide spread halt: {spread_pct:.4f} > {self.spread_halt_threshold}"

        return True, "Spread OK"

    def _check_volume_spike(self, market_data: Dict) -> Tuple[bool, str]:
        """Check for unusual volume spikes"""
        current_volume = market_data.get('volume', 0)
        avg_volume = market_data.get('avg_volume', current_volume)

        if avg_volume > 0:
            volume_ratio = current_volume / avg_volume
            if volume_ratio > self.volume_spike_threshold:
                self._halt_trading(minutes=5)
                return False, f"Volume spike halt: {volume_ratio:.2f}x average"

        return True, "Volume OK"

    def _halt_trading(self, minutes: int = 0, hours: int = 0):
        """Halt trading for specified duration"""
        halt_duration = timedelta(minutes=minutes, hours=hours)
        self.trading_halted_until = datetime.now() + halt_duration

        log_risk(f"Trading halted for {minutes}m {hours}h until {self.trading_halted_until}")

    def activate_emergency_stop(self, reason: str = "Manual activation"):
        """Activate emergency stop"""
        self.emergency_stop_active = True
        log_risk(f"EMERGENCY STOP ACTIVATED: {reason}")
        self.logger.critical(f"EMERGENCY STOP: {reason}")

    def deactivate_emergency_stop(self, reason: str = "Manual deactivation"):
        """Deactivate emergency stop"""
        self.emergency_stop_active = False
        self.trading_halted_until = None
        log_risk(f"Emergency stop deactivated: {reason}")
        self.logger.info(f"Emergency stop deactivated: {reason}")

    def record_trade_result(self, pnl: float, symbol: str):
        """Record trade result for tracking"""
        today = datetime.now().date()

        # Update daily loss tracker
        if today not in self.daily_loss_tracker:
            self.daily_loss_tracker[today] = 0
        self.daily_loss_tracker[today] += pnl

        # Update consecutive losses
        if pnl < 0:
            self.consecutive_losses += 1
        else:
            self.consecutive_losses = 0  # Reset on winning trade

        self.logger.info(f"Trade result recorded: {symbol} PnL: {pnl:.2f}, Consecutive losses: {self.consecutive_losses}")

    def get_risk_status(self) -> Dict:
        """Get current risk status"""
        today = datetime.now().date()
        daily_loss = self.daily_loss_tracker.get(today, 0)

        return {
            'emergency_stop_active': self.emergency_stop_active,
            'trading_halted': self.trading_halted_until is not None,
            'trading_halted_until': self.trading_halted_until.isoformat() if self.trading_halted_until else None,
            'daily_loss_usd': daily_loss,
            'daily_loss_limit_usd': self.max_daily_loss_usd,
            'consecutive_losses': self.consecutive_losses,
            'max_consecutive_losses': self.max_consecutive_losses,
            'daily_loss_pct_used': abs(daily_loss) / self.max_daily_loss_usd * 100,
        }

    def validate_trade(self,
                      action: Dict,
                      market_data: Dict,
                      position_data: Dict,
                      strategy_state: Dict) -> Tuple[bool, Optional[str]]:
        """
        Validate a proposed trade against all risk parameters
        
        Returns
        -------
        Tuple[bool, Optional[str]]
            (is_valid, reason_if_invalid)
        """
        try:
            # Check circuit breakers first
            circuit_check = self.check_circuit_breakers(market_data)
            if not circuit_check[0]:
                return False, circuit_check[1]

            # Verify we have all required fields
            if not action:
                return False, "Empty action data"

            # Validate required action fields exist
            required_fields = {'action', 'confidence'}
            if action['action'] not in [ActionType.HOLD.value, ActionType.CLOSE.value]:
                required_fields.update({'entry_price', 'stop_loss'})

            missing_fields = required_fields - set(action.keys())
            if missing_fields:
                return False, f"Missing required fields: {', '.join(missing_fields)}"

            # Validate market data has required fields
            if not market_data or 'price' not in market_data:
                return False, "Invalid or missing market data"

            # Skip validation for hold actions
            if action['action'] == ActionType.HOLD.value:
                return True, None
                
            # Extra safety for live mode
            if not self.dry_run and self.live_mode:
                # Verify position size limit in live mode
                if action.get('position_size', 0) > self.max_live_position_size:
                    return False, f"Position size exceeds live mode limit of {self.max_live_position_size} USD"
                    
                # Additional safety checks for live mode
                confidence_threshold = self.config.get('live_mode_confidence_threshold', 0.8)
                if action.get('confidence', 0) < confidence_threshold:
                    return False, f"Confidence too low for live trading: {action.get('confidence', 0):.2f} < {confidence_threshold}"
            
            # 1. Check drawdown limit
            current_drawdown = strategy_state.get('current_drawdown', 0)
            if current_drawdown >= self.max_drawdown_pct:
                return False, f"Max drawdown limit reached: {current_drawdown:.2f}%"
                
            # 2. Check liquidation buffer for new trades
            if action['action'] in [ActionType.LONG.value, ActionType.SHORT.value]:
                liq_buffer = self._calculate_liquidation_buffer(
                    action, market_data['price'], self.leverage
                )
                if liq_buffer < self.min_liq_buffer_pct:
                    return False, f"Insufficient liquidation buffer: {liq_buffer:.2f}%"
                    
            # 3. Validate stop loss
            if not self._validate_stop_loss(action, market_data):
                return False, "Invalid stop loss placement"
                
            # 4. Check risk level restrictions
            if strategy_state['risk_mode'] == RiskLevel.EXTREME.value:
                if action['action'] != ActionType.CLOSE.value:
                    return False, "New positions blocked due to extreme risk level"
                    
            # 5. Validate position sizing
            if not self._validate_position_size(action, market_data):
                return False, "Invalid position size"
                
            return True, None
            
        except Exception as e:
            log_risk(f"Error in trade validation: {str(e)}")
            return False, f"Validation error: {str(e)}"
            
    def _calculate_liquidation_buffer(self,
                                    action: Dict,
                                    current_price: float,
                                    leverage: float) -> float:
        """Calculate distance to liquidation as percentage"""
        entry_price = action['entry_price']
        
        if action['action'] == ActionType.LONG.value:
            liq_price = entry_price * (1 - (1 / leverage))
            buffer_pct = ((entry_price - liq_price) / entry_price) * 100
        else:  # SHORT
            liq_price = entry_price * (1 + (1 / leverage))
            buffer_pct = ((liq_price - entry_price) / entry_price) * 100
            
        return buffer_pct
        
    def _validate_stop_loss(self, action: Dict, market_data: Dict) -> bool:
        """Validate stop loss placement"""
        if action['action'] in [ActionType.HOLD.value, ActionType.CLOSE.value]:
            return True
            
        entry_price = action['entry_price']
        stop_loss = action['stop_loss']
        
        # Calculate SL distance as percentage
        sl_pct = abs(entry_price - stop_loss) / entry_price * 100
        
        # Must be greater than minimum
        if sl_pct < market_data['min_sl_distance_pct']:
            return False
            
        # Validate SL direction
        if action['action'] == ActionType.LONG.value:
            return stop_loss < entry_price
        else:  # SHORT
            return stop_loss > entry_price
            
    def _validate_position_size(self, action: Dict, market_data: Dict) -> bool:
        """Validate position size against limits"""
        if action['action'] in [ActionType.HOLD.value, ActionType.CLOSE.value]:
            return True
            
        # Get position size in quote currency
        pos_size = abs(action['position_size'])
        
        # Calculate allowed size based on config percentage
        # Note: In real implementation, would need account balance from exchange
        max_size = market_data.get('account_balance', 1000) * self.position_size_pct
        
        return pos_size <= max_size
        
    def adjust_position_size(self,
                           base_size: float,
                           risk_level: RiskLevel,
                           win_rate: float) -> float:
        """Adjust position size based on risk factors"""
        
        # Start with configured size
        adjusted_size = base_size
        
        # Risk level adjustments
        risk_multipliers = {
            RiskLevel.LOW: 1.0,
            RiskLevel.MEDIUM: 0.8,
            RiskLevel.HIGH: 0.5,
            RiskLevel.EXTREME: 0.25
        }
        
        # Apply risk multiplier
        adjusted_size *= risk_multipliers[risk_level]
        
        # Win rate adjustment
        if win_rate < self.min_win_rate + 0.1:  # Within 10% of minimum
            adjusted_size *= 0.5
            
        return adjusted_size
