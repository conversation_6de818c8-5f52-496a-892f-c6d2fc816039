from typing import Dict, Optional
from utils.enums import MarketRegime, PositionSide
from utils.logger import get_logger

logger = get_logger()

class LLMPromptBuilder:
    """Builds structured prompts for the LLM trading decision maker"""
    
    @staticmethod
    def _format_market_section(market_data: Dict) -> str:
        """Format market data section of the prompt"""
        # Basic market data
        prompt = f"""[Market]
- Symbol: {market_data['symbol']}
- Price: {market_data['price']}
- Bid: {market_data['bid']} | Ask: {market_data['ask']} | Spread: {market_data['spread_pct']:.3f}%
- 1m Change: {market_data['delta_1m']:.2f}% | 5m Change: {market_data['delta_5m']:.2f}%
- Order Book: Buy Pressure: {market_data['buy_pressure_pct']:.2f}%, Sell Wall: {market_data['sell_wall_size']}
- Volume Surge: {market_data['volume_surge']:.2f}%\n"""

        # Add technical indicators for each timeframe
        if 'indicators' in market_data:
            prompt += "\n[Technical Indicators]"
            for timeframe, indicators in market_data['indicators'].items():
                prompt += f"\n{timeframe} Timeframe:"
                if indicators['sma_20']:
                    prompt += f"\n- SMA(20): {indicators['sma_20']:.2f}"
                if indicators['sma_50']:
                    prompt += f"\n- SMA(50): {indicators['sma_50']:.2f}"
                if indicators['rsi']:
                    prompt += f"\n- RSI: {indicators['rsi']:.2f}"
                if indicators['macd']['macd']:
                    prompt += f"\n- MACD: {indicators['macd']['macd']:.2f} | Signal: {indicators['macd']['signal']:.2f} | Hist: {indicators['macd']['hist']:.2f}"
                if indicators['bb']['middle']:
                    prompt += f"\n- BB Middle: {indicators['bb']['middle']:.2f}"
                    prompt += f"\n- BB Upper: {indicators['bb']['upper']:.2f}"
                    prompt += f"\n- BB Lower: {indicators['bb']['lower']:.2f}"
        
        return prompt

    @staticmethod
    def _format_position_section(position_data: Dict) -> str:
        """Format position data section of the prompt"""
        return f"""[Position]
- Side: {position_data['side']}
- Entry Price: {position_data.get('entry_price', 'None')}
- Leverage: {position_data['leverage']}x
- Unrealized PnL: {position_data.get('pnl_usd', 0):.2f} USDT ({position_data.get('pnl_pct', 0):.2f}%)
- Liquidation Buffer: {position_data.get('liq_buffer_pct', 0):.2f}%
- Position Size: {position_data.get('position_size', 0)} USDT"""

    @staticmethod
    def _format_strategy_section(strategy_data: Dict) -> str:
        """Format strategy state section of the prompt"""
        return f"""[Strategy]
- Regime: {strategy_data['market_regime']}
- Risk Mode: {strategy_data['risk_mode']}
- Signal Confidence: {strategy_data['confidence']:.2f}
- Cooldown Active: {strategy_data['cooldown_active']}"""

    @staticmethod
    def _format_performance_section(performance_data: Dict) -> str:
        """Format performance metrics section of the prompt"""
        return f"""[Performance Metrics]
- PnL: {performance_data['cumulative_pnl']}
- Win Rate: {performance_data['win_rate']}
- Drawdown: {performance_data['drawdown']}
- Trade Score: {performance_data['quality_score']}"""

    @staticmethod
    def _format_account_section(account_data: Dict) -> str:
        """Format account metrics section of the prompt with enhanced LLM awareness"""
        metrics = account_data.get('account_metrics', {})
        risk = account_data.get('risk_metrics', {})
        capacity = account_data.get('trading_capacity', {})
        performance = account_data.get('performance_metrics', {})
        volatility = account_data.get('volatility_metrics', {})
        market_analysis = account_data.get('market_analysis', {})
        
        # Format base account info
        account_info = f"""[Account Status]
Balance Metrics:
- Total Balance: {metrics.get('total_balance', 0):.2f} USDT
- Free Balance: {metrics.get('free_balance', 0):.2f} USDT
- Used Margin: {metrics.get('used_margin', 0):.2f} USDT
- Available Margin: {metrics.get('available_margin', 0):.2f} USDT

Position Overview:
- Total Position Value: {metrics.get('total_position_value', 0):.2f} USDT
- Unrealized PnL: {metrics.get('unrealized_pnl', 0):.2f} USDT
- Active Positions: {metrics.get('position_count', 0)}
- Max Leverage Used: {metrics.get('max_leverage_used', 0)}x

Risk Status:
- Account Risk: {risk.get('account_risk', 0):.2%}
- Margin Usage: {risk.get('margin_usage_pct', 0):.1f}%
- Available Margin: {risk.get('available_margin_pct', 0):.1f}%
- Max Drawdown: {risk.get('max_drawdown', 0):.2f}%
- Sharpe Ratio: {risk.get('sharpe_ratio', 0):.2f}
- Calmar Ratio: {risk.get('calmar_ratio', 0):.2f}

Performance Metrics:
- Win Rate: {performance.get('win_rate', 0):.2%}
- Profit Factor: {performance.get('profit_factor', 0):.2f}
- Avg Daily ROI: {performance.get('avg_daily_roi', 0):.2f}%
- Diversification: {performance.get('diversification_score', 0):.2f}"""

        # Add volatility section if available
        if volatility:
            account_info += f"""

Volatility Analysis:
- Overall Market Volatility: {volatility.get('overall_volatility', 0):.4f}"""

            # Add individual symbol volatilities
            symbols = volatility.get('symbols', {})
            if symbols:
                account_info += "\n- Symbol Volatilities:"
                for symbol, data in symbols.items():
                    if data:
                        account_info += f"\n  - {symbol}: {data.get('current', 0):.4f} ({data.get('regime', 'normal')})"

        # Add detected patterns if available
        detected_patterns = market_analysis.get('detected_patterns', {})
        if detected_patterns:
            account_info += "\n\nDetected Market Patterns:"
            for symbol, pattern_data in detected_patterns.items():
                patterns = pattern_data.get('patterns', [])
                confidence = pattern_data.get('confidence', 0)
                if patterns:
                    account_info += f"\n- {symbol}: {', '.join(patterns)} (confidence: {confidence:.2f})"

        # Add portfolio correlation warning if needed
        high_corr_pairs = performance.get('highly_correlated_pairs', [])
        if high_corr_pairs:
            account_info += "\n\nHigh Correlation Warning:"
            for pair_data in high_corr_pairs[:3]:  # Show top 3 correlated pairs
                pair = pair_data.get('pair', '')
                corr = pair_data.get('correlation', 0)
                account_info += f"\n- {pair}: {corr:.2f} correlation"

        # Add trading capacity
        account_info += f"""

Trading Capacity:
- Max New Position: {capacity.get('max_new_position_size', 0):.2f} USDT
- Safe Position Size: {capacity.get('safe_position_size', 0):.2f} USDT
- Position Limit: {"REACHED" if capacity.get('position_size_limit_reached') else "Available"}
- Margin Limit: {"REACHED" if capacity.get('margin_limit_reached') else "Available"}"""

        return account_info

    def _format_technical_indicators(self, analysis: Dict) -> str:
        """Format technical indicators section"""
        indicators = analysis.get('technical_indicators', {})
        if not indicators:
            return ""

        result = "\nTECHNICAL INDICATORS:\n"
        for timeframe, data in indicators.items():
            result += f"{timeframe} Timeframe:\n"
            for indicator, value in data.items():
                if isinstance(value, (int, float)):
                    result += f"- {indicator}: {value:.4f}\n"
                else:
                    result += f"- {indicator}: {value}\n"

        return result

    def _format_market_analysis(self, analysis: Dict) -> str:
        """Format market analysis for LLM consumption"""
        manipulation_metrics = analysis.get('manipulation_metrics', {})
        
        # Add manipulation analysis section
        manipulation_status = (
            "MARKET MANIPULATION ANALYSIS:\n"
            f"Overall Manipulation Score: {manipulation_metrics.get('overall_score', 1.0):.2f}\n"
            f"Safe to Trade: {manipulation_metrics.get('safe_to_trade', False)}\n\n"
            "Detected Patterns:\n"
        )
        
        if manipulation_metrics:
            # Add spoofing analysis
            spoofing = manipulation_metrics.get('spoofing_indicators', {})
            manipulation_status += (
                f"- Spoofing Risk: {spoofing.get('score', 0):.2f}\n"
                f"  Volume Spikes: {spoofing.get('volume_spikes_count', 0)}\n"
            )
            
            # Add wash trading analysis
            wash = manipulation_metrics.get('wash_trading', {})
            manipulation_status += (
                f"- Wash Trading Risk: {wash.get('score', 0):.2f}\n"
                f"  Pattern Score: {wash.get('volume_pattern_score', 0):.2f}\n"
            )
            
            # Add momentum ignition analysis
            ignition = manipulation_metrics.get('momentum_ignition', {})
            manipulation_status += (
                f"- Momentum Ignition Risk: {ignition.get('score', 0):.2f}\n"
                f"  Aggressive Moves: {ignition.get('aggressive_moves', 0)}\n"
            )
            
            # Add layering analysis
            layering = manipulation_metrics.get('layering_indicators', {})
            manipulation_status += (
                f"- Layering Risk: {layering.get('score', 0):.2f}\n"
                f"  Volume Concentration: {layering.get('volume_concentration', 0):.2f}\n"
            )
            
            # Add natural trading analysis
            natural = manipulation_metrics.get('natural_trading', {})
            manipulation_status += (
                "\nNatural Trading Indicators:\n"
                f"- Confidence: {natural.get('confidence', 0):.2f}\n"
                f"- Market Efficiency: {natural.get('market_efficiency', 0):.2f}\n"
                f"- Volume Consistency: {natural.get('volume_consistency', 0):.2f}\n"
            )
        
        # Add order flow analysis
        order_flow = analysis.get('order_flow', {})
        if order_flow:
            order_flow_status = (
                "\nORDER FLOW ANALYSIS:\n"
                f"Buying Pressure: {order_flow.get('buying_pressure', 0):.2f}\n"
                f"Selling Pressure: {order_flow.get('selling_pressure', 0):.2f}\n"
                f"Large Orders: {order_flow.get('large_orders_count', 0)}\n"
                f"Delta Momentum: {order_flow.get('delta_momentum', 0):.2f}\n\n"
                "Institutional Levels:\n"
            )
            
            for level in order_flow.get('institutional_levels', []):
                order_flow_status += (
                    f"- {level['type'].capitalize()} at {level['price']:.2f}\n"
                    f"  Volume: {level['volume']:.2f}, Delta: {level['delta']:.2f}\n"
                )
            
            if order_flow.get('absorption_zones', []):
                order_flow_status += "\nAbsorption Zones:\n"
                for zone in order_flow['absorption_zones'][:3]:  # Show top 3 zones
                    order_flow_status += f"- Price: {zone['price']:.2f}, Volume: {zone['volume']:.2f}\n"
                    
            # Add market regime analysis
            market_regime = analysis.get('market_regime', {})
            if market_regime:
                regime_status = "\nMARKET REGIME ANALYSIS:\n"
                regime_status += f"Current Regime: {market_regime.get('regime', 'Unknown')}\n"
                regime_status += f"Regime Strength: {market_regime.get('strength', 0):.2f}\n"
                regime_status += f"Volatility State: {market_regime.get('volatility', 'normal')}\n"
                
                # Add trait-based analysis
                traits = market_regime.get('traits', {})
                if traits:
                    regime_status += "\nRegime Traits:\n"
                    for trait, value in traits.items():
                        regime_status += f"- {trait.replace('_', ' ').title()}: {value:.2f}\n"
                        
                # Add recommended actions
                recommendations = market_regime.get('recommendations', [])
                if recommendations:
                    regime_status += "\nRegime-Specific Recommendations:\n"
                    for rec in recommendations:
                        regime_status += f"- {rec}\n"
                        
                return self._format_technical_indicators(analysis) + "\n" + regime_status + "\n" + manipulation_status + "\n" + order_flow_status
            
            return self._format_technical_indicators(analysis) + "\n" + manipulation_status + "\n" + order_flow_status
        
        return self._format_technical_indicators(analysis) + "\n" + manipulation_status

    def build_prompt(
        self,
        market_data: Dict,
        position_data: Dict,
        strategy_data: Dict,
        performance_data: Dict,
        analysis: Dict,
        account_data: Dict,
        opportunity_data: Dict = None
    ) -> str:
        """
        Build the complete prompt for the LLM
        
        Parameters
        ----------
        market_data : Dict
            Current market data including price, spread, volume etc.
        position_data : Dict
            Current position information if any
        strategy_data : Dict
            Current strategy state and parameters
        performance_data : Dict
            Recent performance metrics
        analysis : Dict
            Market analysis data including manipulation detection
        account_data : Dict
            Account status and metrics
        opportunity_data : Dict, optional
            Additional opportunity data for multi-symbol context (default is None)
            
        Returns
        -------
        str
            Complete formatted prompt for the LLM
        """
        try:
            # Add multi-symbol context if available
            symbol_context = ""
            if opportunity_data:
                symbol_context = f"""
SYMBOL SELECTION METRICS:
- Market Quality Score: {opportunity_data['score']:.2f}
- Volume Quality: {opportunity_data['metrics'].get('volume_quality', 0):.2f}
- Spread Quality: {opportunity_data['metrics'].get('spread_quality', 0):.2f}
- Manipulation Safety: {opportunity_data['metrics'].get('manipulation_safety', 0):.2f}
- Opportunity Score: {opportunity_data['metrics'].get('opportunity_score', 0):.2f}
"""

            prompt = f"""You are a high-frequency futures scalping strategist. Use the data below to make a decision.

{LLMPromptBuilder._format_account_section(account_data)}

{symbol_context}
{self._format_market_section(market_data)}

{LLMPromptBuilder._format_position_section(position_data)}

{LLMPromptBuilder._format_strategy_section(strategy_data)}

{LLMPromptBuilder._format_performance_section(performance_data)}

{self._format_market_analysis(analysis)}

ACCOUNT MANAGEMENT RULES:
1. Never exceed {account_data['risk_metrics']['available_margin_pct']:.1f}% of available margin
2. Keep total account risk below 80%
3. Use position sizes within safe limits ({account_data['trading_capacity']['safe_position_size']:.2f} USDT)
4. Reduce position sizes when risk score > 0.7
5. No new positions if margin usage > 70%
6. Scale out of positions if unrealized PnL < -2%

DECISION FRAMEWORK:
1. Symbol Quality Check:
   - Verify market quality score (must be > 0.7)
   - Check volume and spread quality
   - Validate manipulation safety
   - Assess opportunity metrics

2. Safety Analysis:
   - Check manipulation score (must be < 0.3)
   - Verify natural trading metrics
   - Validate volume consistency
   - Check spread stability

3. Market Context:
   - Analyze current regime
   - Check volatility state
   - Verify institutional activity
   - Review order flow delta

4. Entry Conditions:
   - Confirm price action patterns
   - Validate support/resistance
   - Check absorption zones
   - Verify volume confirmation

5. Risk Management:
   - Set position size based on volatility
   - Use regime-appropriate stop loss
   - Scale take profit to market conditions
   - Monitor manipulation indicators

Return a JSON response with your decision:
{{
    "action": "long" | "short" | "hold" | "close",
    "entry_price": float,
    "stop_loss": float,
    "take_profit": float,
    "position_size": float <= {account_data['trading_capacity']['safe_position_size']:.2f},
    "confidence": float,
    "reason": "detailed explanation including account risk assessment"
}}

Note: Your confidence score should reflect:
- Account conditions (0.2 weight)
- Market conditions (0.2 weight)
- Setup quality (0.2 weight)
- Risk/reward ratio (0.2 weight)
- Safety metrics (0.2 weight)

Trading Rules:
1. Only trade symbols with quality score > 0.7
2. No trading when spread quality < 0.5
3. Require manipulation safety > 0.7
4. Maintain SL distance > {market_data.get('min_sl_distance_pct', 1)}%
5. Be more conservative when drawdown > 5%
6. Consider market regime in position sizing
7. Exit immediately if manipulation patterns emerge
8. Scale position size with symbol quality
9. Prioritize high opportunity score setups
10. Monitor correlated symbols for confirmation"""

            return prompt
            
        except Exception as e:
            logger.error(f"Error building LLM prompt: {str(e)}")
            raise
