"""
Main Backtester Interface for Epinnox v7

This module provides a high-level interface for running backtests with
historical data, strategy configurations, and comprehensive result analysis.
"""

import asyncio
import pandas as pd
import ccxt.async_support as ccxt
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Union
import yaml

from core.strategy_backtester import StrategyBacktester, BacktestResults
from utils.logger import get_logger
from utils.config_validator import ConfigValidator


class Backtester:
    """
    High-level backtesting interface for Epinnox v7
    
    Features:
    - Historical data fetching via CCXT
    - Strategy configuration loading
    - Comprehensive backtesting execution
    - Results analysis and export
    - Integration with existing Epinnox components
    """
    
    def __init__(self, config_path: str = "config/scalper_config.yaml"):
        """
        Initialize the backtester
        
        Parameters:
            config_path: Path to the main configuration file
        """
        self.config = ConfigValidator.load_and_validate(config_path)
        self.logger = get_logger()
        
        # Override settings for backtesting
        self.config['live_mode'] = False
        self.config['debug_mode'] = True
        
    async def fetch_historical_data(
        self, 
        symbol: str, 
        timeframe: str = '1m',
        days: int = 7,
        exchange_id: str = 'binance'
    ) -> pd.DataFrame:
        """
        Fetch historical OHLCV data for backtesting
        
        Parameters:
            symbol: Trading pair symbol (e.g., 'DOGE/USDT:USDT')
            timeframe: Timeframe for candles ('1m', '5m', '15m', '1h', '1d')
            days: Number of days of historical data to fetch
            exchange_id: Exchange to fetch data from
            
        Returns:
            DataFrame with OHLCV data
        """
        try:
            self.logger.info(f"Fetching {days} days of {timeframe} data for {symbol} from {exchange_id}")
            
            # Initialize exchange
            exchange_class = getattr(ccxt, exchange_id)
            exchange = exchange_class({
                'enableRateLimit': True,
                'options': {'defaultType': 'swap'}  # For futures
            })
            
            # Calculate time range
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            # Fetch data
            since = int(start_time.timestamp() * 1000)
            limit = 1000  # Most exchanges limit to 1000 candles per request
            
            all_candles = []
            current_since = since
            
            while current_since < int(end_time.timestamp() * 1000):
                try:
                    candles = await exchange.fetch_ohlcv(
                        symbol, timeframe, since=current_since, limit=limit
                    )
                    
                    if not candles:
                        break
                    
                    all_candles.extend(candles)
                    current_since = candles[-1][0] + 1  # Move to next timestamp
                    
                    # Rate limiting
                    await asyncio.sleep(exchange.rateLimit / 1000)
                    
                except Exception as e:
                    self.logger.warning(f"Error fetching batch: {str(e)}")
                    break
            
            await exchange.close()
            
            if not all_candles:
                raise ValueError(f"No data fetched for {symbol}")
            
            # Convert to DataFrame
            df = pd.DataFrame(all_candles, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Remove duplicates and sort
            df = df.drop_duplicates(subset=['timestamp']).sort_values('timestamp').reset_index(drop=True)
            
            self.logger.info(f"Fetched {len(df)} candles from {df['timestamp'].iloc[0]} to {df['timestamp'].iloc[-1]}")
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error fetching historical data: {str(e)}")
            raise
    
    def load_strategy(self, strategy_path: str) -> Dict:
        """
        Load strategy configuration from YAML file
        
        Parameters:
            strategy_path: Path to strategy YAML file
            
        Returns:
            Strategy configuration dictionary
        """
        try:
            with open(strategy_path, 'r') as f:
                strategy = yaml.safe_load(f)
            
            self.logger.info(f"Loaded strategy from {strategy_path}")
            return strategy
            
        except Exception as e:
            self.logger.error(f"Error loading strategy: {str(e)}")
            raise
    
    async def run_backtest(
        self,
        symbol: str,
        ohlcv_data: pd.DataFrame,
        strategy_config: Optional[Dict] = None,
        use_llm: bool = False,
        initial_balance: float = 10000.0,
        leverage: int = 1
    ) -> BacktestResults:
        """
        Run a comprehensive backtest
        
        Parameters:
            symbol: Trading pair symbol
            ohlcv_data: Historical OHLCV data
            strategy_config: Strategy configuration (None for LLM-only)
            use_llm: Whether to use LLM for decisions
            initial_balance: Starting balance in USDT
            leverage: Trading leverage
            
        Returns:
            Comprehensive backtest results
        """
        try:
            # Update config for backtest
            backtest_config = self.config.copy()
            backtest_config.update({
                'initial_balance': initial_balance,
                'leverage': leverage,
                'symbols': [symbol]
            })
            
            # Create backtester instance
            backtester = StrategyBacktester(
                config=backtest_config,
                ohlcv_data=ohlcv_data,
                symbol=symbol,
                strategy_config=strategy_config
            )
            
            # Run backtest
            results = await backtester.run(use_llm=use_llm)
            
            # Export results
            export_path = await backtester.export_results(results)
            self.logger.info(f"Backtest completed. Results saved to: {export_path}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error running backtest: {str(e)}")
            raise
    
    async def run_strategy_comparison(
        self,
        symbol: str,
        ohlcv_data: pd.DataFrame,
        strategies: List[Dict],
        initial_balance: float = 10000.0,
        leverage: int = 1
    ) -> Dict[str, BacktestResults]:
        """
        Compare multiple strategies on the same data
        
        Parameters:
            symbol: Trading pair symbol
            ohlcv_data: Historical OHLCV data
            strategies: List of strategy configurations
            initial_balance: Starting balance in USDT
            leverage: Trading leverage
            
        Returns:
            Dictionary mapping strategy names to results
        """
        results = {}
        
        for i, strategy in enumerate(strategies):
            strategy_name = strategy.get('name', f'Strategy_{i+1}')
            self.logger.info(f"Running backtest for {strategy_name}")
            
            try:
                result = await self.run_backtest(
                    symbol=symbol,
                    ohlcv_data=ohlcv_data,
                    strategy_config=strategy,
                    use_llm=False,
                    initial_balance=initial_balance,
                    leverage=leverage
                )
                results[strategy_name] = result
                
            except Exception as e:
                self.logger.error(f"Error running {strategy_name}: {str(e)}")
                continue
        
        return results
    
    def analyze_results(self, results: BacktestResults) -> Dict:
        """
        Analyze backtest results and provide insights
        
        Parameters:
            results: Backtest results to analyze
            
        Returns:
            Analysis insights and recommendations
        """
        analysis = {
            'performance_grade': 'F',
            'strengths': [],
            'weaknesses': [],
            'recommendations': []
        }
        
        # Performance grading
        if results.win_rate >= 60 and results.profit_factor >= 1.5 and results.sharpe_ratio >= 1.0:
            analysis['performance_grade'] = 'A'
        elif results.win_rate >= 50 and results.profit_factor >= 1.2 and results.sharpe_ratio >= 0.5:
            analysis['performance_grade'] = 'B'
        elif results.win_rate >= 40 and results.profit_factor >= 1.0:
            analysis['performance_grade'] = 'C'
        elif results.total_pnl > 0:
            analysis['performance_grade'] = 'D'
        
        # Identify strengths
        if results.win_rate >= 55:
            analysis['strengths'].append(f"High win rate ({results.win_rate:.1f}%)")
        if results.profit_factor >= 1.5:
            analysis['strengths'].append(f"Strong profit factor ({results.profit_factor:.2f})")
        if results.sharpe_ratio >= 1.0:
            analysis['strengths'].append(f"Good risk-adjusted returns (Sharpe: {results.sharpe_ratio:.2f})")
        if results.max_drawdown_pct <= 5:
            analysis['strengths'].append(f"Low drawdown ({results.max_drawdown_pct:.1f}%)")
        
        # Identify weaknesses
        if results.win_rate < 40:
            analysis['weaknesses'].append(f"Low win rate ({results.win_rate:.1f}%)")
        if results.profit_factor < 1.0:
            analysis['weaknesses'].append(f"Poor profit factor ({results.profit_factor:.2f})")
        if results.max_drawdown_pct > 10:
            analysis['weaknesses'].append(f"High drawdown ({results.max_drawdown_pct:.1f}%)")
        if results.avg_trade_duration > 60:  # More than 1 hour for scalping
            analysis['weaknesses'].append(f"Long trade duration ({results.avg_trade_duration:.1f} min)")
        
        # Recommendations
        if results.win_rate < 50:
            analysis['recommendations'].append("Improve entry criteria to increase win rate")
        if results.profit_factor < 1.2:
            analysis['recommendations'].append("Optimize take-profit and stop-loss levels")
        if results.max_drawdown_pct > 8:
            analysis['recommendations'].append("Implement stricter risk management")
        if results.trades_per_day < 1:
            analysis['recommendations'].append("Increase signal frequency for more opportunities")
        
        return analysis
