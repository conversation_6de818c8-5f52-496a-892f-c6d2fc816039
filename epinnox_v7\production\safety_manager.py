"""
Production Safety Manager for Epinnox v7

Comprehensive safety system for live trading operations.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from utils.logger import get_logger
from exchanges.exchange_manager import get_exchange_manager
from llm.trading_engine import get_trading_engine
from core.risk_guard import RiskGuard

logger = get_logger()


class SafetyLevel(Enum):
    """Safety levels"""
    GREEN = "green"      # Normal operation
    YELLOW = "yellow"    # Caution - reduced trading
    ORANGE = "orange"    # Warning - limited trading
    RED = "red"          # Critical - trading halted


@dataclass
class SafetyCheck:
    """Safety check result"""
    name: str
    status: bool
    level: SafetyLevel
    message: str
    details: Dict[str, Any]
    timestamp: datetime


class ProductionSafetyManager:
    """Comprehensive safety management for production trading"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger()
        
        # Safety state
        self.current_safety_level = SafetyLevel.RED  # Start in safe mode
        self.safety_checks = {}
        self.last_safety_check = None
        
        # Emergency state
        self.emergency_stop_active = False
        self.emergency_reason = None
        
        # Managers
        self.exchange_manager = get_exchange_manager(config)
        self.trading_engine = get_trading_engine(config)
        self.risk_guard = RiskGuard(config)
        
        # Safety thresholds
        self.safety_thresholds = {
            'max_daily_loss_usd': config.get('production', {}).get('max_daily_loss_usd', 500),
            'max_drawdown_pct': config.get('risk_limits', {}).get('max_drawdown_pct', 3.0),
            'max_consecutive_losses': config.get('risk_limits', {}).get('max_consecutive_losses', 3),
            'max_latency_ms': 1000,
            'min_account_balance_usd': 1000,
            'max_error_rate_pct': 5.0
        }
        
        # Performance tracking
        self.daily_pnl = 0.0
        self.consecutive_losses = 0
        self.error_count = 0
        self.total_operations = 0
        
        self.logger.info("Production Safety Manager initialized")
    
    async def perform_comprehensive_safety_check(self) -> Dict[str, Any]:
        """Perform comprehensive safety check"""
        self.logger.info("Performing comprehensive safety check...")
        
        safety_checks = []
        
        # 1. Exchange connectivity check
        exchange_check = await self.check_exchange_connectivity()
        safety_checks.append(exchange_check)
        
        # 2. API credentials check
        credentials_check = await self.check_api_credentials()
        safety_checks.append(credentials_check)
        
        # 3. Account balance check
        balance_check = await self.check_account_balance()
        safety_checks.append(balance_check)
        
        # 4. Risk limits check
        risk_check = await self.check_risk_limits()
        safety_checks.append(risk_check)
        
        # 5. System performance check
        performance_check = await self.check_system_performance()
        safety_checks.append(performance_check)
        
        # 6. Configuration validation
        config_check = await self.check_configuration()
        safety_checks.append(config_check)
        
        # 7. Emergency systems check
        emergency_check = await self.check_emergency_systems()
        safety_checks.append(emergency_check)
        
        # 8. Market conditions check
        market_check = await self.check_market_conditions()
        safety_checks.append(market_check)
        
        # Determine overall safety level
        overall_level = self.determine_safety_level(safety_checks)
        
        # Update safety state
        self.current_safety_level = overall_level
        self.last_safety_check = datetime.now()
        
        # Store individual checks
        for check in safety_checks:
            self.safety_checks[check.name] = check
        
        # Create summary
        summary = {
            'overall_safety_level': overall_level.value,
            'timestamp': datetime.now().isoformat(),
            'checks_passed': sum(1 for check in safety_checks if check.status),
            'total_checks': len(safety_checks),
            'critical_issues': [check.message for check in safety_checks 
                              if not check.status and check.level == SafetyLevel.RED],
            'warnings': [check.message for check in safety_checks 
                        if not check.status and check.level in [SafetyLevel.ORANGE, SafetyLevel.YELLOW]],
            'ready_for_trading': overall_level in [SafetyLevel.GREEN, SafetyLevel.YELLOW],
            'checks': {check.name: {
                'status': check.status,
                'level': check.level.value,
                'message': check.message,
                'details': check.details
            } for check in safety_checks}
        }
        
        self.logger.info(f"Safety check complete. Level: {overall_level.value}")
        return summary
    
    async def check_exchange_connectivity(self) -> SafetyCheck:
        """Check exchange connectivity"""
        try:
            health_results = await self.exchange_manager.health_check_all()
            
            connected_exchanges = 0
            total_exchanges = len(health_results)
            
            for exchange_name, health in health_results.items():
                if health.get('status') == 'healthy':
                    connected_exchanges += 1
            
            if connected_exchanges == 0:
                return SafetyCheck(
                    name="exchange_connectivity",
                    status=False,
                    level=SafetyLevel.RED,
                    message="No exchanges connected",
                    details=health_results,
                    timestamp=datetime.now()
                )
            elif connected_exchanges < total_exchanges:
                return SafetyCheck(
                    name="exchange_connectivity",
                    status=True,
                    level=SafetyLevel.YELLOW,
                    message=f"Partial connectivity: {connected_exchanges}/{total_exchanges} exchanges",
                    details=health_results,
                    timestamp=datetime.now()
                )
            else:
                return SafetyCheck(
                    name="exchange_connectivity",
                    status=True,
                    level=SafetyLevel.GREEN,
                    message="All exchanges connected",
                    details=health_results,
                    timestamp=datetime.now()
                )
        
        except Exception as e:
            return SafetyCheck(
                name="exchange_connectivity",
                status=False,
                level=SafetyLevel.RED,
                message=f"Exchange connectivity check failed: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now()
            )
    
    async def check_api_credentials(self) -> SafetyCheck:
        """Check API credentials validity"""
        try:
            # Test API credentials by making authenticated calls
            authenticated_exchanges = 0
            total_exchanges = len(self.exchange_manager.exchanges)
            
            for exchange_name, exchange in self.exchange_manager.exchanges.items():
                if exchange.is_authenticated():
                    try:
                        # Test with a simple API call
                        await exchange.get_account_balance()
                        authenticated_exchanges += 1
                    except Exception as e:
                        self.logger.warning(f"Authentication test failed for {exchange_name}: {str(e)}")
            
            if authenticated_exchanges == 0:
                return SafetyCheck(
                    name="api_credentials",
                    status=False,
                    level=SafetyLevel.RED,
                    message="No valid API credentials",
                    details={'authenticated_exchanges': authenticated_exchanges},
                    timestamp=datetime.now()
                )
            elif authenticated_exchanges < total_exchanges:
                return SafetyCheck(
                    name="api_credentials",
                    status=True,
                    level=SafetyLevel.YELLOW,
                    message=f"Partial authentication: {authenticated_exchanges}/{total_exchanges}",
                    details={'authenticated_exchanges': authenticated_exchanges},
                    timestamp=datetime.now()
                )
            else:
                return SafetyCheck(
                    name="api_credentials",
                    status=True,
                    level=SafetyLevel.GREEN,
                    message="All API credentials valid",
                    details={'authenticated_exchanges': authenticated_exchanges},
                    timestamp=datetime.now()
                )
        
        except Exception as e:
            return SafetyCheck(
                name="api_credentials",
                status=False,
                level=SafetyLevel.RED,
                message=f"API credentials check failed: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now()
            )
    
    async def check_account_balance(self) -> SafetyCheck:
        """Check HTX Linear Perpetual Futures USDT margin balance"""
        try:
            total_usdt_margin = 0.0

            for exchange_name, exchange in self.exchange_manager.exchanges.items():
                if exchange.is_authenticated():
                    balances = await exchange.get_account_balance()

                    # Focus ONLY on USDT futures margin balance
                    for balance in balances:
                        currency = balance.currency.upper()

                        # Only count USDT futures margin
                        if 'USDT_FUTURES' in currency:
                            total_usdt_margin += balance.total

            # Minimum USDT margin for futures trading
            min_balance = 0.0  # $0 minimum - let the LLM do the work!
            
            if total_usdt_margin < min_balance:
                return SafetyCheck(
                    name="account_balance",
                    status=False,
                    level=SafetyLevel.RED,
                    message=f"Insufficient USDT margin: ${total_usdt_margin:.2f} < ${min_balance}",
                    details={'total_balance': total_usdt_margin, 'min_required': min_balance},
                    timestamp=datetime.now()
                )
            elif total_usdt_margin < min_balance * 2:
                return SafetyCheck(
                    name="account_balance",
                    status=True,
                    level=SafetyLevel.YELLOW,
                    message=f"Low USDT margin warning: ${total_usdt_margin:.2f}",
                    details={'total_balance': total_usdt_margin, 'min_required': min_balance},
                    timestamp=datetime.now()
                )
            else:
                return SafetyCheck(
                    name="account_balance",
                    status=True,
                    level=SafetyLevel.GREEN,
                    message=f"Sufficient USDT margin: ${total_usdt_margin:.2f}",
                    details={'total_balance': total_usdt_margin, 'min_required': min_balance},
                    timestamp=datetime.now()
                )
        
        except Exception as e:
            return SafetyCheck(
                name="account_balance",
                status=False,
                level=SafetyLevel.RED,
                message=f"Balance check failed: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now()
            )
    
    async def check_risk_limits(self) -> SafetyCheck:
        """Check risk management configuration"""
        try:
            risk_config = self.config.get('risk_limits', {})
            
            # Check required risk parameters
            required_params = [
                'max_daily_loss_usd',
                'max_drawdown_pct',
                'stop_loss_pct',
                'max_consecutive_losses'
            ]
            
            missing_params = []
            for param in required_params:
                if param not in risk_config:
                    missing_params.append(param)
            
            if missing_params:
                return SafetyCheck(
                    name="risk_limits",
                    status=False,
                    level=SafetyLevel.RED,
                    message=f"Missing risk parameters: {missing_params}",
                    details={'missing_params': missing_params},
                    timestamp=datetime.now()
                )
            
            # Check if risk limits are reasonable
            warnings = []
            
            if risk_config.get('max_daily_loss_usd', 0) > 1000:
                warnings.append("Daily loss limit > $1000 (high risk)")
            
            if risk_config.get('max_drawdown_pct', 0) > 5:
                warnings.append("Drawdown limit > 5% (high risk)")
            
            if risk_config.get('stop_loss_pct', 0) > 3:
                warnings.append("Stop loss > 3% (high risk)")
            
            if warnings:
                return SafetyCheck(
                    name="risk_limits",
                    status=True,
                    level=SafetyLevel.YELLOW,
                    message=f"Risk warnings: {'; '.join(warnings)}",
                    details={'warnings': warnings, 'config': risk_config},
                    timestamp=datetime.now()
                )
            else:
                return SafetyCheck(
                    name="risk_limits",
                    status=True,
                    level=SafetyLevel.GREEN,
                    message="Risk limits properly configured",
                    details={'config': risk_config},
                    timestamp=datetime.now()
                )
        
        except Exception as e:
            return SafetyCheck(
                name="risk_limits",
                status=False,
                level=SafetyLevel.RED,
                message=f"Risk limits check failed: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now()
            )
    
    async def check_system_performance(self) -> SafetyCheck:
        """Check system performance metrics"""
        try:
            import psutil
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # Check thresholds
            issues = []
            
            if cpu_percent > 80:
                issues.append(f"High CPU usage: {cpu_percent:.1f}%")
            
            if memory_percent > 80:
                issues.append(f"High memory usage: {memory_percent:.1f}%")
            
            if disk_percent > 90:
                issues.append(f"High disk usage: {disk_percent:.1f}%")
            
            details = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'disk_percent': disk_percent
            }
            
            if issues:
                return SafetyCheck(
                    name="system_performance",
                    status=False,
                    level=SafetyLevel.ORANGE,
                    message=f"Performance issues: {'; '.join(issues)}",
                    details=details,
                    timestamp=datetime.now()
                )
            else:
                return SafetyCheck(
                    name="system_performance",
                    status=True,
                    level=SafetyLevel.GREEN,
                    message="System performance normal",
                    details=details,
                    timestamp=datetime.now()
                )
        
        except Exception as e:
            return SafetyCheck(
                name="system_performance",
                status=False,
                level=SafetyLevel.YELLOW,
                message=f"Performance check failed: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now()
            )
    
    async def check_configuration(self) -> SafetyCheck:
        """Check configuration validity"""
        try:
            # Check critical configuration parameters
            critical_checks = []
            
            # Environment check
            env = self.config.get('environment', 'development')
            if env != 'production':
                critical_checks.append(f"Environment not set to production: {env}")
            
            # Dry run check
            dry_run = self.config.get('dry_run', True)
            if dry_run:
                critical_checks.append("Dry run mode is enabled")
            
            # Debug mode check
            debug_mode = self.config.get('debug_mode', True)
            if debug_mode:
                critical_checks.append("Debug mode is enabled")
            
            if critical_checks:
                return SafetyCheck(
                    name="configuration",
                    status=False,
                    level=SafetyLevel.RED,
                    message=f"Configuration issues: {'; '.join(critical_checks)}",
                    details={'issues': critical_checks},
                    timestamp=datetime.now()
                )
            else:
                return SafetyCheck(
                    name="configuration",
                    status=True,
                    level=SafetyLevel.GREEN,
                    message="Configuration valid for production",
                    details={'environment': env, 'dry_run': dry_run, 'debug_mode': debug_mode},
                    timestamp=datetime.now()
                )
        
        except Exception as e:
            return SafetyCheck(
                name="configuration",
                status=False,
                level=SafetyLevel.RED,
                message=f"Configuration check failed: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now()
            )
    
    async def check_emergency_systems(self) -> SafetyCheck:
        """Check emergency stop systems"""
        try:
            # Test emergency stop functionality
            emergency_systems = []
            
            # Check if risk guard has emergency stop
            if hasattr(self.risk_guard, 'activate_emergency_stop'):
                emergency_systems.append("risk_guard_emergency_stop")
            
            # Check if trading engine has emergency stop
            if hasattr(self.trading_engine, 'emergency_stop'):
                emergency_systems.append("trading_engine_emergency_stop")
            
            # Check if exchange manager has emergency stop
            if hasattr(self.exchange_manager, 'emergency_stop_all_trading'):
                emergency_systems.append("exchange_manager_emergency_stop")
            
            if len(emergency_systems) >= 2:
                return SafetyCheck(
                    name="emergency_systems",
                    status=True,
                    level=SafetyLevel.GREEN,
                    message="Emergency systems operational",
                    details={'available_systems': emergency_systems},
                    timestamp=datetime.now()
                )
            else:
                return SafetyCheck(
                    name="emergency_systems",
                    status=False,
                    level=SafetyLevel.RED,
                    message="Insufficient emergency systems",
                    details={'available_systems': emergency_systems},
                    timestamp=datetime.now()
                )
        
        except Exception as e:
            return SafetyCheck(
                name="emergency_systems",
                status=False,
                level=SafetyLevel.RED,
                message=f"Emergency systems check failed: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now()
            )
    
    async def check_market_conditions(self) -> SafetyCheck:
        """Check current market conditions"""
        try:
            # Get market data for main trading symbols
            symbols = self.config.get('trading_symbols', ['BTC/USDT'])
            market_issues = []
            
            for symbol in symbols:
                try:
                    market_data = await self.exchange_manager.get_market_data(symbol)
                    
                    # Check spread
                    spread_pct = market_data.spread_pct
                    if spread_pct > 0.5:  # 0.5% spread threshold
                        market_issues.append(f"{symbol} spread too wide: {spread_pct:.3f}%")
                    
                    # Check volatility (simplified)
                    change_24h = abs(market_data.change_pct_24h)
                    if change_24h > 10:  # 10% daily change threshold
                        market_issues.append(f"{symbol} high volatility: {change_24h:.1f}%")
                
                except Exception as e:
                    market_issues.append(f"Failed to get data for {symbol}: {str(e)}")
            
            if market_issues:
                return SafetyCheck(
                    name="market_conditions",
                    status=True,
                    level=SafetyLevel.YELLOW,
                    message=f"Market warnings: {'; '.join(market_issues)}",
                    details={'issues': market_issues},
                    timestamp=datetime.now()
                )
            else:
                return SafetyCheck(
                    name="market_conditions",
                    status=True,
                    level=SafetyLevel.GREEN,
                    message="Market conditions normal",
                    details={'symbols_checked': symbols},
                    timestamp=datetime.now()
                )
        
        except Exception as e:
            return SafetyCheck(
                name="market_conditions",
                status=False,
                level=SafetyLevel.YELLOW,
                message=f"Market conditions check failed: {str(e)}",
                details={'error': str(e)},
                timestamp=datetime.now()
            )
    
    def determine_safety_level(self, checks: List[SafetyCheck]) -> SafetyLevel:
        """Determine overall safety level from individual checks"""
        # If any check is RED, overall is RED
        if any(check.level == SafetyLevel.RED for check in checks):
            return SafetyLevel.RED
        
        # If any check is ORANGE, overall is ORANGE
        if any(check.level == SafetyLevel.ORANGE for check in checks):
            return SafetyLevel.ORANGE
        
        # If any check is YELLOW, overall is YELLOW
        if any(check.level == SafetyLevel.YELLOW for check in checks):
            return SafetyLevel.YELLOW
        
        # All checks are GREEN
        return SafetyLevel.GREEN
    
    async def activate_emergency_stop(self, reason: str) -> Dict[str, Any]:
        """Activate emergency stop"""
        self.emergency_stop_active = True
        self.emergency_reason = reason
        self.current_safety_level = SafetyLevel.RED
        
        self.logger.critical(f"EMERGENCY STOP ACTIVATED: {reason}")
        
        # Stop trading engine
        if self.trading_engine:
            await self.trading_engine.emergency_stop()
        
        # Emergency stop all exchanges
        if self.exchange_manager:
            exchange_results = await self.exchange_manager.emergency_stop_all_trading()
        else:
            exchange_results = {}
        
        return {
            'status': 'emergency_stop_activated',
            'reason': reason,
            'timestamp': datetime.now().isoformat(),
            'exchange_results': exchange_results
        }
    
    def is_safe_to_trade(self) -> Tuple[bool, str]:
        """Check if it's safe to trade"""
        if self.emergency_stop_active:
            return False, f"Emergency stop active: {self.emergency_reason}"
        
        if self.current_safety_level == SafetyLevel.RED:
            return False, "Safety level RED - trading not permitted"
        
        if self.current_safety_level == SafetyLevel.ORANGE:
            return False, "Safety level ORANGE - trading restricted"
        
        return True, f"Safety level {self.current_safety_level.value} - trading permitted"
    
    def get_safety_status(self) -> Dict[str, Any]:
        """Get current safety status"""
        return {
            'current_safety_level': self.current_safety_level.value,
            'emergency_stop_active': self.emergency_stop_active,
            'emergency_reason': self.emergency_reason,
            'last_safety_check': self.last_safety_check.isoformat() if self.last_safety_check else None,
            'safe_to_trade': self.is_safe_to_trade()[0],
            'safety_message': self.is_safe_to_trade()[1],
            'checks_count': len(self.safety_checks),
            'timestamp': datetime.now().isoformat()
        }
