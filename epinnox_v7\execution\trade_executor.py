from typing import Dict, Optional
import asyncio
import ccxt.async_support as ccxt
from datetime import datetime
import time
from utils.enums import ActionType, PositionSide
from utils.logger import get_logger, log_trade, log_error, log_risk

class TradeExecutor:
    """Handles order execution and position management"""
    
    def __init__(self, exchange: ccxt.Exchange, config: Dict):
        self.exchange = exchange
        self.config = config
        self.logger = get_logger()
        
        # Check if we're in dry run mode
        self.live_mode = config.get('live_mode', True)
        if not self.live_mode:
            self.logger.info("Running in DRY RUN mode - orders will be simulated")
        
        # Track pending orders
        self.pending_orders = {}
        
        # Initialize dry run state
        self._dry_run_positions = {}
        
    def _log_dry_run(self, msg: str):
        """Log dry run actions with special formatting"""
        self.logger.info(f"[DRY RUN] {msg}")
        
    async def _simulate_order(self, symbol: str, side: str, amount: float, 
                            order_type: str, price: Optional[float] = None) -> Dict:
        """Simulate an order execution in dry run mode"""
        order_id = f"dry_run_{int(datetime.now().timestamp())}_{side}"
          # Get current price if not provided
        if not price:
            ticker = await self.exchange.fetch_ticker(symbol)
            price = ticker['last']
            
        order = {
            'id': order_id,
            'symbol': symbol,
            'side': side,
            'type': order_type,
            'amount': amount,
            'price': price,
            'timestamp': datetime.now().timestamp() * 1000,
            'status': 'closed'
        }
        self._log_dry_run(
            f"Simulated {order_type} {side} order: {symbol} "
            f"Amount: {amount} @ {price}"
        )
        
        return order
        
    async def execute_trade(self,
                      symbol: str,
                      action: Dict,
                      current_position: Optional[Dict] = None) -> Dict:
        """
        Execute a trade based on LLM decision
        
        Parameters
        ----------
        symbol : str
            Trading pair symbol
        action : Dict
            Trade action from LLM including:
            - action: str (long/short/close/hold)
            - entry_price: float
            - stop_loss: float
            - take_profit: float
            - position_size: float
        current_position : Optional[Dict]
            Current position data if any
            
        Returns
        -------
        Dict
            Execution result including:
            - success: bool
            - order_id: Optional[str]
            - error: Optional[str]
        """
        try:
            # Log debug info if configured
            if self.config.get('debug_mode', False):
                self.logger.info(
                    f"Executing trade: {symbol} | Action: {action['action']} | "
                    f"Size: {action.get('position_size', 0)} | "
                    f"Entry: {action.get('entry_price', 0)}"
                )
            
            # Handle different action types
            if action['action'] == ActionType.HOLD.value:
                return {'success': True, 'action': 'hold'}
                
            elif action['action'] == ActionType.CLOSE.value:
                if not current_position or current_position['side'] == PositionSide.NONE.value:
                    return {'success': True, 'action': 'close', 'message': 'No position to close'}
                    
                if self.live_mode:
                    return await self._close_position(symbol, current_position)
                else:
                    self._log_dry_run(f"Closing position: {symbol}")
                    return {'success': True, 'action': 'close', 'dry_run': True}
                    
            elif action['action'] in [ActionType.LONG.value, ActionType.SHORT.value]:
                # Close existing position if it exists and is opposite side
                if current_position and current_position['side'] != PositionSide.NONE.value:
                    if (
                        (action['action'] == ActionType.LONG.value and current_position['side'] == PositionSide.SHORT.value) or
                        (action['action'] == ActionType.SHORT.value and current_position['side'] == PositionSide.LONG.value)
                    ):
                        if self.live_mode:
                            await self._close_position(symbol, current_position)
                        else:
                            self._log_dry_run(f"Closing opposite position: {symbol}")
                            
                # Open new position
                if self.live_mode:
                    return await self._open_position(symbol, action)
                else:
                    return await self._simulate_order(
                        symbol=symbol,
                        side="buy" if action['action'] == ActionType.LONG.value else "sell",
                        amount=action['position_size'],
                        order_type="market",
                        price=action['entry_price']
                    )
                
            else:
                raise ValueError(f"Invalid action type: {action['action']}")
                
        except Exception as e:
            log_error(f"Trade execution error: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
            
    async def _open_position(self, symbol: str, action: Dict) -> Dict:
        """Open a new position with enhanced error handling and slippage protection"""
        try:
            # 1. Check if we have sufficient balance
            try:
                balance = await self.exchange.fetch_balance()
                free_margin = balance['free']['USDT']
                
                required_margin = action['position_size'] / action['leverage']
                if required_margin > free_margin:
                    log_risk(f"Insufficient margin: required {required_margin:.2f} USDT, available {free_margin:.2f} USDT")
                    return {
                        'success': False,
                        'error': f"Insufficient margin: {free_margin:.2f} < {required_margin:.2f}",
                        'error_type': 'INSUFFICIENT_BALANCE'
                    }
            except ccxt.InsufficientFunds as e:
                log_risk(f"Insufficient funds: {str(e)}")
                return {
                    'success': False,
                    'error': f"Insufficient funds: {str(e)}",
                    'error_type': 'INSUFFICIENT_FUNDS'
                }
            except Exception as e:
                log_error(f"Balance check error: {str(e)}")
            
            # 2. Check for slippage before executing
            if action['action'] == ActionType.LONG.value:
                side = 'buy'
            else:
                side = 'sell'
                
            slippage_ok, current_price, slippage_pct = await self._check_slippage(
                symbol, action['entry_price'], side
            )
            
            if not slippage_ok:
                log_risk(f"Excessive slippage detected ({slippage_pct:.2f}%) - trade canceled")
                return {
                    'success': False,
                    'error': f"Excessive slippage: {slippage_pct:.2f}%",
                    'error_type': 'SLIPPAGE'
                }
                
            # 3. Set leverage
            try:
                await self.exchange.set_leverage(action['leverage'], symbol)
            except ccxt.ExchangeError as e:
                log_error(f"Leverage setting error: {str(e)}")
                # Continue anyway, may just be using existing leverage
            
            # 4. Place the order with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    order = await self.exchange.create_order(
                        symbol=symbol,
                        type='market',
                        side=side,
                        amount=action['position_size']
                    )
                    
                    # 5. Set stop loss and take profit orders
                    try:
                        # Place stop loss
                        await self.exchange.create_order(
                            symbol=symbol,
                            type='stop',
                            side='sell' if side == 'buy' else 'buy',
                            amount=action['position_size'],
                            price=action['stop_loss'],
                            params={'stopPrice': action['stop_loss']}
                        )
                        
                        # Place take profit
                        await self.exchange.create_order(
                            symbol=symbol,
                            type='limit',
                            side='sell' if side == 'buy' else 'buy',
                            amount=action['position_size'],
                            price=action['take_profit']
                        )
                    except Exception as e:
                        log_error(f"Error setting SL/TP: {str(e)}")
                        # Main position is still open, so continue
                    
                    # Log trade
                    action_str = "LONG" if side == 'buy' else "SHORT"
                    log_trade(
                        f"Opened {action_str} position: {symbol} | "
                        f"Size: {action['position_size']} | "
                        f"Entry: {current_price} | "
                        f"SL: {action['stop_loss']} | "
                        f"TP: {action['take_profit']}"
                    )
                    
                    return {
                        'success': True,
                        'action': action['action'],
                        'order_id': order['id'],
                        'execution_price': current_price,
                        'slippage_pct': slippage_pct
                    }
                
                except ccxt.InsufficientFunds as e:
                    log_risk(f"Insufficient funds: {str(e)}")
                    return {
                        'success': False,
                        'error': f"Insufficient funds: {str(e)}",
                        'error_type': 'INSUFFICIENT_FUNDS'
                    }
                    
                except ccxt.NetworkError as e:
                    if attempt < max_retries - 1:
                        log_error(f"Network error (attempt {attempt + 1}/{max_retries}): {str(e)}")
                        await asyncio.sleep(1)
                    else:
                        raise
                        
                except ccxt.ExchangeError as e:
                    if 'rate limit' in str(e).lower():
                        if attempt < max_retries - 1:
                            log_error(f"Rate limit hit (attempt {attempt + 1}/{max_retries}): {str(e)}")
                            await asyncio.sleep(2)
                        else:
                            raise
                    else:
                        raise
            
            raise Exception("Maximum retry attempts exceeded")
            
        except Exception as e:
            log_error(f"Error opening position: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'error_type': 'EXECUTION_ERROR'
            }
            
    async def _close_position(self, symbol: str, position: Dict) -> Dict:
        """Close an existing position with enhanced error handling"""
        try:
            # Calculate close parameters
            side = "sell" if position['side'] == PositionSide.LONG.value else "buy"
            amount = position['size']
            
            # Cancel any pending orders first
            try:
                await self._cancel_pending_orders(symbol)
            except Exception as e:
                log_error(f"Error cancelling pending orders: {str(e)}")
                # Continue with position close anyway

            # Log the attempt
            self.logger.info(f"Attempting to close {position['side']} position: {symbol} | Size: {amount}")
            
            if not self.live_mode:
                # Dry run: simulate the close order
                close_order = await self._simulate_order(
                    symbol, side, amount, 'market'
                )
            else:
                # Live mode with retry logic
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        # Place real close order
                        close_order = await self.exchange.create_order(
                            symbol=symbol,
                            type='market',
                            side=side,
                            amount=amount
                        )
                        break
                    except ccxt.InsufficientFunds as e:
                        log_risk(f"Insufficient funds when closing: {str(e)}")
                        return {
                            'success': False,
                            'error': f"Insufficient funds: {str(e)}",
                            'error_type': 'INSUFFICIENT_FUNDS'
                        }
                    except ccxt.NetworkError as e:
                        if attempt < max_retries - 1:
                            log_error(f"Network error during close (attempt {attempt + 1}/{max_retries}): {str(e)}")
                            await asyncio.sleep(1)
                        else:
                            raise
                    except ccxt.ExchangeError as e:
                        if 'rate limit' in str(e).lower():
                            if attempt < max_retries - 1:
                                log_error(f"Rate limit hit during close (attempt {attempt + 1}/{max_retries}): {str(e)}")
                                await asyncio.sleep(2)
                            else:
                                raise
                        else:
                            raise
                else:
                    raise Exception("Maximum retry attempts exceeded when closing position")
            
            # Get execution price if available
            try:
                ticker = await self.exchange.fetch_ticker(symbol)
                execution_price = ticker['last']
            except Exception:
                execution_price = None
            
            # Log the close
            log_trade(
                f"Closed {position['side']} position: {symbol} | "
                f"Size: {amount} | PnL: {position.get('pnl_usd', 0):.2f} USDT | "
                f"Price: {execution_price if execution_price else 'unknown'}"
            )
            
            return {
                'success': True,
                'action': 'close',
                'order_id': close_order['id'],
                'execution_price': execution_price
            }
            
        except ccxt.OrderNotFound as e:
            log_error(f"Order not found when closing: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'error_type': 'ORDER_NOT_FOUND'
            }
        except Exception as e:
            log_error(f"Error closing position: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'error_type': 'EXECUTION_ERROR'
            }
            
    async def _cancel_pending_orders(self, symbol: str):
        """Cancel all pending orders for a symbol"""
        try:
            open_orders = await self.exchange.fetch_open_orders(symbol)
            for order in open_orders:
                await self.exchange.cancel_order(order['id'], symbol)
        except Exception as e:
            log_error(f"Error canceling orders: {str(e)}")
            
    async def modify_stop_loss(self, symbol: str, new_stop_price: float):
        """Modify stop loss price for trailing stops"""
        try:
            # Find existing stop order
            open_orders = await self.exchange.fetch_open_orders(symbol)
            stop_orders = [o for o in open_orders if o['type'] == 'stop']
            
            if not stop_orders:
                return
                
            # Cancel existing stop
            old_stop = stop_orders[0]
            await self.exchange.cancel_order(old_stop['id'], symbol)
            
            # Place new stop order
            new_stop = await self.exchange.create_order(
                symbol=symbol,
                type='stop',
                side=old_stop['side'],
                amount=old_stop['amount'],
                price=new_stop_price,
                params={'stopPrice': new_stop_price}
            )
            
            return new_stop
            
        except Exception as e:
            log_error(f"Error modifying stop loss: {str(e)}")
            
    async def get_open_orders(self, symbol: str) -> list:
        """Get all open orders for a symbol"""
        try:
            return await self.exchange.fetch_open_orders(symbol)
        except Exception as e:
            log_error(f"Error fetching open orders: {str(e)}")
            return []
            
    async def close(self):
        """Clean up resources"""
        # Cancel all pending orders before shutdown
        for symbol in self.pending_orders:
            await self._cancel_pending_orders(symbol)
            
    async def _check_slippage(self, symbol: str, expected_price: float, side: str) -> tuple:
        """
        Check if current price has slipped too far from expected price
        
        Returns
        -------
        tuple
            (is_slippage_acceptable, current_price, slippage_pct)
        """
        try:
            # Get current price
            ticker = await self.exchange.fetch_ticker(symbol)
            current_price = ticker['bid'] if side == 'sell' else ticker['ask']
            
            # Calculate slippage percentage
            if side == 'buy':
                # For buys, slippage is positive if current price is higher (worse)
                slippage_pct = (current_price - expected_price) / expected_price * 100
            else:
                # For sells, slippage is positive if current price is lower (worse)
                slippage_pct = (expected_price - current_price) / expected_price * 100
                
            # Get max allowed slippage from config
            max_slippage = self.config.get('max_slippage_pct', 0.5)  # Default 0.5%
            
            # Log slippage
            if slippage_pct > 0:
                self.logger.warning(f"Price slippage for {side} {symbol}: {slippage_pct:.2f}% (max: {max_slippage:.2f}%)")
            
            return slippage_pct <= max_slippage, current_price, slippage_pct
            
        except Exception as e:
            log_error(f"Error checking slippage: {str(e)}")
            return True, expected_price, 0  # Allow trade to proceed on error
