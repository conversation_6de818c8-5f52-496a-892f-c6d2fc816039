"""
Trading Controls Widget for Epinnox v7

Production trading controls with safety features and confirmations.
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QComboBox, QDoubleSpinBox,
    QSpinBox, QGroupBox, QMessageBox, QProgressBar,
    QCheckBox, QFrame, QTextEdit, QTabWidget
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPalette, QColor
from typing import Dict, Any
from datetime import datetime

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from auth.decorators import require_auth, admin_required
from auth.authentication import session_manager
from utils.logger import get_logger

logger = get_logger()


class TradingControlsWidget(QWidget):
    """Production trading controls with safety features"""
    
    # Signals
    trading_started = pyqtSignal()
    trading_stopped = pyqtSignal()
    emergency_stop_triggered = pyqtSignal()
    manual_trade_requested = pyqtSignal(dict)
    strategy_parameters_changed = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger()
        
        # State
        self.trading_active = False
        self.emergency_stop_active = False
        self.dry_run_mode = True
        
        # UI Components
        self.setup_ui()
        self.setup_styling()
        self.setup_connections()
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(1000)  # Update every second
        
        self.logger.info("Trading Controls Widget initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Title
        title = QLabel("Trading Controls")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        
        # Main Controls Tab
        self.main_controls_tab = self.create_main_controls_tab()
        self.tab_widget.addTab(self.main_controls_tab, "Main Controls")
        
        # Manual Trading Tab
        self.manual_trading_tab = self.create_manual_trading_tab()
        self.tab_widget.addTab(self.manual_trading_tab, "Manual Trading")
        
        # Strategy Parameters Tab
        self.strategy_params_tab = self.create_strategy_params_tab()
        self.tab_widget.addTab(self.strategy_params_tab, "Strategy Params")
        
        layout.addWidget(self.tab_widget)
        
        # Status bar
        self.status_bar = self.create_status_bar()
        layout.addWidget(self.status_bar)
    
    def create_main_controls_tab(self) -> QWidget:
        """Create main trading controls tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Trading Mode Group
        mode_group = QGroupBox("Trading Mode")
        mode_layout = QVBoxLayout(mode_group)
        
        self.dry_run_checkbox = QCheckBox("Dry Run Mode (Safe)")
        self.dry_run_checkbox.setChecked(True)
        self.dry_run_checkbox.stateChanged.connect(self.on_dry_run_changed)
        mode_layout.addWidget(self.dry_run_checkbox)
        
        # Mode warning
        self.mode_warning = QLabel("⚠️ LIVE TRADING MODE - Real money at risk!")
        self.mode_warning.setStyleSheet("color: red; font-weight: bold; background: yellow; padding: 5px;")
        self.mode_warning.hide()
        mode_layout.addWidget(self.mode_warning)
        
        layout.addWidget(mode_group)
        
        # Main Trading Controls
        controls_group = QGroupBox("Trading Controls")
        controls_layout = QGridLayout(controls_group)
        
        # Start Trading Button
        self.start_button = QPushButton("🚀 Start Trading")
        self.start_button.setMinimumHeight(50)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.start_button.clicked.connect(self.start_trading)
        controls_layout.addWidget(self.start_button, 0, 0)
        
        # Stop Trading Button
        self.stop_button = QPushButton("⏹️ Stop Trading")
        self.stop_button.setMinimumHeight(50)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.stop_button.clicked.connect(self.stop_trading)
        self.stop_button.setEnabled(False)
        controls_layout.addWidget(self.stop_button, 0, 1)
        
        # Emergency Stop Button (Admin Only)
        self.emergency_button = QPushButton("🛑 EMERGENCY STOP")
        self.emergency_button.setMinimumHeight(50)
        self.emergency_button.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border: 3px solid #b71c1c;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #b71c1c;
                border-color: #8e0000;
            }
        """)
        self.emergency_button.clicked.connect(self.emergency_stop)
        controls_layout.addWidget(self.emergency_button, 1, 0, 1, 2)
        
        # Show/hide emergency button based on role
        try:
            if session_manager.is_authenticated():
                from auth.models import UserRole
                if not session_manager.has_role(UserRole.ADMIN):
                    self.emergency_button.hide()
        except:
            # If authentication not available, hide emergency button
            self.emergency_button.hide()
        
        layout.addWidget(controls_group)
        
        # Trading Status
        status_group = QGroupBox("Status")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("Status: Stopped")
        self.status_label.setFont(QFont("Arial", 12, QFont.Bold))
        status_layout.addWidget(self.status_label)
        
        self.uptime_label = QLabel("Uptime: 00:00:00")
        status_layout.addWidget(self.uptime_label)
        
        self.last_action_label = QLabel("Last Action: None")
        status_layout.addWidget(self.last_action_label)
        
        layout.addWidget(status_group)
        
        return widget
    
    def create_manual_trading_tab(self) -> QWidget:
        """Create manual trading tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Manual Trade Group
        trade_group = QGroupBox("Manual Trade Entry")
        trade_layout = QGridLayout(trade_group)
        
        # Symbol selection
        trade_layout.addWidget(QLabel("Symbol:"), 0, 0)
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems(["BTC/USDT:USDT", "ETH/USDT:USDT", "DOGE/USDT:USDT"])
        trade_layout.addWidget(self.symbol_combo, 0, 1)
        
        # Side selection
        trade_layout.addWidget(QLabel("Side:"), 1, 0)
        self.side_combo = QComboBox()
        self.side_combo.addItems(["Long", "Short"])
        trade_layout.addWidget(self.side_combo, 1, 1)
        
        # Quantity
        trade_layout.addWidget(QLabel("Quantity:"), 2, 0)
        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setRange(0.001, 1000.0)
        self.quantity_spin.setDecimals(3)
        self.quantity_spin.setValue(0.1)
        trade_layout.addWidget(self.quantity_spin, 2, 1)
        
        # Leverage
        trade_layout.addWidget(QLabel("Leverage:"), 3, 0)
        self.leverage_spin = QSpinBox()
        self.leverage_spin.setRange(1, 100)
        self.leverage_spin.setValue(3)
        trade_layout.addWidget(self.leverage_spin, 3, 1)
        
        # Stop Loss
        trade_layout.addWidget(QLabel("Stop Loss %:"), 4, 0)
        self.stop_loss_spin = QDoubleSpinBox()
        self.stop_loss_spin.setRange(0.1, 50.0)
        self.stop_loss_spin.setDecimals(1)
        self.stop_loss_spin.setValue(2.0)
        trade_layout.addWidget(self.stop_loss_spin, 4, 1)
        
        # Take Profit
        trade_layout.addWidget(QLabel("Take Profit %:"), 5, 0)
        self.take_profit_spin = QDoubleSpinBox()
        self.take_profit_spin.setRange(0.1, 100.0)
        self.take_profit_spin.setDecimals(1)
        self.take_profit_spin.setValue(4.0)
        trade_layout.addWidget(self.take_profit_spin, 5, 1)
        
        # Risk calculation
        self.risk_label = QLabel("Estimated Risk: $0.00")
        self.risk_label.setStyleSheet("font-weight: bold; color: #ff6600;")
        trade_layout.addWidget(self.risk_label, 6, 0, 1, 2)
        
        # Execute button
        self.execute_button = QPushButton("Execute Trade")
        self.execute_button.setMinimumHeight(40)
        self.execute_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        self.execute_button.clicked.connect(self.execute_manual_trade)
        trade_layout.addWidget(self.execute_button, 7, 0, 1, 2)
        
        layout.addWidget(trade_group)
        
        # Connect signals for risk calculation
        self.quantity_spin.valueChanged.connect(self.calculate_risk)
        self.leverage_spin.valueChanged.connect(self.calculate_risk)
        self.stop_loss_spin.valueChanged.connect(self.calculate_risk)
        
        return widget
    
    def create_strategy_params_tab(self) -> QWidget:
        """Create strategy parameters tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Strategy Parameters Group
        params_group = QGroupBox("Live Strategy Parameters")
        params_layout = QGridLayout(params_group)
        
        # Position Size
        params_layout.addWidget(QLabel("Position Size %:"), 0, 0)
        self.position_size_spin = QDoubleSpinBox()
        self.position_size_spin.setRange(0.1, 10.0)
        self.position_size_spin.setDecimals(1)
        self.position_size_spin.setValue(2.0)
        params_layout.addWidget(self.position_size_spin, 0, 1)
        
        # Max Leverage
        params_layout.addWidget(QLabel("Max Leverage:"), 1, 0)
        self.max_leverage_spin = QSpinBox()
        self.max_leverage_spin.setRange(1, 100)
        self.max_leverage_spin.setValue(3)
        params_layout.addWidget(self.max_leverage_spin, 1, 1)
        
        # Confidence Threshold
        params_layout.addWidget(QLabel("Confidence Threshold:"), 2, 0)
        self.confidence_spin = QDoubleSpinBox()
        self.confidence_spin.setRange(0.1, 1.0)
        self.confidence_spin.setDecimals(2)
        self.confidence_spin.setValue(0.65)
        params_layout.addWidget(self.confidence_spin, 2, 1)
        
        # Min Spread
        params_layout.addWidget(QLabel("Min Spread %:"), 3, 0)
        self.min_spread_spin = QDoubleSpinBox()
        self.min_spread_spin.setRange(0.001, 1.0)
        self.min_spread_spin.setDecimals(3)
        self.min_spread_spin.setValue(0.002)
        params_layout.addWidget(self.min_spread_spin, 3, 1)
        
        # Apply button
        self.apply_params_button = QPushButton("Apply Parameters")
        self.apply_params_button.setMinimumHeight(40)
        self.apply_params_button.clicked.connect(self.apply_strategy_parameters)
        params_layout.addWidget(self.apply_params_button, 4, 0, 1, 2)
        
        layout.addWidget(params_group)
        
        # Parameters status
        self.params_status = QTextEdit()
        self.params_status.setMaximumHeight(100)
        self.params_status.setPlainText("Strategy parameters ready for adjustment...")
        layout.addWidget(self.params_status)
        
        return widget
    
    def create_status_bar(self) -> QWidget:
        """Create status bar"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(frame)
        
        # Connection status
        self.connection_status = QLabel("🔴 Disconnected")
        layout.addWidget(self.connection_status)
        
        # Trading mode
        self.mode_status = QLabel("📊 Dry Run")
        layout.addWidget(self.mode_status)
        
        # Last update
        self.last_update = QLabel("Last Update: Never")
        layout.addWidget(self.last_update)
        
        layout.addStretch()
        
        return frame
    
    def setup_styling(self):
        """Setup widget styling"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #2196F3;
            }
        """)
    
    def setup_connections(self):
        """Setup signal connections"""
        pass
    
    @require_auth
    def start_trading(self, checked=False):
        """Start trading with confirmation"""
        if not self.dry_run_mode:
            reply = QMessageBox.question(
                self,
                'Start Live Trading',
                '⚠️ You are about to start LIVE TRADING with real money!\n\n'
                'Are you absolutely sure you want to proceed?',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
        
        self.trading_active = True
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_label.setText("Status: Running")
        self.status_label.setStyleSheet("color: green; font-weight: bold;")
        
        self.trading_started.emit()
        self.logger.info("Trading started")
        
        self.last_action_label.setText(f"Last Action: Started at {datetime.now().strftime('%H:%M:%S')}")
    
    @require_auth
    def stop_trading(self, checked=False):
        """Stop trading with confirmation"""
        reply = QMessageBox.question(
            self,
            'Stop Trading',
            'Are you sure you want to stop trading?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.Yes:
            self.trading_active = False
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText("Status: Stopped")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            
            self.trading_stopped.emit()
            self.logger.info("Trading stopped")
            
            self.last_action_label.setText(f"Last Action: Stopped at {datetime.now().strftime('%H:%M:%S')}")
    
    @admin_required
    def emergency_stop(self):
        """Emergency stop with confirmation"""
        reply = QMessageBox.critical(
            self,
            'EMERGENCY STOP',
            '🛑 EMERGENCY STOP\n\n'
            'This will immediately:\n'
            '• Stop all trading\n'
            '• Close all positions\n'
            '• Cancel all orders\n\n'
            'Are you sure?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.emergency_stop_active = True
            self.trading_active = False
            
            # Disable all controls
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.emergency_button.setText("🛑 EMERGENCY ACTIVE")
            self.emergency_button.setEnabled(False)
            
            self.status_label.setText("Status: EMERGENCY STOP")
            self.status_label.setStyleSheet("color: white; background-color: red; font-weight: bold; padding: 5px;")
            
            self.emergency_stop_triggered.emit()
            self.logger.critical("EMERGENCY STOP ACTIVATED")
            
            self.last_action_label.setText(f"Last Action: EMERGENCY STOP at {datetime.now().strftime('%H:%M:%S')}")
    
    def on_dry_run_changed(self, state):
        """Handle dry run mode change"""
        self.dry_run_mode = state == Qt.Checked
        
        if self.dry_run_mode:
            self.mode_warning.hide()
            self.mode_status.setText("📊 Dry Run")
            self.mode_status.setStyleSheet("color: green;")
        else:
            self.mode_warning.show()
            self.mode_status.setText("💰 LIVE MODE")
            self.mode_status.setStyleSheet("color: red; font-weight: bold;")
    
    @require_auth
    def execute_manual_trade(self):
        """Execute manual trade"""
        trade_data = {
            'symbol': self.symbol_combo.currentText(),
            'side': self.side_combo.currentText().lower(),
            'quantity': self.quantity_spin.value(),
            'leverage': self.leverage_spin.value(),
            'stop_loss_pct': self.stop_loss_spin.value(),
            'take_profit_pct': self.take_profit_spin.value(),
            'dry_run': self.dry_run_mode,
            'timestamp': datetime.now().isoformat()
        }
        
        # Confirmation dialog
        risk_amount = self.calculate_risk_amount()
        
        reply = QMessageBox.question(
            self,
            'Confirm Manual Trade',
            f"Execute {trade_data['side']} trade?\n\n"
            f"Symbol: {trade_data['symbol']}\n"
            f"Quantity: {trade_data['quantity']}\n"
            f"Leverage: {trade_data['leverage']}x\n"
            f"Estimated Risk: ${risk_amount:.2f}\n"
            f"Mode: {'DRY RUN' if self.dry_run_mode else 'LIVE TRADING'}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.manual_trade_requested.emit(trade_data)
            self.logger.info(f"Manual trade requested: {trade_data}")
            
            QMessageBox.information(
                self,
                'Trade Submitted',
                f"Trade submitted successfully!\n\n"
                f"{'[DRY RUN] ' if self.dry_run_mode else ''}Trade will be executed shortly."
            )
    
    def calculate_risk(self):
        """Calculate and display trade risk"""
        risk_amount = self.calculate_risk_amount()
        self.risk_label.setText(f"Estimated Risk: ${risk_amount:.2f}")
        
        # Color code based on risk level
        if risk_amount > 500:
            self.risk_label.setStyleSheet("font-weight: bold; color: red;")
        elif risk_amount > 200:
            self.risk_label.setStyleSheet("font-weight: bold; color: orange;")
        else:
            self.risk_label.setStyleSheet("font-weight: bold; color: green;")
    
    def calculate_risk_amount(self) -> float:
        """Calculate risk amount for current trade parameters"""
        # Simplified risk calculation
        quantity = self.quantity_spin.value()
        leverage = self.leverage_spin.value()
        stop_loss_pct = self.stop_loss_spin.value() / 100
        
        # Assume BTC price for calculation
        estimated_price = 50000.0
        position_value = quantity * estimated_price * leverage
        risk_amount = position_value * stop_loss_pct
        
        return risk_amount
    
    def apply_strategy_parameters(self):
        """Apply strategy parameters"""
        params = {
            'position_size_pct': self.position_size_spin.value(),
            'max_leverage': self.max_leverage_spin.value(),
            'confidence_threshold': self.confidence_spin.value(),
            'min_spread_pct': self.min_spread_spin.value(),
            'timestamp': datetime.now().isoformat()
        }
        
        self.strategy_parameters_changed.emit(params)
        
        # Update status
        status_text = f"Parameters updated at {datetime.now().strftime('%H:%M:%S')}:\n"
        for key, value in params.items():
            if key != 'timestamp':
                status_text += f"• {key}: {value}\n"
        
        self.params_status.setPlainText(status_text)
        self.logger.info(f"Strategy parameters updated: {params}")
    
    def update_status(self):
        """Update status display"""
        current_time = datetime.now().strftime('%H:%M:%S')
        self.last_update.setText(f"Last Update: {current_time}")
        
        # Update connection status (simulate)
        self.connection_status.setText("🟢 Connected")
        self.connection_status.setStyleSheet("color: green;")
    
    def set_trading_active(self, active: bool):
        """Set trading active state externally"""
        self.trading_active = active
        self.start_button.setEnabled(not active)
        self.stop_button.setEnabled(active)
        
        if active:
            self.status_label.setText("Status: Running")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.status_label.setText("Status: Stopped")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
