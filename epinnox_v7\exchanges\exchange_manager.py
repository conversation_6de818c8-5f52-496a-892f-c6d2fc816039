"""
Exchange Manager for Epinnox v7

Manages multiple exchange connections and provides unified trading interface.
"""

import asyncio
from typing import Dict, Any, List, Optional, Type
from datetime import datetime
from enum import Enum

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from exchanges.base_exchange import BaseExchange, MarketData, Position, Order, Balance
from exchanges.htx_exchange import HTXExchange
from utils.logger import get_logger
from security.key_manager import CredentialManager

logger = get_logger()


class ExchangeType(Enum):
    """Supported exchange types"""
    HTX = "htx"
    BINANCE = "binance"
    BYBIT = "bybit"
    OKX = "okx"


class ExchangeManager:
    """Manages multiple exchange connections"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger()
        
        # Exchange instances
        self.exchanges: Dict[str, BaseExchange] = {}
        self.primary_exchange: Optional[BaseExchange] = None
        
        # Credential manager
        self.credential_manager = CredentialManager(config)
        
        # Exchange classes
        self.exchange_classes: Dict[ExchangeType, Type[BaseExchange]] = {
            ExchangeType.HTX: HTXExchange,
            # Add other exchanges here as implemented
        }
        
        # Connection status
        self.connected_exchanges = set()
        self.authenticated_exchanges = set()
        
        self.logger.info("Exchange Manager initialized")
    
    async def initialize_exchange(self, exchange_name: str, exchange_type: ExchangeType) -> bool:
        """Initialize and connect to an exchange"""
        try:
            # Get exchange class
            if exchange_type not in self.exchange_classes:
                raise ValueError(f"Unsupported exchange type: {exchange_type}")
            
            exchange_class = self.exchange_classes[exchange_type]
            
            # Create exchange instance
            exchange = exchange_class(self.config)
            
            # Connect to exchange
            if await exchange.connect():
                self.exchanges[exchange_name] = exchange
                self.connected_exchanges.add(exchange_name)
                
                # Set as primary if first exchange
                if self.primary_exchange is None:
                    self.primary_exchange = exchange
                
                self.logger.info(f"Successfully initialized {exchange_name} exchange")
                return True
            else:
                self.logger.error(f"Failed to connect to {exchange_name}")
                return False
        
        except Exception as e:
            self.logger.error(f"Error initializing {exchange_name}: {str(e)}")
            return False
    
    async def authenticate_exchange(self, exchange_name: str) -> bool:
        """Authenticate with an exchange using stored credentials"""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not initialized")
            
            exchange = self.exchanges[exchange_name]
            
            # Get credentials from secure storage
            credentials = self.credential_manager.get_exchange_credentials(exchange_name)
            if not credentials:
                raise ValueError(f"No credentials found for {exchange_name}")
            
            # Authenticate
            api_key = credentials.get('api_key')
            api_secret = credentials.get('api_secret')
            passphrase = credentials.get('passphrase')  # For some exchanges
            
            if await exchange.authenticate(api_key, api_secret, passphrase):
                self.authenticated_exchanges.add(exchange_name)
                self.logger.info(f"Successfully authenticated with {exchange_name}")
                return True
            else:
                self.logger.error(f"Authentication failed for {exchange_name}")
                return False
        
        except Exception as e:
            self.logger.error(f"Error authenticating {exchange_name}: {str(e)}")
            return False
    
    async def disconnect_all(self):
        """Disconnect from all exchanges"""
        for exchange_name, exchange in self.exchanges.items():
            try:
                await exchange.disconnect()
                self.logger.info(f"Disconnected from {exchange_name}")
            except Exception as e:
                self.logger.error(f"Error disconnecting from {exchange_name}: {str(e)}")
        
        self.exchanges.clear()
        self.connected_exchanges.clear()
        self.authenticated_exchanges.clear()
        self.primary_exchange = None
    
    def get_exchange(self, exchange_name: str = None) -> Optional[BaseExchange]:
        """Get exchange instance"""
        if exchange_name:
            return self.exchanges.get(exchange_name)
        else:
            return self.primary_exchange
    
    def is_exchange_ready(self, exchange_name: str = None) -> bool:
        """Check if exchange is connected and authenticated"""
        if exchange_name:
            return (exchange_name in self.connected_exchanges and 
                   exchange_name in self.authenticated_exchanges)
        else:
            return (self.primary_exchange is not None and 
                   self.primary_exchange.is_connected() and 
                   self.primary_exchange.is_authenticated())
    
    async def get_market_data(self, symbol: str, exchange_name: str = None) -> MarketData:
        """Get market data from exchange"""
        exchange = self.get_exchange(exchange_name)
        if not exchange:
            raise ValueError("No exchange available")

        return await exchange.get_market_data(symbol)

    def get_market_data_sync(self, symbol: str, exchange_name: str = None) -> MarketData:
        """Get market data from exchange (synchronous version for GUI)"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                return None

            # Use CCXT synchronous method if available
            if hasattr(exchange, 'ccxt_exchange') and exchange.ccxt_exchange:
                ticker = exchange.ccxt_exchange.fetch_ticker(symbol)
                if ticker:
                    from exchanges.base_exchange import MarketData
                    return MarketData(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        bid=float(ticker.get('bid', 0)),
                        ask=float(ticker.get('ask', 0)),
                        last=float(ticker.get('last', 0)),
                        volume=float(ticker.get('baseVolume', 0)),
                        high_24h=float(ticker.get('high', 0)),
                        low_24h=float(ticker.get('low', 0)),
                        change_24h=float(ticker.get('change', 0)),
                        change_pct_24h=float(ticker.get('percentage', 0))
                    )
            return None
        except Exception as e:
            self.logger.error(f"Error getting sync market data for {symbol}: {str(e)}")
            return None
    
    async def get_account_balance(self, exchange_name: str = None) -> List[Balance]:
        """Get account balance from exchange"""
        exchange = self.get_exchange(exchange_name)
        if not exchange:
            raise ValueError("No exchange available")
        
        return await exchange.get_account_balance()
    
    async def get_positions(self, exchange_name: str = None) -> List[Position]:
        """Get positions from exchange"""
        exchange = self.get_exchange(exchange_name)
        if not exchange:
            raise ValueError("No exchange available")
        
        return await exchange.get_positions()
    
    async def place_order(self, symbol: str, side: str, type: str, amount: float, 
                         price: float = None, exchange_name: str = None, **kwargs) -> Order:
        """Place order on exchange"""
        exchange = self.get_exchange(exchange_name)
        if not exchange:
            raise ValueError("No exchange available")
        
        # Convert string parameters to enums
        from exchanges.base_exchange import OrderSide, OrderType
        order_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL
        order_type = OrderType.MARKET if type.lower() == 'market' else OrderType.LIMIT
        
        return await exchange.place_order(symbol, order_side, order_type, amount, price, **kwargs)
    
    async def cancel_order(self, order_id: str, symbol: str, exchange_name: str = None) -> bool:
        """Cancel order on exchange"""
        exchange = self.get_exchange(exchange_name)
        if not exchange:
            raise ValueError("No exchange available")
        
        return await exchange.cancel_order(order_id, symbol)
    
    async def cancel_all_orders(self, symbol: str = None, exchange_name: str = None) -> int:
        """Cancel all orders on exchange"""
        exchange = self.get_exchange(exchange_name)
        if not exchange:
            raise ValueError("No exchange available")
        
        return await exchange.cancel_all_orders(symbol)
    
    async def emergency_stop_all_trading(self) -> Dict[str, Any]:
        """Emergency stop all trading across all exchanges"""
        results = {}
        
        for exchange_name in self.authenticated_exchanges:
            try:
                exchange = self.exchanges[exchange_name]
                
                # Cancel all orders
                cancelled_orders = await exchange.cancel_all_orders()
                
                # Get current positions (for futures exchanges)
                positions = await exchange.get_positions()
                
                results[exchange_name] = {
                    'cancelled_orders': cancelled_orders,
                    'open_positions': len(positions),
                    'status': 'stopped'
                }
                
                self.logger.critical(f"Emergency stop executed on {exchange_name}")
                
            except Exception as e:
                results[exchange_name] = {
                    'error': str(e),
                    'status': 'error'
                }
                self.logger.error(f"Emergency stop failed on {exchange_name}: {str(e)}")
        
        return results
    
    async def health_check_all(self) -> Dict[str, Any]:
        """Perform health check on all exchanges"""
        results = {}
        
        for exchange_name, exchange in self.exchanges.items():
            try:
                health = await exchange.health_check()
                results[exchange_name] = health
            except Exception as e:
                results[exchange_name] = {
                    'status': 'error',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
        
        return results
    
    def get_status(self) -> Dict[str, Any]:
        """Get overall exchange manager status"""
        return {
            'total_exchanges': len(self.exchanges),
            'connected_exchanges': list(self.connected_exchanges),
            'authenticated_exchanges': list(self.authenticated_exchanges),
            'primary_exchange': self.primary_exchange.name if self.primary_exchange else None,
            'ready_for_trading': len(self.authenticated_exchanges) > 0,
            'timestamp': datetime.now().isoformat()
        }
    
    async def setup_production_exchanges(self) -> bool:
        """Setup exchanges for production trading"""
        try:
            # Get exchange configuration
            exchange_config = self.config.get('exchanges', {})
            
            success_count = 0
            total_exchanges = 0
            
            for exchange_name, config in exchange_config.items():
                if not config.get('enabled', False):
                    continue
                
                total_exchanges += 1
                exchange_type_str = config.get('type', '').lower()
                
                try:
                    exchange_type = ExchangeType(exchange_type_str)
                    
                    # Initialize exchange
                    if await self.initialize_exchange(exchange_name, exchange_type):
                        # Authenticate exchange
                        if await self.authenticate_exchange(exchange_name):
                            success_count += 1
                            self.logger.info(f"Successfully setup {exchange_name} for production")
                        else:
                            self.logger.error(f"Failed to authenticate {exchange_name}")
                    else:
                        self.logger.error(f"Failed to initialize {exchange_name}")
                
                except ValueError as e:
                    self.logger.error(f"Invalid exchange type for {exchange_name}: {exchange_type_str}")
                except Exception as e:
                    self.logger.error(f"Error setting up {exchange_name}: {str(e)}")
            
            if success_count > 0:
                self.logger.info(f"Successfully setup {success_count}/{total_exchanges} exchanges")
                return True
            else:
                self.logger.error("Failed to setup any exchanges")
                return False
        
        except Exception as e:
            self.logger.error(f"Error setting up production exchanges: {str(e)}")
            return False


# Global exchange manager instance
_exchange_manager: Optional[ExchangeManager] = None


def get_exchange_manager(config: Dict[str, Any] = None) -> ExchangeManager:
    """Get global exchange manager instance"""
    global _exchange_manager
    
    if _exchange_manager is None and config is not None:
        _exchange_manager = ExchangeManager(config)
    
    return _exchange_manager
