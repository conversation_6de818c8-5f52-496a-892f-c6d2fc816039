#!/usr/bin/env python3
"""
Simple script to run the pre-launch checklist
"""

import sys
import asyncio
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def main():
    """Run the pre-launch checklist"""
    try:
        print("🚀 Running Epinnox v7 Pre-Launch Checklist...")
        
        from production.pre_launch_checklist import run_pre_launch_checklist
        
        # Run checklist with production config
        result = await run_pre_launch_checklist("config/production_config.yaml")
        
        # Print results
        print("\n" + "="*60)
        print("📊 PRE-LAUNCH CHECKLIST RESULTS")
        print("="*60)
        
        print(f"Overall Status: {result['overall_status'].upper()}")
        print(f"Ready for Production: {'✅ YES' if result['ready_for_production'] else '❌ NO'}")
        print(f"Duration: {result['duration_seconds']:.1f} seconds")
        
        summary = result['summary']
        print(f"\nChecks Summary:")
        print(f"  Total Checks: {summary['total_checks']}")
        print(f"  ✅ Passed: {summary['passed']}")
        print(f"  ❌ Failed: {summary['failed']}")
        print(f"  ⚠️ Warnings: {summary['warnings']}")
        print(f"  🔴 Critical Failures: {summary['critical_failures']}")
        
        # Show critical failures
        if result['critical_failures']:
            print(f"\n🔴 CRITICAL FAILURES:")
            for failure in result['critical_failures']:
                print(f"  - {failure['name']}: {failure['message']}")
        
        # Show warnings
        if result['warnings']:
            print(f"\n⚠️ WARNINGS:")
            for warning in result['warnings']:
                print(f"  - {warning['name']}: {warning['message']}")
        
        # Show recommendations
        if result['recommendations']:
            print(f"\n💡 RECOMMENDATIONS:")
            for rec in result['recommendations']:
                print(f"  {rec}")
        
        # Show next steps
        if result['next_steps']:
            print(f"\n📋 NEXT STEPS:")
            for step in result['next_steps']:
                print(f"  {step}")
        
        # Save detailed report
        timestamp = result['timestamp'].replace(':', '-').replace('.', '-')
        report_file = f"logs/pre_launch_report_{timestamp}.json"
        
        Path("logs").mkdir(exist_ok=True)
        with open(report_file, 'w') as f:
            json.dump(result, f, indent=2)
        
        print(f"\n📄 Detailed report saved: {report_file}")
        
        return result['ready_for_production']
        
    except Exception as e:
        print(f"❌ Error running checklist: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
