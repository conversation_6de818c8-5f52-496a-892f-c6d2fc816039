# 🚀 Epinnox v7 Production Deployment Guide

## ⚠️ CRITICAL WARNING

**THIS GUIDE ENABLES LIVE TRADING WITH REAL MONEY**

Only proceed if you:
- ✅ Understand the risks of automated cryptocurrency trading
- ✅ Have thoroughly tested the system in dry-run mode
- ✅ Are prepared for potential financial losses
- ✅ Have read and understood all documentation

---

## 📋 Pre-Deployment Checklist

### 1. System Requirements ✅
- **Python 3.9+** with all dependencies installed
- **Minimum 4GB RAM** for stable operation
- **Stable internet connection** with low latency
- **Dedicated server/VPS** recommended for 24/7 operation

### 2. Exchange Account Setup ✅
- **HTX (Huobi) Account** with verified identity
- **Sufficient balance** (minimum $1000 recommended)
- **API keys created** with proper permissions:
  - ✅ Read account information
  - ✅ Spot trading
  - ❌ Withdrawal (DISABLED for security)

### 3. Security Preparations ✅
- **Strong passwords** for all accounts
- **2FA enabled** on exchange accounts
- **IP whitelist** configured for API access
- **Secure backup** of API keys and configuration

---

## 🔧 Step-by-Step Deployment

### Step 1: Environment Setup

```bash
# Navigate to project directory
cd epinnox_v7

# Install production dependencies
pip install -r requirements.txt

# Verify installation
python -c "import PyQt5, ccxt, pydantic; print('Dependencies OK')"
```

### Step 2: Production Configuration

```bash
# Copy production configuration template
cp config/production_config.yaml config/my_production_config.yaml

# Edit configuration for your needs
nano config/my_production_config.yaml
```

**Critical Configuration Settings:**
```yaml
# MUST be set for live trading
environment: "production"
dry_run: false
debug_mode: false

# Conservative risk settings
risk_limits:
  max_daily_loss_usd: 500      # Adjust based on your risk tolerance
  max_drawdown_pct: 3.0        # Maximum account drawdown
  stop_loss_pct: 1.5           # Tight stop losses
  position_size_pct: 1.0       # Conservative position sizing

# Trading parameters
trading_symbols: ["BTC/USDT", "ETH/USDT"]
max_concurrent_positions: 2
leverage: 2                    # Conservative leverage
```

### Step 3: API Key Setup

```bash
# Run secure API key setup
python setup_api_keys.py
```

Follow the interactive prompts to:
1. Enter your HTX API credentials
2. Verify key permissions
3. Test connectivity

### Step 4: Pre-Launch Validation

```bash
# Run comprehensive pre-launch checklist
python -m production.pre_launch_checklist
```

This will validate:
- ✅ Configuration settings
- ✅ Exchange connectivity
- ✅ API permissions
- ✅ Risk management setup
- ✅ Security measures
- ✅ Emergency systems

**Do not proceed unless ALL critical checks pass!**

### Step 5: Final Testing (RECOMMENDED)

```bash
# Test with dry-run mode first
python test_production_dashboard.py

# Run a short dry-run session
python launch_production.py --dry-run --duration=30min
```

### Step 6: Production Launch

```bash
# Launch live trading system
python launch_production.py
```

**You will be prompted to:**
1. Confirm understanding of risks
2. Authorize live trading
3. Provide final confirmation

---

## 🛡️ Safety Measures

### Automatic Safety Features ✅

1. **Circuit Breakers**
   - Daily loss limits
   - Consecutive loss protection
   - High volatility halts
   - Connection failure protection

2. **Risk Management**
   - Position size limits
   - Leverage restrictions
   - Stop loss enforcement
   - Margin usage monitoring

3. **Emergency Controls**
   - Emergency stop button
   - Automatic system shutdown
   - Manual intervention capabilities
   - Real-time monitoring

### Manual Safety Controls ✅

1. **Dashboard Controls**
   - Start/Stop trading
   - Emergency stop (admin only)
   - Position management
   - Risk monitoring

2. **Keyboard Shortcuts**
   - `ESC` - Emergency stop (admin)
   - `Space` - Toggle trading
   - `F2` - Stop trading

---

## 📊 Monitoring and Management

### Real-Time Monitoring ✅

The production dashboard provides:
- **Live P&L tracking**
- **Position management**
- **Risk metrics**
- **System health**
- **Market data**
- **Alert notifications**

### Log Files 📝

Monitor these log files:
```
logs/production/
├── epinnox_production.log     # Main system log
├── trading_decisions.log      # LLM trading decisions
├── risk_events.log           # Risk management events
├── security_events.log       # Security-related events
└── errors.log               # Error tracking
```

### Performance Metrics 📈

Track key metrics:
- **Win rate** (target: >60%)
- **Average profit/loss**
- **Maximum drawdown**
- **Daily P&L**
- **System uptime**

---

## 🚨 Emergency Procedures

### Emergency Stop Procedures

1. **Immediate Stop**
   ```bash
   # From dashboard: Press ESC key (admin only)
   # Or click Emergency Stop button
   ```

2. **Manual Intervention**
   ```bash
   # Stop trading engine
   python -c "from llm.trading_engine import get_trading_engine; import asyncio; asyncio.run(get_trading_engine().emergency_stop())"
   
   # Cancel all orders
   python -c "from exchanges.exchange_manager import get_exchange_manager; import asyncio; asyncio.run(get_exchange_manager().emergency_stop_all_trading())"
   ```

3. **System Shutdown**
   ```bash
   # Graceful shutdown
   pkill -f "launch_production.py"
   
   # Force shutdown if needed
   pkill -9 -f "python"
   ```

### Recovery Procedures

1. **Check system logs** for error details
2. **Verify account balance** and positions
3. **Review recent trades** for anomalies
4. **Run pre-launch checklist** before restarting
5. **Restart with reduced parameters** if needed

---

## 📞 Support and Troubleshooting

### Common Issues

1. **Connection Errors**
   - Check internet connectivity
   - Verify API key permissions
   - Check exchange status

2. **Authentication Failures**
   - Verify API keys are correct
   - Check IP whitelist settings
   - Ensure sufficient permissions

3. **Trading Halts**
   - Check circuit breaker status
   - Review risk limit breaches
   - Verify account balance

### Getting Help

1. **Check log files** for detailed error messages
2. **Review configuration** for incorrect settings
3. **Run diagnostic tools**:
   ```bash
   python -m production.pre_launch_checklist
   python test_production_dashboard.py
   ```

---

## 📈 Performance Optimization

### Recommended Settings for Beginners

```yaml
# Conservative settings for new users
position_size_pct: 0.5          # Very small positions
max_daily_loss_usd: 100         # Low daily loss limit
max_concurrent_positions: 1     # Single position only
leverage: 1                     # No leverage
confidence_threshold: 0.80      # High confidence only
```

### Advanced Settings for Experienced Users

```yaml
# More aggressive settings (higher risk)
position_size_pct: 2.0          # Larger positions
max_daily_loss_usd: 1000        # Higher loss tolerance
max_concurrent_positions: 3     # Multiple positions
leverage: 3                     # Moderate leverage
confidence_threshold: 0.65      # Lower confidence threshold
```

---

## 🔄 Maintenance and Updates

### Daily Maintenance ✅

1. **Check system status** via dashboard
2. **Review trading performance**
3. **Monitor log files** for errors
4. **Verify account balance**
5. **Check for system updates**

### Weekly Maintenance ✅

1. **Backup configuration** and logs
2. **Review trading statistics**
3. **Update risk parameters** if needed
4. **Check exchange announcements**
5. **Rotate API keys** (monthly)

### Monthly Maintenance ✅

1. **Full system audit**
2. **Performance analysis**
3. **Risk parameter optimization**
4. **Security review**
5. **Update dependencies**

---

## ⚖️ Legal and Compliance

### Risk Disclosure ⚠️

- **Cryptocurrency trading involves substantial risk**
- **Past performance does not guarantee future results**
- **You may lose some or all of your investment**
- **Automated trading systems can malfunction**
- **Market conditions can change rapidly**

### Regulatory Compliance 📋

- **Check local regulations** for automated trading
- **Maintain proper records** of all trades
- **Report profits/losses** for tax purposes
- **Comply with exchange terms** of service
- **Follow anti-money laundering** requirements

---

## 🎯 Success Tips

### Best Practices ✅

1. **Start small** with conservative settings
2. **Monitor closely** during initial operation
3. **Keep detailed records** of performance
4. **Regularly review** and adjust parameters
5. **Stay informed** about market conditions

### Risk Management ✅

1. **Never risk more** than you can afford to lose
2. **Diversify** across multiple strategies
3. **Use stop losses** consistently
4. **Monitor correlation** between positions
5. **Have an exit strategy** for all scenarios

---

## 🚀 Ready to Launch?

### Final Checklist Before Going Live:

- [ ] ✅ All dependencies installed
- [ ] ✅ Production configuration reviewed
- [ ] ✅ API keys securely stored
- [ ] ✅ Pre-launch checklist passed
- [ ] ✅ Emergency procedures understood
- [ ] ✅ Monitoring systems ready
- [ ] ✅ Risk tolerance defined
- [ ] ✅ Legal compliance verified

### Launch Command:

```bash
python launch_production.py
```

**Remember: You can always stop trading immediately using the emergency stop features!**

---

*Good luck with your automated trading journey! Trade responsibly and never risk more than you can afford to lose.*

**Epinnox v7 Team**
