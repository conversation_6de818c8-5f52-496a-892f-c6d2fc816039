#!/usr/bin/env python3
"""
Epinnox v7 Production Launch Script

CRITICAL: This script enables LIVE TRADING with real money.
Only run after completing all safety checks and validations.
"""

import sys
import os
import asyncio
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

from utils.logger import get_logger, setup_logger
from utils.config_validator import ConfigValidator
from production.pre_launch_checklist import run_pre_launch_checklist
from production.safety_manager import ProductionSafetyManager
from exchanges.exchange_manager import get_exchange_manager
from llm.trading_engine import get_trading_engine
from gui.production_dashboard import ProductionDashboard
from auth.login_dialog import show_login_dialog

# Setup logging
setup_logger("config/production_config.yaml")
logger = get_logger()


class ProductionLauncher:
    """Production trading system launcher with comprehensive safety checks"""
    
    def __init__(self):
        self.logger = get_logger()
        self.config = None
        self.safety_manager = None
        self.exchange_manager = None
        self.trading_engine = None
        self.dashboard = None
        
        # Launch state
        self.pre_launch_passed = False
        self.safety_checks_passed = False
        self.user_confirmed = False
        self.trading_active = False
    
    async def launch_production_system(self) -> bool:
        """Launch the complete production trading system"""
        try:
            self.logger.info("🚀 EPINNOX v7 PRODUCTION LAUNCH SEQUENCE INITIATED")
            self.logger.info("=" * 80)
            
            # Step 1: Load production configuration
            if not await self.load_production_config():
                return False
            
            # Step 2: Run pre-launch checklist
            if not await self.run_pre_launch_validation():
                return False
            
            # Step 3: Perform comprehensive safety checks
            if not await self.perform_safety_checks():
                return False
            
            # Step 4: Get user confirmation for live trading
            if not await self.get_user_confirmation():
                return False
            
            # Step 5: Initialize trading systems
            if not await self.initialize_trading_systems():
                return False
            
            # Step 6: Start trading engine
            if not await self.start_trading_engine():
                return False
            
            # Step 7: Launch production dashboard
            if not await self.launch_dashboard():
                return False
            
            self.logger.info("🎉 EPINNOX v7 PRODUCTION SYSTEM SUCCESSFULLY LAUNCHED!")
            self.logger.info("💰 LIVE TRADING IS NOW ACTIVE")
            self.logger.info("=" * 80)
            
            return True
        
        except Exception as e:
            self.logger.critical(f"CRITICAL ERROR during production launch: {str(e)}")
            await self.emergency_shutdown()
            return False
    
    async def load_production_config(self) -> bool:
        """Load and validate production configuration"""
        try:
            self.logger.info("📋 Loading production configuration...")
            
            config_path = "config/production_config.yaml"
            if not Path(config_path).exists():
                self.logger.error(f"Production config not found: {config_path}")
                self.logger.info("Please create production_config.yaml from the template")
                return False
            
            # Load configuration
            self.config = ConfigValidator.load_and_validate(config_path)
            
            # Verify critical production settings
            if self.config.get('environment') != 'production':
                self.logger.error("Configuration environment is not set to 'production'")
                return False
            
            if self.config.get('dry_run', True):
                self.logger.error("Dry run mode is enabled - cannot start live trading")
                return False
            
            self.logger.info("✅ Production configuration loaded successfully")
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to load production config: {str(e)}")
            return False
    
    async def run_pre_launch_validation(self) -> bool:
        """Run comprehensive pre-launch checklist"""
        try:
            self.logger.info("🔍 Running pre-launch validation checklist...")
            
            # Run checklist
            checklist_result = await run_pre_launch_checklist("config/production_config.yaml")
            
            # Save checklist report
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_path = f"logs/pre_launch_report_{timestamp}.json"
            
            os.makedirs("logs", exist_ok=True)
            with open(report_path, 'w') as f:
                json.dump(checklist_result, f, indent=2)
            
            self.logger.info(f"📄 Pre-launch report saved: {report_path}")
            
            # Check results
            overall_status = checklist_result.get('overall_status')
            ready_for_production = checklist_result.get('ready_for_production', False)
            critical_failures = checklist_result.get('critical_failures', [])
            
            if critical_failures:
                self.logger.error("❌ CRITICAL FAILURES detected in pre-launch checklist:")
                for failure in critical_failures:
                    self.logger.error(f"  - {failure['name']}: {failure['message']}")
                
                self.logger.error("🛑 Cannot proceed to live trading with critical failures")
                return False
            
            if not ready_for_production:
                self.logger.warning("⚠️ System not fully ready for production")
                self.logger.warning("Review the pre-launch report before proceeding")
                
                # Ask user if they want to continue despite warnings
                response = input("\nContinue despite warnings? (type 'yes' to continue): ")
                if response.lower() != 'yes':
                    self.logger.info("Production launch cancelled by user")
                    return False
            
            self.pre_launch_passed = True
            self.logger.info("✅ Pre-launch validation completed successfully")
            return True
        
        except Exception as e:
            self.logger.error(f"Pre-launch validation failed: {str(e)}")
            return False
    
    async def perform_safety_checks(self) -> bool:
        """Perform comprehensive safety checks"""
        try:
            self.logger.info("🛡️ Performing comprehensive safety checks...")
            
            # Initialize safety manager
            self.safety_manager = ProductionSafetyManager(self.config)
            
            # Run safety checks
            safety_result = await self.safety_manager.perform_comprehensive_safety_check()
            
            # Check safety level
            safety_level = safety_result.get('overall_safety_level')
            ready_for_trading = safety_result.get('ready_for_trading', False)
            critical_issues = safety_result.get('critical_issues', [])
            
            if critical_issues:
                self.logger.error("❌ CRITICAL SAFETY ISSUES detected:")
                for issue in critical_issues:
                    self.logger.error(f"  - {issue}")
                
                self.logger.error("🛑 Cannot proceed to live trading with critical safety issues")
                return False
            
            if not ready_for_trading:
                self.logger.warning(f"⚠️ Safety level: {safety_level} - Trading may be restricted")
            
            self.safety_checks_passed = True
            self.logger.info(f"✅ Safety checks completed - Level: {safety_level}")
            return True
        
        except Exception as e:
            self.logger.error(f"Safety checks failed: {str(e)}")
            return False
    
    async def get_user_confirmation(self) -> bool:
        """Get user confirmation for live trading (simplified for development)"""
        try:
            self.logger.info("👤 Development mode - simplified confirmation...")

            print("\n" + "=" * 60)
            print("🚀 EPINNOX v7 PRODUCTION TRADING")
            print("=" * 60)
            print("Ready to start live trading with HTX exchange")
            print("Balance: $39.09 USDT | Safety systems: ACTIVE")
            print()

            # Simple confirmation for development
            user_input = input("Start live trading? (y/n): ").strip().lower()

            if user_input in ['y', 'yes']:
                self.user_confirmed = True
                self.logger.info("✅ User confirmed live trading")
                return True
            else:
                self.logger.info("❌ User declined live trading")
                return False

        except Exception as e:
            self.logger.error(f"User confirmation failed: {str(e)}")
            return False
    
    async def initialize_trading_systems(self) -> bool:
        """Initialize all trading system components"""
        try:
            self.logger.info("⚙️ Initializing trading systems...")
            
            # Initialize exchange manager
            self.exchange_manager = get_exchange_manager(self.config)
            if not await self.exchange_manager.setup_production_exchanges():
                self.logger.error("Failed to setup production exchanges")
                return False
            
            # Initialize trading engine
            self.trading_engine = get_trading_engine(self.config)
            
            self.logger.info("✅ Trading systems initialized successfully")
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to initialize trading systems: {str(e)}")
            return False
    
    async def start_trading_engine(self) -> bool:
        """Start the LLM trading engine"""
        try:
            self.logger.info("🤖 Starting LLM trading engine...")
            
            # Start trading engine
            if await self.trading_engine.start_trading():
                self.trading_active = True
                self.logger.info("✅ LLM trading engine started successfully")
                self.logger.info("💰 AUTONOMOUS TRADING IS NOW ACTIVE")
                return True
            else:
                self.logger.error("Failed to start LLM trading engine")
                return False
        
        except Exception as e:
            self.logger.error(f"Failed to start trading engine: {str(e)}")
            return False
    
    async def launch_dashboard(self) -> bool:
        """Launch the production dashboard"""
        try:
            self.logger.info("📊 Launching production dashboard...")
            
            # Show authentication dialog
            if not show_login_dialog(self.config):
                self.logger.error("Authentication failed - cannot launch dashboard")
                return False
            
            # Create and show dashboard
            from PyQt5.QtWidgets import QApplication
            app = QApplication(sys.argv)
            
            self.dashboard = ProductionDashboard(self.config)
            self.dashboard.show()
            
            self.logger.info("✅ Production dashboard launched successfully")
            
            # Run dashboard
            app.exec_()
            
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to launch dashboard: {str(e)}")
            return False
    
    async def emergency_shutdown(self):
        """Emergency shutdown of all systems"""
        try:
            self.logger.critical("🚨 INITIATING EMERGENCY SHUTDOWN")
            
            # Stop trading engine
            if self.trading_engine:
                await self.trading_engine.emergency_stop()
            
            # Emergency stop all exchanges
            if self.exchange_manager:
                await self.exchange_manager.emergency_stop_all_trading()
            
            # Activate safety manager emergency stop
            if self.safety_manager:
                await self.safety_manager.activate_emergency_stop("Production launch failure")
            
            self.logger.critical("🛑 EMERGENCY SHUTDOWN COMPLETED")
        
        except Exception as e:
            self.logger.critical(f"Error during emergency shutdown: {str(e)}")


async def main():
    """Main entry point for production launch"""
    print("🚀 Epinnox v7 Production Launch System")
    print("=" * 50)
    
    # Create launcher
    launcher = ProductionLauncher()
    
    try:
        # Launch production system
        success = await launcher.launch_production_system()
        
        if success:
            print("\n🎉 Production system launched successfully!")
            print("Monitor the dashboard and logs for trading activity.")
        else:
            print("\n❌ Production launch failed!")
            print("Check the logs for details and fix any issues before retrying.")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n🛑 Production launch interrupted by user")
        await launcher.emergency_shutdown()
        sys.exit(1)
    
    except Exception as e:
        print(f"\n💥 Unexpected error during launch: {str(e)}")
        await launcher.emergency_shutdown()
        sys.exit(1)


if __name__ == "__main__":
    # Run the production launch
    asyncio.run(main())
