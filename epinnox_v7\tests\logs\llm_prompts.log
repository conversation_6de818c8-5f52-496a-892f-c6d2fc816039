
=== 2025-06-13T09:23:22.151618 ===
test prompt
[2025-06-13 11:27:14] What is 2+2? Answer with just the number.
[2025-06-13 11:30:48] What is 2+2? Answer with just the number.
[2025-06-13 11:30:51] Analyze this market data and provide a trading decision:
                        Current price: 50000
                        SMA_20: 49800
                        RSI: 65
                        Provide your answer as either BUY, SELL, or HOLD.
[2025-06-13 11:31:00] Given the current market conditions:
                        Price trend: Upward
                        Volume: Increasing
                        Volatility: Low
                        What would be your trading recommendation? Answer with BUY, SELL, or HOLD.
[2025-06-13 13:40:37] You are a high-frequency futures scalping strategist. Use the data below to make a decision.

[Market]
- Symbol: BTC/USDT
- Price: 50000.0
- Bid: 49990.0 | Ask: 50010.0 | Spread: 0.040%
- 1m Change: 0.10% | 5m Change: 0.50%
- Order Book: Buy Pressure: 55.00%, Sell Wall: 100000
- Volume Surge: 120.00%

[Position]
- Side: LONG
- Entry Price: 49800.0
- Leverage: 2x
- Unrealized PnL: 400.00 USDT (0.80%)
- Liquidation Buffer: 95.00%
- Position Size: 10000 USDT

[Strategy]
- Regime: MarketRegime.TRENDING
- Risk Mode: NORMAL
- Signal Confidence: 0.85
- Cooldown Active: False

[Performance Metrics]
- PnL: 5000.0
- Win Rate: 0.65
- Drawdown: -0.02
- Trade Score: 0.78

Return JSON like:
{
    "action": "long" | "short" | "hold" | "close",
    "entry_price": float,
    "stop_loss": float,
    "take_profit": float,
    "position_size": float,
    "confidence": float,
    "reason": "string summary"
}

Additional Trading Rules:
1. Only open new positions when spread < 0.05%
2. Maintain SL distance > 0.5%
3. Account for volume surge and order book pressure in your decision
4. Be more conservative when drawdown > 5%
5. Consider market regime in position sizing

