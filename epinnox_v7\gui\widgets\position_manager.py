"""
Position Manager Widget for Epinnox v7

Interactive position management with one-click controls.
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QGroupBox, QHeaderView, QMessageBox,
    QMenu, QAction, QFrame, QProgressBar, QComboBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QBrush, QContextMenuEvent
from typing import Dict, Any, List
from datetime import datetime
import json

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from auth.decorators import require_auth
from utils.logger import get_logger

logger = get_logger()


class PositionManagerWidget(QWidget):
    """Interactive position management widget"""
    
    # Signals
    position_closed = pyqtSignal(str)  # symbol
    position_modified = pyqtSignal(str, dict)  # symbol, modifications
    stop_loss_updated = pyqtSignal(str, float)  # symbol, new_stop_loss
    take_profit_updated = pyqtSignal(str, float)  # symbol, new_take_profit
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger()
        
        # Data
        self.positions: Dict[str, Dict[str, Any]] = {}
        
        # UI Setup
        self.setup_ui()
        self.setup_styling()
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_positions)
        self.update_timer.start(2000)  # Update every 2 seconds
        
        # Connect to live data manager
        self.connect_to_data_manager()
        
        self.logger.info("Position Manager Widget initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Header
        header_layout = QHBoxLayout()
        
        title = QLabel("Position Manager")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Refresh button
        self.refresh_button = QPushButton("🔄 Refresh")
        self.refresh_button.clicked.connect(self.refresh_positions)
        header_layout.addWidget(self.refresh_button)
        
        # Close All button
        self.close_all_button = QPushButton("❌ Close All Positions")
        self.close_all_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        self.close_all_button.clicked.connect(self.close_all_positions)
        header_layout.addWidget(self.close_all_button)
        
        layout.addLayout(header_layout)
        
        # Summary section
        self.summary_frame = self.create_summary_section()
        layout.addWidget(self.summary_frame)
        
        # Positions table
        self.positions_table = self.create_positions_table()
        layout.addWidget(self.positions_table)
        
        # Quick actions
        self.quick_actions = self.create_quick_actions()
        layout.addWidget(self.quick_actions)
    
    def create_summary_section(self) -> QFrame:
        """Create position summary section"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(frame)
        
        # Total P&L
        self.total_pnl_label = QLabel("Total P&L: $0.00")
        self.total_pnl_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(self.total_pnl_label)
        
        # Unrealized P&L
        self.unrealized_pnl_label = QLabel("Unrealized: $0.00")
        layout.addWidget(self.unrealized_pnl_label)
        
        # Position count
        self.position_count_label = QLabel("Positions: 0")
        layout.addWidget(self.position_count_label)
        
        # Margin used
        self.margin_used_label = QLabel("Margin Used: $0.00")
        layout.addWidget(self.margin_used_label)
        
        layout.addStretch()
        
        return frame
    
    def create_positions_table(self) -> QTableWidget:
        """Create positions table"""
        table = QTableWidget()
        
        # Set columns
        columns = [
            "Symbol", "Side", "Size", "Entry Price", "Current Price",
            "Unrealized P&L", "P&L %", "Margin", "Stop Loss", "Take Profit", "Actions"
        ]
        table.setColumnCount(len(columns))
        table.setHorizontalHeaderLabels(columns)
        
        # Configure table
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setContextMenuPolicy(Qt.CustomContextMenu)
        table.customContextMenuRequested.connect(self.show_context_menu)
        
        return table
    
    def create_quick_actions(self) -> QGroupBox:
        """Create quick actions section"""
        group = QGroupBox("Quick Actions")
        layout = QHBoxLayout(group)
        
        # Close profitable positions
        close_profitable_btn = QPushButton("Close Profitable")
        close_profitable_btn.setStyleSheet("background-color: #4CAF50; color: white; padding: 8px;")
        close_profitable_btn.clicked.connect(self.close_profitable_positions)
        layout.addWidget(close_profitable_btn)
        
        # Close losing positions
        close_losing_btn = QPushButton("Close Losing")
        close_losing_btn.setStyleSheet("background-color: #f44336; color: white; padding: 8px;")
        close_losing_btn.clicked.connect(self.close_losing_positions)
        layout.addWidget(close_losing_btn)
        
        # Set stop loss for all
        set_sl_btn = QPushButton("Set Stop Loss All")
        set_sl_btn.clicked.connect(self.set_stop_loss_all)
        layout.addWidget(set_sl_btn)
        
        # Set take profit for all
        set_tp_btn = QPushButton("Set Take Profit All")
        set_tp_btn.clicked.connect(self.set_take_profit_all)
        layout.addWidget(set_tp_btn)
        
        layout.addStretch()
        
        return group
    
    def setup_styling(self):
        """Setup widget styling"""
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f5f5f5;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 8px;
                border: none;
                border-bottom: 2px solid #2196F3;
                font-weight: bold;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def connect_to_data_manager(self):
        """Connect to live data manager for real position updates"""
        try:
            from gui.data_manager import get_data_manager
            self.data_manager = get_data_manager()

            # Connect to position update signals
            self.data_manager.position_updated.connect(self.on_position_updated)
            self.data_manager.positions_cleared.connect(self.on_positions_cleared)

            # Clear any existing demo positions
            self.positions.clear()
            self.update_positions_display()

            self.logger.info("Connected to live data manager for position updates")

        except Exception as e:
            self.logger.error(f"Failed to connect to data manager: {str(e)}")
            # Fallback to demo data if connection fails
            self.load_demo_positions()

    def on_position_updated(self, symbol: str, position_data: Dict[str, Any]):
        """Handle live position update from data manager"""
        try:
            # Convert to our internal format
            self.positions[symbol] = {
                "symbol": position_data["symbol"],
                "side": position_data["side"],
                "size": position_data["size"],
                "entry_price": position_data["entry_price"],
                "current_price": position_data["current_price"],
                "unrealized_pnl": position_data["unrealized_pnl"],
                "unrealized_pnl_pct": position_data["unrealized_pnl_pct"],
                "margin_used": position_data["margin_used"],
                "stop_loss": 0.0,  # Would need to get from orders
                "take_profit": 0.0,  # Would need to get from orders
                "timestamp": datetime.now()
            }

            # Track that we have positions
            self._had_positions = True

            # Update display
            self.update_positions_display()
            self.logger.debug(f"Updated position display for {symbol}")

        except Exception as e:
            self.logger.error(f"Error handling position update for {symbol}: {str(e)}")

    def on_positions_cleared(self):
        """Handle when all positions are cleared"""
        self.positions.clear()
        self.update_positions_display()
        # Only log if we actually had positions before
        if hasattr(self, '_had_positions') and self._had_positions:
            self.logger.debug("Positions cleared")
            self._had_positions = False

    def load_demo_positions(self):
        """Load demo positions as fallback"""
        sample_positions = {
            "BTC-USDT": {
                "symbol": "BTC-USDT",
                "side": "long",
                "size": 0.1,
                "entry_price": 49500.0,
                "current_price": 50200.0,
                "unrealized_pnl": 70.0,
                "unrealized_pnl_pct": 1.41,
                "margin_used": 1650.0,
                "stop_loss": 48500.0,
                "take_profit": 52000.0,
                "timestamp": datetime.now()
            }
        }

        self.positions = sample_positions
        self.update_positions_display()
        self.logger.warning("Using demo positions - live data unavailable")
    
    def update_positions_display(self):
        """Update the positions table display"""
        self.positions_table.setRowCount(len(self.positions))
        
        total_pnl = 0.0
        total_margin = 0.0
        
        for row, (symbol, position) in enumerate(self.positions.items()):
            # Symbol
            self.positions_table.setItem(row, 0, QTableWidgetItem(symbol))
            
            # Side
            side_item = QTableWidgetItem(position["side"].upper())
            if position["side"] == "long":
                side_item.setBackground(QBrush(QColor(200, 255, 200)))
            else:
                side_item.setBackground(QBrush(QColor(255, 200, 200)))
            self.positions_table.setItem(row, 1, side_item)
            
            # Size
            self.positions_table.setItem(row, 2, QTableWidgetItem(f"{position['size']:.3f}"))
            
            # Entry Price
            self.positions_table.setItem(row, 3, QTableWidgetItem(f"${position['entry_price']:.2f}"))
            
            # Current Price
            self.positions_table.setItem(row, 4, QTableWidgetItem(f"${position['current_price']:.2f}"))
            
            # Unrealized P&L
            pnl = position["unrealized_pnl"]
            pnl_item = QTableWidgetItem(f"${pnl:.2f}")
            if pnl > 0:
                pnl_item.setForeground(QBrush(QColor(0, 150, 0)))
            else:
                pnl_item.setForeground(QBrush(QColor(200, 0, 0)))
            self.positions_table.setItem(row, 5, pnl_item)
            
            # P&L %
            pnl_pct = position["unrealized_pnl_pct"]
            pnl_pct_item = QTableWidgetItem(f"{pnl_pct:.2f}%")
            if pnl_pct > 0:
                pnl_pct_item.setForeground(QBrush(QColor(0, 150, 0)))
            else:
                pnl_pct_item.setForeground(QBrush(QColor(200, 0, 0)))
            self.positions_table.setItem(row, 6, pnl_pct_item)
            
            # Margin
            self.positions_table.setItem(row, 7, QTableWidgetItem(f"${position['margin_used']:.2f}"))
            
            # Stop Loss
            sl_text = f"${position['stop_loss']:.2f}" if position.get('stop_loss') else "None"
            self.positions_table.setItem(row, 8, QTableWidgetItem(sl_text))
            
            # Take Profit
            tp_text = f"${position['take_profit']:.2f}" if position.get('take_profit') else "None"
            self.positions_table.setItem(row, 9, QTableWidgetItem(tp_text))
            
            # Actions
            actions_widget = self.create_action_buttons(symbol)
            self.positions_table.setCellWidget(row, 10, actions_widget)
            
            # Update totals
            total_pnl += pnl
            total_margin += position["margin_used"]
        
        # Update summary
        self.update_summary(total_pnl, total_margin, len(self.positions))
    
    def create_action_buttons(self, symbol: str) -> QWidget:
        """Create action buttons for a position"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(4, 4, 4, 4)
        layout.setSpacing(4)
        
        # Close button
        close_btn = QPushButton("Close")
        close_btn.setMaximumWidth(60)
        close_btn.setStyleSheet("background-color: #f44336; color: white; padding: 4px;")
        close_btn.clicked.connect(lambda: self.close_position(symbol))
        layout.addWidget(close_btn)
        
        # Modify button
        modify_btn = QPushButton("Modify")
        modify_btn.setMaximumWidth(60)
        modify_btn.setStyleSheet("background-color: #2196F3; color: white; padding: 4px;")
        modify_btn.clicked.connect(lambda: self.modify_position(symbol))
        layout.addWidget(modify_btn)
        
        return widget
    
    def update_summary(self, total_pnl: float, total_margin: float, position_count: int):
        """Update summary labels"""
        # Total P&L with color coding
        self.total_pnl_label.setText(f"Total P&L: ${total_pnl:.2f}")
        if total_pnl > 0:
            self.total_pnl_label.setStyleSheet("color: green; font-weight: bold;")
        elif total_pnl < 0:
            self.total_pnl_label.setStyleSheet("color: red; font-weight: bold;")
        else:
            self.total_pnl_label.setStyleSheet("color: black; font-weight: bold;")
        
        # Other labels
        self.unrealized_pnl_label.setText(f"Unrealized: ${total_pnl:.2f}")
        self.position_count_label.setText(f"Positions: {position_count}")
        self.margin_used_label.setText(f"Margin Used: ${total_margin:.2f}")
    
    def show_context_menu(self, position):
        """Show context menu for position"""
        if self.positions_table.itemAt(position) is None:
            return
        
        row = self.positions_table.rowAt(position.y())
        if row < 0:
            return
        
        symbol_item = self.positions_table.item(row, 0)
        if symbol_item is None:
            return
        
        symbol = symbol_item.text()
        
        menu = QMenu(self)
        
        # Close position
        close_action = QAction("Close Position", self)
        close_action.triggered.connect(lambda: self.close_position(symbol))
        menu.addAction(close_action)
        
        # Modify position
        modify_action = QAction("Modify Position", self)
        modify_action.triggered.connect(lambda: self.modify_position(symbol))
        menu.addAction(modify_action)
        
        menu.addSeparator()
        
        # Set stop loss
        sl_action = QAction("Set Stop Loss", self)
        sl_action.triggered.connect(lambda: self.set_stop_loss(symbol))
        menu.addAction(sl_action)
        
        # Set take profit
        tp_action = QAction("Set Take Profit", self)
        tp_action.triggered.connect(lambda: self.set_take_profit(symbol))
        menu.addAction(tp_action)
        
        menu.exec_(self.positions_table.mapToGlobal(position))
    
    @require_auth
    def close_position(self, symbol: str):
        """Close a specific position"""
        if symbol not in self.positions:
            return
        
        position = self.positions[symbol]
        
        reply = QMessageBox.question(
            self,
            'Close Position',
            f"Close {position['side']} position for {symbol}?\n\n"
            f"Size: {position['size']:.3f}\n"
            f"Current P&L: ${position['unrealized_pnl']:.2f}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Remove position
            del self.positions[symbol]
            self.update_positions_display()
            
            # Emit signal
            self.position_closed.emit(symbol)
            self.logger.info(f"Position closed: {symbol}")
            
            QMessageBox.information(self, "Position Closed", f"Position {symbol} closed successfully!")
    
    @require_auth
    def close_all_positions(self):
        """Close all positions"""
        if not self.positions:
            QMessageBox.information(self, "No Positions", "No open positions to close.")
            return
        
        reply = QMessageBox.question(
            self,
            'Close All Positions',
            f"Close ALL {len(self.positions)} positions?\n\n"
            "This action cannot be undone!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            closed_positions = list(self.positions.keys())
            self.positions.clear()
            self.update_positions_display()
            
            for symbol in closed_positions:
                self.position_closed.emit(symbol)
            
            self.logger.info(f"All positions closed: {closed_positions}")
            QMessageBox.information(self, "Positions Closed", f"All {len(closed_positions)} positions closed!")
    
    def modify_position(self, symbol: str):
        """Modify a position"""
        # This would open a modification dialog
        QMessageBox.information(self, "Modify Position", f"Position modification for {symbol} - Feature coming soon!")
    
    def set_stop_loss(self, symbol: str):
        """Set stop loss for position"""
        # This would open a stop loss dialog
        QMessageBox.information(self, "Set Stop Loss", f"Stop loss setting for {symbol} - Feature coming soon!")
    
    def set_take_profit(self, symbol: str):
        """Set take profit for position"""
        # This would open a take profit dialog
        QMessageBox.information(self, "Set Take Profit", f"Take profit setting for {symbol} - Feature coming soon!")
    
    def close_profitable_positions(self):
        """Close all profitable positions"""
        profitable = [symbol for symbol, pos in self.positions.items() if pos["unrealized_pnl"] > 0]
        
        if not profitable:
            QMessageBox.information(self, "No Profitable Positions", "No profitable positions to close.")
            return
        
        reply = QMessageBox.question(
            self,
            'Close Profitable Positions',
            f"Close {len(profitable)} profitable positions?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            for symbol in profitable:
                del self.positions[symbol]
                self.position_closed.emit(symbol)
            
            self.update_positions_display()
            QMessageBox.information(self, "Positions Closed", f"{len(profitable)} profitable positions closed!")
    
    def close_losing_positions(self):
        """Close all losing positions"""
        losing = [symbol for symbol, pos in self.positions.items() if pos["unrealized_pnl"] < 0]
        
        if not losing:
            QMessageBox.information(self, "No Losing Positions", "No losing positions to close.")
            return
        
        reply = QMessageBox.question(
            self,
            'Close Losing Positions',
            f"Close {len(losing)} losing positions?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            for symbol in losing:
                del self.positions[symbol]
                self.position_closed.emit(symbol)
            
            self.update_positions_display()
            QMessageBox.information(self, "Positions Closed", f"{len(losing)} losing positions closed!")
    
    def set_stop_loss_all(self):
        """Set stop loss for all positions"""
        QMessageBox.information(self, "Set Stop Loss All", "Bulk stop loss setting - Feature coming soon!")
    
    def set_take_profit_all(self):
        """Set take profit for all positions"""
        QMessageBox.information(self, "Set Take Profit All", "Bulk take profit setting - Feature coming soon!")
    
    def refresh_positions(self):
        """Refresh positions data"""
        # If connected to live data manager, positions are updated automatically
        # This method is now mainly for fallback/demo mode
        if hasattr(self, 'data_manager') and self.data_manager:
            # Live data mode - positions updated via signals
            return

        # Demo mode - simulate price updates
        for symbol, position in self.positions.items():
            # Simulate price movement
            price_change = (hash(str(datetime.now().microsecond + hash(symbol))) % 200 - 100) / 10000
            new_price = position["current_price"] * (1 + price_change)
            position["current_price"] = new_price

            # Recalculate P&L
            if position["side"] == "long":
                position["unrealized_pnl"] = (new_price - position["entry_price"]) * position["size"]
            else:
                position["unrealized_pnl"] = (position["entry_price"] - new_price) * position["size"]

            position["unrealized_pnl_pct"] = (position["unrealized_pnl"] / (position["entry_price"] * position["size"])) * 100

        self.update_positions_display()
    
    def add_position(self, position_data: Dict[str, Any]):
        """Add a new position"""
        symbol = position_data["symbol"]
        self.positions[symbol] = position_data
        self.update_positions_display()
        self.logger.info(f"Position added: {symbol}")
    
    def update_position(self, symbol: str, updates: Dict[str, Any]):
        """Update an existing position"""
        if symbol in self.positions:
            self.positions[symbol].update(updates)
            self.update_positions_display()
            self.logger.info(f"Position updated: {symbol}")
    
    def get_positions(self) -> Dict[str, Dict[str, Any]]:
        """Get all positions"""
        return self.positions.copy()
