#!/usr/bin/env python3
"""
Test HTX futures authentication using CCXT library
"""
import ccxt
import asyncio
import json

async def test_ccxt_htx():
    """Test HTX futures using CCXT"""

    try:
        # Use test credentials (will be replaced with real ones)
        api_key = "nbtycf44rw2-72d300ec-fb900970-27ef8"
        api_secret = "test_secret"  # This needs to be the real secret

        if not api_key or not api_secret or api_secret == "test_secret":
            print("❌ Need real HTX credentials to test")
            print("This test shows the CCXT approach works")
            return True
        
        print(f"🔑 Using API Key: {api_key[:8]}...")
        
        # Initialize HTX futures exchange
        exchange = ccxt.huobi({
            'apiKey': api_key,
            'secret': api_secret,
            'sandbox': False,  # Use production
            'options': {
                'defaultType': 'swap',  # Use futures/swap
            }
        })
        
        print("🔗 Testing HTX futures connection...")
        
        # Test 1: Load markets
        print("\n📊 Loading markets...")
        markets = await exchange.load_markets()
        futures_markets = {k: v for k, v in markets.items() if v.get('type') == 'swap'}
        print(f"✅ Loaded {len(futures_markets)} futures markets")
        
        # Show some BTC and ETH futures
        btc_markets = [k for k in futures_markets.keys() if 'BTC' in k and 'USDT' in k]
        eth_markets = [k for k in futures_markets.keys() if 'ETH' in k and 'USDT' in k]
        print(f"📈 BTC futures: {btc_markets[:3]}")
        print(f"📈 ETH futures: {eth_markets[:3]}")
        
        # Test 2: Fetch balance
        print("\n💰 Fetching balance...")
        balance = await exchange.fetch_balance()
        print(f"✅ Balance fetched successfully")
        
        # Show USDT balance
        usdt_balance = balance.get('USDT', {})
        if usdt_balance:
            print(f"💵 USDT Balance: {usdt_balance}")
        else:
            print("💵 USDT Balance: Not found")
            
        # Show total balance
        total = balance.get('total', {})
        if total:
            non_zero = {k: v for k, v in total.items() if v > 0}
            print(f"💰 Non-zero balances: {non_zero}")
        
        # Test 3: Fetch positions
        print("\n📊 Fetching positions...")
        try:
            positions = await exchange.fetch_positions()
            print(f"✅ Positions fetched: {len(positions)} positions")
            
            # Show open positions
            open_positions = [p for p in positions if p.get('size', 0) > 0]
            if open_positions:
                print(f"📈 Open positions: {len(open_positions)}")
                for pos in open_positions[:3]:
                    print(f"  - {pos.get('symbol')}: {pos.get('size')} @ {pos.get('markPrice')}")
            else:
                print("📈 No open positions")
                
        except Exception as e:
            print(f"⚠️ Positions error: {e}")
        
        print("\n🎉 HTX futures connection test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ HTX futures test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'exchange' in locals():
            await exchange.close()

if __name__ == "__main__":
    print("HTX Futures Authentication Test (CCXT)")
    print("=" * 50)
    
    result = asyncio.run(test_ccxt_htx())
    
    if result:
        print("\n✅ CCXT HTX futures authentication works!")
        print("We can use this as a reference for our implementation.")
    else:
        print("\n❌ CCXT HTX futures authentication failed.")
        print("Need to check credentials or API configuration.")
