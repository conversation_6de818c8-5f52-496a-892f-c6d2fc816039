"""
Strategy Analysis Tab for Epinnox v7 Dashboard

This module provides the strategy visualization and backtesting interface including:
- Backtest results loading and display
- Equity curve plotting
- Performance metrics summary
- Trade analysis table
- Strategy YAML viewer and editor
- Backtest execution controls
"""

import json
import asyncio
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter, QGroupBox,
    QPushButton, QLabel, QTableWidget, QTableWidgetItem, QTextEdit,
    QComboBox, QSpinBox, QDoubleSpinBox, QFileDialog, QMessageBox,
    QProgressBar, QTabWidget, QScrollArea, QGridLayout
)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QColor

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import pandas as pd

from strategy_builder.backtester import Backtester
from core.strategy_backtester import BacktestResults
from utils.logger import get_logger


class BacktestWorker(QThread):
    """Worker thread for running backtests without blocking the UI"""
    
    finished = pyqtSignal(object)  # BacktestResults
    error = pyqtSignal(str)
    progress = pyqtSignal(str)
    
    def __init__(self, symbol, strategy_path, initial_balance, leverage, days):
        super().__init__()
        self.symbol = symbol
        self.strategy_path = strategy_path
        self.initial_balance = initial_balance
        self.leverage = leverage
        self.days = days
        self.logger = get_logger()
    
    def run(self):
        """Run backtest in separate thread"""
        try:
            self.progress.emit("Initializing backtester...")
            
            # Create event loop for async operations
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run backtest
            results = loop.run_until_complete(self._run_backtest())
            
            self.finished.emit(results)
            
        except Exception as e:
            self.logger.error(f"Backtest worker error: {str(e)}")
            self.error.emit(str(e))
        finally:
            try:
                loop.close()
            except:
                pass
    
    async def _run_backtest(self):
        """Async backtest execution"""
        backtester = Backtester()
        
        self.progress.emit("Fetching historical data...")
        data = await backtester.fetch_historical_data(
            symbol=self.symbol,
            timeframe='1m',
            days=self.days,
            exchange_id='binance'
        )
        
        if data is None or len(data) < 100:
            raise ValueError("Insufficient historical data")
        
        self.progress.emit("Loading strategy...")
        strategy = backtester.load_strategy(self.strategy_path)
        
        self.progress.emit("Running backtest simulation...")
        results = await backtester.run_backtest(
            symbol=self.symbol,
            ohlcv_data=data,
            strategy_config=strategy,
            use_llm=False,
            initial_balance=self.initial_balance,
            leverage=self.leverage
        )
        
        return results


class EquityCurveWidget(QWidget):
    """Widget for displaying equity curve plots"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        
        # Create matplotlib figure
        self.figure = Figure(figsize=(12, 6))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        # Initialize empty plot
        self.ax = self.figure.add_subplot(111)
        self.ax.set_title("Equity Curve")
        self.ax.set_xlabel("Time")
        self.ax.set_ylabel("Account Balance (USDT)")
        self.ax.grid(True, alpha=0.3)
        
        self.canvas.draw()
    
    def plot_equity_curve(self, equity_data: List[tuple], initial_balance: float):
        """Plot equity curve from backtest results"""
        try:
            self.ax.clear()
            
            if not equity_data:
                self.ax.text(0.5, 0.5, "No equity data available", 
                           ha='center', va='center', transform=self.ax.transAxes)
                self.canvas.draw()
                return
            
            # Extract timestamps and equity values
            timestamps = [datetime.fromtimestamp(ts) for ts, _ in equity_data]
            equity_values = [equity for _, equity in equity_data]
            
            # Plot equity curve
            self.ax.plot(timestamps, equity_values, 'b-', linewidth=2, label='Account Equity')
            
            # Add initial balance line
            self.ax.axhline(y=initial_balance, color='gray', linestyle='--', 
                          alpha=0.7, label=f'Initial Balance: ${initial_balance:.2f}')
            
            # Formatting
            self.ax.set_title("Account Equity Over Time", fontsize=14, fontweight='bold')
            self.ax.set_xlabel("Time")
            self.ax.set_ylabel("Account Balance (USDT)")
            self.ax.grid(True, alpha=0.3)
            self.ax.legend()
            
            # Format x-axis
            self.figure.autofmt_xdate()
            
            # Color the area under the curve
            self.ax.fill_between(timestamps, equity_values, initial_balance, 
                               where=[eq >= initial_balance for eq in equity_values],
                               color='green', alpha=0.2, interpolate=True)
            self.ax.fill_between(timestamps, equity_values, initial_balance,
                               where=[eq < initial_balance for eq in equity_values],
                               color='red', alpha=0.2, interpolate=True)
            
            self.canvas.draw()
            
        except Exception as e:
            self.ax.clear()
            self.ax.text(0.5, 0.5, f"Error plotting equity curve: {str(e)}", 
                       ha='center', va='center', transform=self.ax.transAxes)
            self.canvas.draw()


class StrategyTab(QWidget):
    """Main strategy analysis tab widget"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger()
        self.current_results = None
        self.backtest_worker = None
        
        self.init_ui()
        self.load_existing_results()
    
    def init_ui(self):
        """Initialize the strategy tab UI"""
        layout = QVBoxLayout(self)
        
        # Create main splitter
        main_splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(main_splitter)
        
        # Left panel - Controls and Results
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # Backtest Controls
        controls_group = QGroupBox("Backtest Controls")
        controls_layout = QGridLayout(controls_group)
        
        # Symbol selection
        controls_layout.addWidget(QLabel("Symbol:"), 0, 0)
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems(['DOGE/USDT:USDT', 'BTC/USDT:USDT', 'ETH/USDT:USDT'])
        controls_layout.addWidget(self.symbol_combo, 0, 1)
        
        # Strategy file selection
        controls_layout.addWidget(QLabel("Strategy:"), 1, 0)
        strategy_layout = QHBoxLayout()
        self.strategy_path_label = QLabel("No strategy selected")
        self.strategy_path_label.setStyleSheet("border: 1px solid #ccc; padding: 4px;")
        strategy_layout.addWidget(self.strategy_path_label)
        
        self.browse_strategy_btn = QPushButton("Browse...")
        self.browse_strategy_btn.clicked.connect(self.browse_strategy)
        strategy_layout.addWidget(self.browse_strategy_btn)
        
        controls_layout.addLayout(strategy_layout, 1, 1)
        
        # Parameters
        controls_layout.addWidget(QLabel("Initial Balance:"), 2, 0)
        self.balance_spinbox = QDoubleSpinBox()
        self.balance_spinbox.setRange(100, 100000)
        self.balance_spinbox.setValue(500)
        self.balance_spinbox.setSuffix(" USDT")
        controls_layout.addWidget(self.balance_spinbox, 2, 1)
        
        controls_layout.addWidget(QLabel("Leverage:"), 3, 0)
        self.leverage_spinbox = QSpinBox()
        self.leverage_spinbox.setRange(1, 100)
        self.leverage_spinbox.setValue(20)
        self.leverage_spinbox.setSuffix("x")
        controls_layout.addWidget(self.leverage_spinbox, 3, 1)
        
        controls_layout.addWidget(QLabel("Days of Data:"), 4, 0)
        self.days_spinbox = QSpinBox()
        self.days_spinbox.setRange(1, 30)
        self.days_spinbox.setValue(7)
        controls_layout.addWidget(self.days_spinbox, 4, 1)
        
        # Run backtest button
        self.run_backtest_btn = QPushButton("🚀 Run Backtest")
        self.run_backtest_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        self.run_backtest_btn.clicked.connect(self.run_backtest)
        controls_layout.addWidget(self.run_backtest_btn, 5, 0, 1, 2)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        controls_layout.addWidget(self.progress_bar, 6, 0, 1, 2)
        
        left_layout.addWidget(controls_group)
        
        # Performance Summary
        self.performance_group = QGroupBox("Performance Summary")
        self.performance_layout = QGridLayout(self.performance_group)
        left_layout.addWidget(self.performance_group)
        
        # Results selector
        results_group = QGroupBox("Saved Results")
        results_layout = QVBoxLayout(results_group)
        
        self.results_combo = QComboBox()
        self.results_combo.currentTextChanged.connect(self.load_selected_result)
        results_layout.addWidget(self.results_combo)
        
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.load_existing_results)
        results_layout.addWidget(refresh_btn)
        
        left_layout.addWidget(results_group)
        
        left_layout.addStretch()
        main_splitter.addWidget(left_panel)
        
        # Right panel - Visualization
        right_panel = QTabWidget()
        
        # Equity curve tab
        self.equity_widget = EquityCurveWidget()
        right_panel.addTab(self.equity_widget, "📈 Equity Curve")
        
        # Trades table tab
        self.trades_table = QTableWidget()
        right_panel.addTab(self.trades_table, "📋 Trades")
        
        # Strategy YAML tab
        self.strategy_text = QTextEdit()
        self.strategy_text.setFont(QFont("Courier", 10))
        right_panel.addTab(self.strategy_text, "📄 Strategy YAML")
        
        main_splitter.addWidget(right_panel)
        
        # Set splitter proportions
        main_splitter.setSizes([400, 1000])
        
        self.update_performance_summary(None)
    
    def browse_strategy(self):
        """Open file dialog to select strategy YAML file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Strategy File", "strategy_builder/strategies", 
            "YAML files (*.yaml *.yml);;All files (*)"
        )
        
        if file_path:
            self.strategy_path_label.setText(Path(file_path).name)
            self.strategy_path_label.setProperty("full_path", file_path)
            
            # Load and display strategy content
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                self.strategy_text.setPlainText(content)
            except Exception as e:
                QMessageBox.warning(self, "Warning", f"Could not load strategy file: {str(e)}")
    
    def run_backtest(self):
        """Start backtest execution"""
        try:
            # Validate inputs
            strategy_path = self.strategy_path_label.property("full_path")
            if not strategy_path:
                QMessageBox.warning(self, "Warning", "Please select a strategy file first.")
                return
            
            symbol = self.symbol_combo.currentText()
            initial_balance = self.balance_spinbox.value()
            leverage = self.leverage_spinbox.value()
            days = self.days_spinbox.value()
            
            # Disable controls
            self.run_backtest_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # Indeterminate progress
            
            # Start backtest worker
            self.backtest_worker = BacktestWorker(symbol, strategy_path, initial_balance, leverage, days)
            self.backtest_worker.finished.connect(self.on_backtest_finished)
            self.backtest_worker.error.connect(self.on_backtest_error)
            self.backtest_worker.progress.connect(self.on_backtest_progress)
            self.backtest_worker.start()
            
        except Exception as e:
            self.logger.error(f"Error starting backtest: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to start backtest: {str(e)}")
            self.reset_backtest_controls()
    
    def on_backtest_progress(self, message: str):
        """Handle backtest progress updates"""
        self.progress_bar.setFormat(message)
    
    def on_backtest_finished(self, results: BacktestResults):
        """Handle backtest completion"""
        try:
            self.current_results = results
            self.display_results(results)
            self.load_existing_results()  # Refresh results list
            
            QMessageBox.information(self, "Success", "Backtest completed successfully!")
            
        except Exception as e:
            self.logger.error(f"Error displaying results: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error displaying results: {str(e)}")
        finally:
            self.reset_backtest_controls()
    
    def on_backtest_error(self, error_message: str):
        """Handle backtest errors"""
        QMessageBox.critical(self, "Backtest Error", f"Backtest failed: {error_message}")
        self.reset_backtest_controls()
    
    def reset_backtest_controls(self):
        """Reset backtest controls to normal state"""
        self.run_backtest_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.progress_bar.setRange(0, 100)
    
    def display_results(self, results: BacktestResults):
        """Display backtest results in the UI"""
        try:
            # Update performance summary
            self.update_performance_summary(results)
            
            # Plot equity curve
            self.equity_widget.plot_equity_curve(results.equity_curve, 
                                                self.balance_spinbox.value())
            
            # Update trades table
            self.update_trades_table(results.trades)
            
        except Exception as e:
            self.logger.error(f"Error displaying results: {str(e)}")
    
    def update_performance_summary(self, results: Optional[BacktestResults]):
        """Update the performance summary display"""
        # Clear existing widgets
        for i in reversed(range(self.performance_layout.count())):
            self.performance_layout.itemAt(i).widget().setParent(None)
        
        if results is None:
            label = QLabel("No results to display")
            label.setAlignment(Qt.AlignCenter)
            self.performance_layout.addWidget(label, 0, 0, 1, 2)
            return
        
        # Create performance metrics display
        metrics = [
            ("Total Trades", f"{results.total_trades}"),
            ("Win Rate", f"{results.win_rate:.2f}%"),
            ("Total PnL", f"${results.total_pnl:.2f}"),
            ("Total Return", f"{results.total_pnl_pct:.2f}%"),
            ("Profit Factor", f"{results.profit_factor:.2f}"),
            ("Max Drawdown", f"{results.max_drawdown_pct:.2f}%"),
            ("Sharpe Ratio", f"{results.sharpe_ratio:.2f}"),
            ("Avg Trade Duration", f"{results.avg_trade_duration:.1f} min")
        ]
        
        for i, (label, value) in enumerate(metrics):
            row = i // 2
            col = (i % 2) * 2
            
            label_widget = QLabel(f"{label}:")
            label_widget.setFont(QFont("Arial", 9, QFont.Bold))
            self.performance_layout.addWidget(label_widget, row, col)
            
            value_widget = QLabel(value)
            value_widget.setFont(QFont("Arial", 9))
            
            # Color coding for important metrics
            if "PnL" in label and results.total_pnl > 0:
                value_widget.setStyleSheet("color: green; font-weight: bold;")
            elif "PnL" in label and results.total_pnl < 0:
                value_widget.setStyleSheet("color: red; font-weight: bold;")
            elif "Win Rate" in label and results.win_rate >= 60:
                value_widget.setStyleSheet("color: green; font-weight: bold;")
            elif "Drawdown" in label and results.max_drawdown_pct > 10:
                value_widget.setStyleSheet("color: red; font-weight: bold;")
            
            self.performance_layout.addWidget(value_widget, row, col + 1)
    
    def update_trades_table(self, trades: List):
        """Update the trades table with trade data"""
        try:
            self.trades_table.setRowCount(len(trades))
            self.trades_table.setColumnCount(8)
            
            headers = ["Time", "Action", "Entry Price", "Exit Price", "PnL", "PnL %", "Duration", "Reason"]
            self.trades_table.setHorizontalHeaderLabels(headers)
            
            for i, trade in enumerate(trades):
                # Convert timestamp to readable format
                time_str = datetime.fromtimestamp(trade.timestamp).strftime("%m/%d %H:%M")
                self.trades_table.setItem(i, 0, QTableWidgetItem(time_str))
                
                self.trades_table.setItem(i, 1, QTableWidgetItem(trade.action))
                self.trades_table.setItem(i, 2, QTableWidgetItem(f"{trade.entry_price:.6f}"))
                
                exit_price = f"{trade.exit_price:.6f}" if trade.exit_price else "N/A"
                self.trades_table.setItem(i, 3, QTableWidgetItem(exit_price))
                
                # Color code PnL
                pnl_item = QTableWidgetItem(f"${trade.pnl:.2f}")
                if trade.pnl > 0:
                    pnl_item.setBackground(QColor(200, 255, 200))
                elif trade.pnl < 0:
                    pnl_item.setBackground(QColor(255, 200, 200))
                self.trades_table.setItem(i, 4, pnl_item)
                
                pnl_pct_item = QTableWidgetItem(f"{trade.pnl_pct:.2f}%")
                if trade.pnl_pct > 0:
                    pnl_pct_item.setBackground(QColor(200, 255, 200))
                elif trade.pnl_pct < 0:
                    pnl_pct_item.setBackground(QColor(255, 200, 200))
                self.trades_table.setItem(i, 5, pnl_pct_item)
                
                duration_min = trade.duration / 60
                self.trades_table.setItem(i, 6, QTableWidgetItem(f"{duration_min:.1f} min"))
                
                self.trades_table.setItem(i, 7, QTableWidgetItem(trade.reason[:50]))
            
            # Resize columns to content
            self.trades_table.resizeColumnsToContents()
            
        except Exception as e:
            self.logger.error(f"Error updating trades table: {str(e)}")
    
    def load_existing_results(self):
        """Load existing backtest results from files"""
        try:
            results_dir = Path("logs/backtest_results")
            if not results_dir.exists():
                return
            
            # Find all JSON result files
            json_files = list(results_dir.glob("*.json"))
            
            self.results_combo.clear()
            self.results_combo.addItem("Select a result...")
            
            for json_file in sorted(json_files, key=lambda x: x.stat().st_mtime, reverse=True):
                # Extract info from filename and file
                try:
                    with open(json_file, 'r') as f:
                        data = json.load(f)
                    
                    symbol = data.get('metadata', {}).get('symbol', 'Unknown')
                    timestamp = json_file.stem.split('_')[-2:]  # Get timestamp part
                    display_name = f"{symbol} - {'_'.join(timestamp)}"
                    
                    self.results_combo.addItem(display_name, str(json_file))
                    
                except Exception as e:
                    self.logger.warning(f"Could not load result file {json_file}: {str(e)}")
                    continue
            
        except Exception as e:
            self.logger.error(f"Error loading existing results: {str(e)}")
    
    def load_selected_result(self, display_name: str):
        """Load and display selected backtest result"""
        if display_name == "Select a result...":
            return
        
        try:
            file_path = self.results_combo.currentData()
            if not file_path:
                return
            
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # Convert JSON data back to BacktestResults format
            results = self.json_to_backtest_results(data)
            self.current_results = results
            self.display_results(results)
            
            # Load strategy YAML if available
            strategy_config = data.get('metadata', {}).get('config', {})
            if strategy_config:
                # Try to find and load the original strategy file
                # For now, just show a placeholder
                self.strategy_text.setPlainText("# Strategy configuration loaded from backtest results\n" +
                                              f"# Original strategy data not available in this format")
            
        except Exception as e:
            self.logger.error(f"Error loading selected result: {str(e)}")
            QMessageBox.warning(self, "Warning", f"Could not load result: {str(e)}")
    
    def json_to_backtest_results(self, data: dict) -> BacktestResults:
        """Convert JSON data back to BacktestResults object"""
        from core.strategy_backtester import BacktestResults, BacktestTrade
        
        # Extract performance metrics
        metrics = data.get('performance_metrics', {})
        
        # Convert equity curve
        equity_curve = [(item['timestamp'], item['equity']) 
                       for item in data.get('equity_curve', [])]
        
        # Convert trades
        trades = []
        for trade_data in data.get('trades', []):
            trade = BacktestTrade(
                timestamp=trade_data.get('timestamp', 0),
                symbol=trade_data.get('symbol', ''),
                action=trade_data.get('action', ''),
                entry_price=trade_data.get('entry_price', 0),
                exit_price=trade_data.get('exit_price'),
                position_size=trade_data.get('position_size', 0),
                pnl=trade_data.get('pnl', 0),
                pnl_pct=trade_data.get('pnl_pct', 0),
                confidence=trade_data.get('confidence', 0),
                reason=trade_data.get('reason', ''),
                duration=trade_data.get('duration_seconds', 0)
            )
            trades.append(trade)
        
        # Create BacktestResults object
        results = BacktestResults(
            total_trades=metrics.get('total_trades', 0),
            winning_trades=metrics.get('winning_trades', 0),
            losing_trades=metrics.get('losing_trades', 0),
            win_rate=metrics.get('win_rate', 0),
            total_pnl=metrics.get('total_pnl', 0),
            total_pnl_pct=metrics.get('total_pnl_pct', 0),
            avg_win=metrics.get('avg_win', 0),
            avg_loss=metrics.get('avg_loss', 0),
            profit_factor=metrics.get('profit_factor', 0),
            max_drawdown=metrics.get('max_drawdown', 0),
            max_drawdown_pct=metrics.get('max_drawdown_pct', 0),
            sharpe_ratio=metrics.get('sharpe_ratio', 0),
            calmar_ratio=metrics.get('calmar_ratio', 0),
            avg_trade_duration=metrics.get('avg_trade_duration_minutes', 0),
            trades_per_day=metrics.get('trades_per_day', 0),
            signal_accuracy=metrics.get('signal_accuracy', 0),
            signal_frequency=data.get('signal_analysis', {}).get('signal_frequency', {}),
            time_of_day_performance=data.get('signal_analysis', {}).get('time_of_day_performance', {}),
            equity_curve=equity_curve,
            trades=trades
        )
        
        return results
    
    def refresh_data(self):
        """Refresh all data in the strategy tab"""
        self.load_existing_results()
