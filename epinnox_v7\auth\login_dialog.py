"""
Login Dialog for Epinnox v7 GUI

Provides secure login interface with session management.
"""

import sys
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QPushButton, QLabel, QCheckBox,
    QMessageBox, QApplication, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QIcon

from .authentication import AuthenticationManager, session_manager
from .models import UserRole
from database.database_manager import get_database_manager
from utils.logger import get_logger

logger = get_logger()


class LoginDialog(QDialog):
    """Secure login dialog with authentication"""
    
    login_successful = pyqtSignal(str)  # Emits username on successful login
    
    def __init__(self, config: dict, parent=None):
        super().__init__(parent)
        self.config = config
        self.auth_manager = None
        self.setup_ui()
        self.setup_auth()
    
    def setup_auth(self):
        """Initialize authentication manager"""
        try:
            db_manager = get_database_manager(self.config)
            with db_manager.get_session() as session:
                self.auth_manager = AuthenticationManager(session)
                session_manager.set_auth_manager(self.auth_manager)
        except Exception as e:
            logger.error(f"Failed to initialize authentication: {str(e)}")
            QMessageBox.critical(
                self,
                "Authentication Error",
                f"Failed to initialize authentication system:\n{str(e)}"
            )
    
    def setup_ui(self):
        """Setup the enhanced login dialog UI"""
        self.setWindowTitle("Epinnox v7 - Production Trading System")

        # Use minimum size instead of fixed size for better scaling
        self.setMinimumSize(500, 400)
        self.resize(500, 400)
        self.setModal(True)

        # Enable high DPI scaling
        self.setAttribute(Qt.WA_AcceptTouchEvents, False)

        # Apply simplified styling for better compatibility
        self.setStyleSheet("""
            QDialog {
                background-color: #34495e;
                color: white;
                font-family: Arial, sans-serif;
            }
            QLabel {
                color: white;
                font-family: Arial, sans-serif;
            }
            QLineEdit {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                color: black;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: bold;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton#createUserButton {
                background-color: #27ae60;
            }
            QPushButton#createUserButton:hover {
                background-color: #229954;
            }
            QPushButton#cancelButton {
                background-color: #95a5a6;
            }
            QPushButton#cancelButton:hover {
                background-color: #7f8c8d;
            }
            QCheckBox {
                color: white;
                font-size: 12px;
                font-family: Arial, sans-serif;
            }
        """)

        # Main layout
        layout = QVBoxLayout()
        layout.setSpacing(25)
        layout.setContentsMargins(40, 30, 40, 30)

        # Simplified header for better compatibility
        header_layout = QVBoxLayout()
        header_layout.setSpacing(15)

        # Simple title
        title_label = QLabel("EPINNOX v7")
        title_font = QFont("Arial", 20, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: white; margin: 10px;")

        subtitle_label = QLabel("Production Trading System")
        subtitle_font = QFont("Arial", 12)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("color: #bdc3c7; margin: 5px;")

        status_label = QLabel("Live Trading Ready")
        status_font = QFont("Arial", 10)
        status_label.setFont(status_font)
        status_label.setAlignment(Qt.AlignCenter)
        status_label.setStyleSheet("""
            color: #27ae60;
            background-color: #2c3e50;
            border: 1px solid #27ae60;
            border-radius: 10px;
            padding: 5px 10px;
            margin: 5px 20px;
        """)

        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        header_layout.addWidget(status_label)
        
        # Login form container
        form_container = QVBoxLayout()
        form_container.setSpacing(20)

        # Username field
        username_layout = QVBoxLayout()
        username_layout.setSpacing(8)
        username_label = QLabel("Username")
        username_font = QFont("Arial", 11)
        username_label.setFont(username_font)
        username_label.setStyleSheet("color: white; margin-bottom: 5px;")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Enter your username")
        self.username_input.returnPressed.connect(self.login)
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)

        # Password field
        password_layout = QVBoxLayout()
        password_layout.setSpacing(8)
        password_label = QLabel("Password")
        password_font = QFont("Arial", 11)
        password_label.setFont(password_font)
        password_label.setStyleSheet("color: white; margin-bottom: 5px;")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("Enter your password")
        self.password_input.returnPressed.connect(self.login)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)

        # Remember me checkbox
        self.remember_checkbox = QCheckBox("Remember me for 30 days")
        checkbox_font = QFont("Arial", 10)
        self.remember_checkbox.setFont(checkbox_font)
        self.remember_checkbox.setStyleSheet("color: white; margin-top: 10px;")

        form_container.addLayout(username_layout)
        form_container.addLayout(password_layout)
        form_container.addWidget(self.remember_checkbox)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        self.create_user_button = QPushButton("Create Account")
        self.create_user_button.setObjectName("createUserButton")
        self.create_user_button.clicked.connect(self.show_create_user_dialog)
        create_font = QFont("Arial", 10)
        self.create_user_button.setFont(create_font)

        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setObjectName("cancelButton")
        self.cancel_button.clicked.connect(self.reject)
        cancel_font = QFont("Arial", 10)
        self.cancel_button.setFont(cancel_font)

        self.login_button = QPushButton("Login to Trading System")
        self.login_button.setDefault(True)
        self.login_button.clicked.connect(self.login)
        login_font = QFont("Arial", 11, QFont.Bold)
        self.login_button.setFont(login_font)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)

        button_layout.addWidget(self.create_user_button)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.login_button)

        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            color: #e74c3c;
            font-size: 12px;
            font-weight: 500;
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
        """)

        # Add all to main layout
        layout.addLayout(header_layout)
        layout.addSpacing(20)
        layout.addLayout(form_container)
        layout.addSpacing(15)
        layout.addLayout(button_layout)
        layout.addWidget(self.status_label)
        layout.addStretch()

        self.setLayout(layout)

        # Focus on username input
        self.username_input.setFocus()
    
    def login(self):
        """Handle login attempt"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            self.show_status("Please enter both username and password", error=True)
            return
        
        if not self.auth_manager:
            self.show_status("Authentication system not available", error=True)
            return
        
        # Attempt login
        try:
            success, message = session_manager.login(
                username=username,
                password=password,
                ip_address="127.0.0.1",  # Local GUI access
                user_agent="Epinnox v7 GUI"
            )
            
            if success:
                logger.info(f"Successful login: {username}")
                self.login_successful.emit(username)
                self.accept()
            else:
                self.show_status(message, error=True)
                self.password_input.clear()
                self.password_input.setFocus()
                
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            self.show_status(f"Login error: {str(e)}", error=True)
    
    def show_status(self, message: str, error: bool = False):
        """Show enhanced status message"""
        self.status_label.setText(message)
        if error:
            self.status_label.setStyleSheet("""
                color: #e74c3c;
                background-color: rgba(231, 76, 60, 0.1);
                border: 1px solid #e74c3c;
                font-size: 12px;
                font-weight: 500;
                margin-top: 10px;
                padding: 8px;
                border-radius: 4px;
            """)
        else:
            self.status_label.setStyleSheet("""
                color: #27ae60;
                background-color: rgba(39, 174, 96, 0.1);
                border: 1px solid #27ae60;
                font-size: 12px;
                font-weight: 500;
                margin-top: 10px;
                padding: 8px;
                border-radius: 4px;
            """)
    
    def show_create_user_dialog(self):
        """Show create user dialog"""
        dialog = CreateUserDialog(self.auth_manager, self)
        if dialog.exec_() == QDialog.Accepted:
            self.show_status("User created successfully! Please login.", error=False)


class CreateUserDialog(QDialog):
    """Dialog for creating new users"""
    
    def __init__(self, auth_manager: AuthenticationManager, parent=None):
        super().__init__(parent)
        self.auth_manager = auth_manager
        self.setup_ui()
    
    def setup_ui(self):
        """Setup create user dialog UI"""
        self.setWindowTitle("Create New User")
        self.setFixedSize(350, 250)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # Form
        form_layout = QFormLayout()
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Username (min 3 chars)")
        
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("Email address")
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("Password (min 8 chars)")
        
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        self.confirm_password_input.setPlaceholderText("Confirm password")
        
        form_layout.addRow("Username:", self.username_input)
        form_layout.addRow("Email:", self.email_input)
        form_layout.addRow("Password:", self.password_input)
        form_layout.addRow("Confirm:", self.confirm_password_input)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        create_button = QPushButton("Create User")
        create_button.clicked.connect(self.create_user)
        
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(create_button)
        
        # Status
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: red; font-size: 11px;")
        
        layout.addLayout(form_layout)
        layout.addLayout(button_layout)
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)
    
    def create_user(self):
        """Create new user"""
        username = self.username_input.text().strip()
        email = self.email_input.text().strip()
        password = self.password_input.text()
        confirm_password = self.confirm_password_input.text()
        
        # Validation
        if not all([username, email, password, confirm_password]):
            self.status_label.setText("All fields are required")
            return
        
        if password != confirm_password:
            self.status_label.setText("Passwords do not match")
            return
        
        # Create user
        try:
            success, message = self.auth_manager.create_user(
                username=username,
                email=email,
                password=password,
                role=UserRole.VIEWER  # Default role
            )
            
            if success:
                self.accept()
            else:
                self.status_label.setText(message)
                
        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")


def show_login_dialog(config: dict) -> bool:
    """
    Show login dialog and return True if login successful
    """
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    dialog = LoginDialog(config)
    result = dialog.exec_()
    
    return result == QDialog.Accepted
