#!/usr/bin/env python3
"""
Comprehensive Backtester Test Script for Epinnox v7

This script demonstrates the full backtesting capabilities including:
- Historical data fetching
- Strategy testing (both rule-based and LLM)
- Performance analysis
- Results visualization
- Edge case testing
"""

import asyncio
import sys
import traceback
from pathlib import Path
from datetime import datetime

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

from strategy_builder.backtester import Backtester
from utils.logger import get_logger


async def test_data_fetching():
    """Test historical data fetching functionality"""
    logger = get_logger()
    logger.info("=== Testing Historical Data Fetching ===")
    
    try:
        backtester = Backtester()
        
        # Test fetching DOGE/USDT data
        logger.info("Fetching DOGE/USDT historical data...")
        data = await backtester.fetch_historical_data(
            symbol='DOGE/USDT:USDT',
            timeframe='1m',
            days=3,  # Start with 3 days for testing
            exchange_id='binance'
        )
        
        logger.info(f"Successfully fetched {len(data)} candles")
        logger.info(f"Data range: {data['timestamp'].iloc[0]} to {data['timestamp'].iloc[-1]}")
        logger.info(f"Price range: {data['close'].min():.6f} to {data['close'].max():.6f}")
        
        return data
        
    except Exception as e:
        logger.error(f"Error fetching data: {str(e)}")
        logger.error(traceback.format_exc())
        return None


async def test_rule_based_strategy():
    """Test rule-based strategy backtesting"""
    logger = get_logger()
    logger.info("=== Testing Rule-Based Strategy ===")
    
    try:
        backtester = Backtester()
        
        # Fetch data
        data = await backtester.fetch_historical_data(
            symbol='DOGE/USDT:USDT',
            timeframe='1m',
            days=2,
            exchange_id='binance'
        )
        
        if data is None or len(data) < 100:
            logger.error("Insufficient data for backtesting")
            return None
        
        # Load strategy
        strategy_path = "strategy_builder/strategies/simple_momentum.yaml"
        strategy = backtester.load_strategy(strategy_path)
        
        # Run backtest with high leverage for testing
        logger.info("Running rule-based backtest...")
        results = await backtester.run_backtest(
            symbol='DOGE/USDT:USDT',
            ohlcv_data=data,
            strategy_config=strategy,
            use_llm=False,
            initial_balance=500.0,  # $500 starting balance
            leverage=20  # 20x leverage as requested
        )
        
        # Print results
        logger.info("=== Backtest Results ===")
        logger.info(f"Total trades: {results.total_trades}")
        logger.info(f"Win rate: {results.win_rate:.2f}%")
        logger.info(f"Total PnL: ${results.total_pnl:.2f} ({results.total_pnl_pct:.2f}%)")
        logger.info(f"Max drawdown: {results.max_drawdown_pct:.2f}%")
        logger.info(f"Sharpe ratio: {results.sharpe_ratio:.2f}")
        logger.info(f"Profit factor: {results.profit_factor:.2f}")
        
        # Analyze results
        analysis = backtester.analyze_results(results)
        logger.info(f"Performance grade: {analysis['performance_grade']}")
        logger.info(f"Strengths: {analysis['strengths']}")
        logger.info(f"Weaknesses: {analysis['weaknesses']}")
        logger.info(f"Recommendations: {analysis['recommendations']}")
        
        return results
        
    except Exception as e:
        logger.error(f"Error in rule-based strategy test: {str(e)}")
        logger.error(traceback.format_exc())
        return None


async def test_strategy_comparison():
    """Test comparing multiple strategies"""
    logger = get_logger()
    logger.info("=== Testing Strategy Comparison ===")
    
    try:
        backtester = Backtester()
        
        # Fetch data
        data = await backtester.fetch_historical_data(
            symbol='DOGE/USDT:USDT',
            timeframe='1m',
            days=1,  # Shorter period for comparison test
            exchange_id='binance'
        )
        
        if data is None or len(data) < 50:
            logger.error("Insufficient data for strategy comparison")
            return None
        
        # Load multiple strategies
        strategies = []
        
        try:
            strategy1 = backtester.load_strategy("strategy_builder/strategies/simple_momentum.yaml")
            strategy1['name'] = 'Simple Momentum'
            strategies.append(strategy1)
        except:
            logger.warning("Could not load simple_momentum.yaml")
        
        try:
            strategy2 = backtester.load_strategy("strategy_builder/strategies/scalping_breakout.yaml")
            strategy2['name'] = 'Scalping Breakout'
            strategies.append(strategy2)
        except:
            logger.warning("Could not load scalping_breakout.yaml")
        
        if not strategies:
            logger.error("No strategies loaded for comparison")
            return None
        
        # Run comparison
        logger.info(f"Comparing {len(strategies)} strategies...")
        results = await backtester.run_strategy_comparison(
            symbol='DOGE/USDT:USDT',
            ohlcv_data=data,
            strategies=strategies,
            initial_balance=500.0,
            leverage=20
        )
        
        # Print comparison results
        logger.info("=== Strategy Comparison Results ===")
        for strategy_name, result in results.items():
            logger.info(f"\n{strategy_name}:")
            logger.info(f"  Trades: {result.total_trades}")
            logger.info(f"  Win Rate: {result.win_rate:.2f}%")
            logger.info(f"  PnL: ${result.total_pnl:.2f} ({result.total_pnl_pct:.2f}%)")
            logger.info(f"  Max DD: {result.max_drawdown_pct:.2f}%")
            logger.info(f"  Sharpe: {result.sharpe_ratio:.2f}")
        
        return results
        
    except Exception as e:
        logger.error(f"Error in strategy comparison test: {str(e)}")
        logger.error(traceback.format_exc())
        return None


async def test_edge_cases():
    """Test edge cases and error handling"""
    logger = get_logger()
    logger.info("=== Testing Edge Cases ===")
    
    try:
        backtester = Backtester()
        
        # Test 1: Empty data
        logger.info("Testing with empty data...")
        import pandas as pd
        empty_data = pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        
        try:
            results = await backtester.run_backtest(
                symbol='TEST/USDT:USDT',
                ohlcv_data=empty_data,
                strategy_config=None,
                use_llm=False,
                initial_balance=1000.0,
                leverage=1
            )
            logger.info("Empty data test: PASSED (no crash)")
        except Exception as e:
            logger.info(f"Empty data test: Expected error - {str(e)}")
        
        # Test 2: Invalid strategy
        logger.info("Testing with invalid strategy...")
        invalid_strategy = {'invalid': 'strategy'}
        
        # Fetch minimal data for testing
        test_data = await backtester.fetch_historical_data(
            symbol='DOGE/USDT:USDT',
            timeframe='1m',
            days=0.1,  # Very short period
            exchange_id='binance'
        )
        
        if test_data is not None and len(test_data) > 10:
            try:
                results = await backtester.run_backtest(
                    symbol='DOGE/USDT:USDT',
                    ohlcv_data=test_data,
                    strategy_config=invalid_strategy,
                    use_llm=False,
                    initial_balance=1000.0,
                    leverage=1
                )
                logger.info("Invalid strategy test: PASSED (handled gracefully)")
            except Exception as e:
                logger.info(f"Invalid strategy test: Expected error - {str(e)}")
        
        # Test 3: Extreme leverage
        logger.info("Testing with extreme leverage...")
        if test_data is not None and len(test_data) > 10:
            try:
                results = await backtester.run_backtest(
                    symbol='DOGE/USDT:USDT',
                    ohlcv_data=test_data,
                    strategy_config=None,
                    use_llm=False,
                    initial_balance=100.0,
                    leverage=100  # Extreme leverage
                )
                logger.info("Extreme leverage test: PASSED")
                logger.info(f"Result with 100x leverage: {results.total_pnl_pct:.2f}% PnL")
            except Exception as e:
                logger.info(f"Extreme leverage test: Error - {str(e)}")
        
        logger.info("Edge case testing completed")
        
    except Exception as e:
        logger.error(f"Error in edge case testing: {str(e)}")
        logger.error(traceback.format_exc())


async def run_comprehensive_doge_backtest():
    """Run a comprehensive DOGE/USDT backtest with 20x leverage and $500 balance"""
    logger = get_logger()
    logger.info("=== Comprehensive DOGE/USDT Backtest ===")
    
    try:
        backtester = Backtester()
        
        # Fetch 7 days of DOGE/USDT data
        logger.info("Fetching 7 days of DOGE/USDT data...")
        data = await backtester.fetch_historical_data(
            symbol='DOGE/USDT:USDT',
            timeframe='1m',
            days=7,
            exchange_id='binance'
        )
        
        if data is None or len(data) < 1000:
            logger.error("Insufficient data for comprehensive backtest")
            return None
        
        logger.info(f"Data loaded: {len(data)} candles")
        logger.info(f"Price range: ${data['close'].min():.6f} - ${data['close'].max():.6f}")
        logger.info(f"Average volume: {data['volume'].mean():.0f}")
        
        # Load and run simple momentum strategy
        strategy = backtester.load_strategy("strategy_builder/strategies/simple_momentum.yaml")
        
        logger.info("Running comprehensive backtest...")
        logger.info("Parameters:")
        logger.info(f"  Symbol: DOGE/USDT:USDT")
        logger.info(f"  Initial Balance: $500")
        logger.info(f"  Leverage: 20x")
        logger.info(f"  Strategy: Simple Momentum")
        logger.info(f"  Data Period: 7 days")
        
        results = await backtester.run_backtest(
            symbol='DOGE/USDT:USDT',
            ohlcv_data=data,
            strategy_config=strategy,
            use_llm=False,
            initial_balance=500.0,
            leverage=20
        )
        
        # Detailed results analysis
        logger.info("\n" + "="*50)
        logger.info("COMPREHENSIVE BACKTEST RESULTS")
        logger.info("="*50)
        
        logger.info(f"📊 TRADING PERFORMANCE:")
        logger.info(f"   Total Trades: {results.total_trades}")
        logger.info(f"   Winning Trades: {results.winning_trades}")
        logger.info(f"   Losing Trades: {results.losing_trades}")
        logger.info(f"   Win Rate: {results.win_rate:.2f}%")
        
        logger.info(f"\n💰 PROFIT & LOSS:")
        logger.info(f"   Initial Balance: $500.00")
        logger.info(f"   Final Balance: ${500 + results.total_pnl:.2f}")
        logger.info(f"   Total PnL: ${results.total_pnl:.2f}")
        logger.info(f"   Total Return: {results.total_pnl_pct:.2f}%")
        logger.info(f"   Average Win: ${results.avg_win:.2f}")
        logger.info(f"   Average Loss: ${results.avg_loss:.2f}")
        logger.info(f"   Profit Factor: {results.profit_factor:.2f}")
        
        logger.info(f"\n⚠️  RISK METRICS:")
        logger.info(f"   Maximum Drawdown: ${results.max_drawdown:.2f} ({results.max_drawdown_pct:.2f}%)")
        logger.info(f"   Sharpe Ratio: {results.sharpe_ratio:.2f}")
        logger.info(f"   Calmar Ratio: {results.calmar_ratio:.2f}")
        
        logger.info(f"\n⏱️  TIME ANALYSIS:")
        logger.info(f"   Average Trade Duration: {results.avg_trade_duration:.1f} minutes")
        logger.info(f"   Trades Per Day: {results.trades_per_day:.1f}")
        
        logger.info(f"\n🎯 SIGNAL ANALYSIS:")
        logger.info(f"   Signal Accuracy: {results.signal_accuracy:.2f}")
        for action, count in results.signal_frequency.items():
            logger.info(f"   {action.upper()} signals: {count}")
        
        # Performance analysis
        analysis = backtester.analyze_results(results)
        logger.info(f"\n📈 PERFORMANCE GRADE: {analysis['performance_grade']}")
        
        if analysis['strengths']:
            logger.info(f"\n✅ STRENGTHS:")
            for strength in analysis['strengths']:
                logger.info(f"   • {strength}")
        
        if analysis['weaknesses']:
            logger.info(f"\n❌ WEAKNESSES:")
            for weakness in analysis['weaknesses']:
                logger.info(f"   • {weakness}")
        
        if analysis['recommendations']:
            logger.info(f"\n💡 RECOMMENDATIONS:")
            for rec in analysis['recommendations']:
                logger.info(f"   • {rec}")
        
        logger.info("\n" + "="*50)
        
        return results
        
    except Exception as e:
        logger.error(f"Error in comprehensive backtest: {str(e)}")
        logger.error(traceback.format_exc())
        return None


async def main():
    """Run all backtester tests"""
    logger = get_logger()
    logger.info("Starting Epinnox v7 Backtester Comprehensive Test Suite")
    logger.info("="*60)
    
    # Test 1: Data fetching
    data = await test_data_fetching()
    if data is None:
        logger.error("Data fetching failed. Aborting tests.")
        return
    
    # Test 2: Rule-based strategy
    await test_rule_based_strategy()
    
    # Test 3: Strategy comparison
    await test_strategy_comparison()
    
    # Test 4: Edge cases
    await test_edge_cases()
    
    # Test 5: Comprehensive DOGE backtest
    await run_comprehensive_doge_backtest()
    
    logger.info("\n" + "="*60)
    logger.info("All backtester tests completed!")
    logger.info("Check the logs/backtest_results/ directory for detailed results.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Test failed with error: {str(e)}")
        traceback.print_exc()
