import json
from typing import Dict, Optional, Union
from utils.enums import ActionType
from utils.logger import get_logger, log_error
from pathlib import Path
import asyncio
from datetime import datetime

logger = get_logger()

class LLMResponseValidator:
    """Validates and sanitizes LLM response"""
    
    @staticmethod
    def validate_action(action: str) -> bool:
        """Validate that action is one of the allowed types"""
        try:
            ActionType(action.lower())
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_prices(response: Dict, current_price: float) -> bool:
        """Validate price values are reasonable"""
        if response['action'] in ['hold', 'close']:
            return True
            
        try:
            entry = float(response.get('entry_price', current_price))
            sl = float(response.get('stop_loss', 0))
            tp = float(response.get('take_profit', 0))
            
            # Basic sanity checks
            if entry <= 0 or sl <= 0 or tp <= 0:
                return False
                
            # SL/TP checks based on action
            if response['action'] == 'long':
                if not (sl < entry < tp):
                    return False
            elif response['action'] == 'short':
                if not (tp < entry < sl):
                    return False
                    
            return True
            
        except (TypeError, ValueError):
            return False
    
    @staticmethod
    def validate_confidence(confidence: Union[float, str]) -> bool:
        """Validate confidence is a float between 0 and 1"""
        try:
            conf = float(confidence)
            return 0 <= conf <= 1
        except (TypeError, ValueError):
            return False

def parse_llm_response(
    response_text: str,
    current_price: float,
    min_confidence: float = 0.65
) -> Optional[Dict]:
    """
    Parse and validate the LLM's JSON response
    Returns None if validation fails
    """
    try:
        # First try to parse the JSON
        try:
            response = json.loads(response_text)
        except json.JSONDecodeError:
            log_error("Failed to parse LLM response as JSON")
            return None
            
        # Create validator instance
        validator = LLMResponseValidator()
        
        # Required fields validation
        if 'action' not in response or 'confidence' not in response:
            log_error("Missing required fields in LLM response")
            return None
            
        # Action validation
        if not validator.validate_action(response['action']):
            log_error(f"Invalid action: {response['action']}")
            return None
            
        # Confidence validation
        if not validator.validate_confidence(response['confidence']):
            log_error(f"Invalid confidence value: {response['confidence']}")
            return None
            
        # Confidence threshold check
        if float(response['confidence']) < min_confidence:
            logger.info(f"Confidence {response['confidence']} below threshold {min_confidence}")
            return None
            
        # Price validation for trades
        if not validator.validate_prices(response, current_price):
            log_error("Invalid price values in response")
            return None
            
        # If we get here, response is valid
        return {
            'action': response['action'].lower(),
            'entry_price': float(response.get('entry_price', current_price)),
            'stop_loss': float(response.get('stop_loss', 0)),
            'take_profit': float(response.get('take_profit', 0)),
            'position_size': float(response.get('position_size', 0)),
            'confidence': float(response['confidence']),
            'reason': str(response.get('reason', 'No reason provided'))
        }
        
    except Exception as e:
        log_error(f"Error parsing LLM response: {str(e)}")
        return None

class LLMResponseParser:
    """Validates and parses LLM JSON responses"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # Get log directory from config with fallback
        log_dir = Path(self.config.get('logging', {}).get('log_dir', 'logs'))
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Get debug logs path with fallback
        debug_logs = self.config.get('logging', {}).get('debug_logs', {})
        self.rejected_signals_file = Path(debug_logs.get('rejected_signals', 'logs/rejected_signals.jsonl'))
        self.rejected_signals_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Thread safety
        self._file_lock = asyncio.Lock()
        
    async def _log_rejected_signal(self, prompt: str, response: str, reason: str):
        """Log rejected signal to JSONL file"""
        try:
            async with self._file_lock:
                rejected_entry = {
                    'timestamp': datetime.now().isoformat(),
                    'prompt': prompt,
                    'response': response,
                    'reason': reason
                }
                
                with open(self.rejected_signals_file, 'a') as f:
                    f.write(json.dumps(rejected_entry) + '\n')
                    
        except Exception as e:
            self.logger.error(f"Failed to log rejected signal: {str(e)}")
            
    async def parse_response(self, response: str, prompt: str) -> Optional[Dict]:
        """Parse and validate LLM response, logging rejections"""
        try:
            # Parse JSON
            parsed = json.loads(response)
            
            # Validate structure
            if not self._validate_structure(parsed):
                await self._log_rejected_signal(
                    prompt, response, "Invalid structure"
                )
                return None
                
            # Validate values
            if not self._validate_values(parsed):
                await self._log_rejected_signal(
                    prompt, response, "Invalid values"
                )
                return None
                  # Check confidence threshold
            if not self.check_confidence_threshold(parsed):
                await self._log_rejected_signal(
                    prompt, response, "Below confidence threshold"
                )
                return None
                
            return parsed
            
        except json.JSONDecodeError as e:
            await self._log_rejected_signal(
                prompt, response, f"JSON decode error: {str(e)}"
            )
            return None
        except Exception as e:
            await self._log_rejected_signal(
                prompt, response, f"Unexpected error: {str(e)}"
            )
            return None
            
    def _validate_structure(self, parsed: Dict) -> bool:
        """Validate the structure of the parsed response"""
        # Base required keys for all actions
        required_keys = {'action', 'confidence', 'reason'}
        
        # Get action and ensure it's a string
        action = parsed.get('action')
        if not isinstance(action, str):
            self.logger.warning("Action must be a string")
            return False
            
        # Convert to lowercase for consistency
        action = action.lower()
        
        # Additional required keys based on action type
        if action in ['long', 'short']:
            required_keys.update({'entry_price', 'stop_loss', 'take_profit', 'position_size'})
            
        # Check if all required keys exist
        missing_keys = required_keys - set(parsed.keys())
        if missing_keys:
            self.logger.warning(f"Missing required keys: {missing_keys}")
            return False
            
        return True

    def _validate_values(self, parsed: Dict) -> bool:
        """Validate the values in the parsed response with type checking"""
        validator = LLMResponseValidator()
        
        try:
            # Action validation
            if not isinstance(parsed['action'], str) or not validator.validate_action(parsed['action'].lower()):
                self.logger.warning(f"Invalid action: {parsed.get('action')}")
                return False
                
            # Confidence validation with type checking
            try:
                confidence = float(parsed['confidence'])
                if not validator.validate_confidence(confidence):
                    self.logger.warning(f"Invalid confidence value: {confidence}")
                    return False
            except (ValueError, TypeError):
                self.logger.warning(f"Confidence must be a number: {parsed.get('confidence')}")
                return False
            
            # For trade actions, validate all numeric fields
            action = parsed['action'].lower()
            if action in ['long', 'short']:
                numeric_fields = ['entry_price', 'stop_loss', 'take_profit', 'position_size']
                
                for field in numeric_fields:
                    try:
                        value = float(parsed[field])
                        if value <= 0:
                            self.logger.warning(f"Field {field} must be positive: {value}")
                            return False
                    except (ValueError, TypeError):
                        self.logger.warning(f"Field {field} must be a number: {parsed.get(field)}")
                        return False
                        
                # Validate prices are logical based on action type
                entry = float(parsed['entry_price'])
                sl = float(parsed['stop_loss'])
                tp = float(parsed['take_profit'])
                
                if action == 'long' and not (sl < entry < tp):
                    self.logger.warning(f"Invalid price relationships for long: SL={sl}, Entry={entry}, TP={tp}")
                    return False
                elif action == 'short' and not (tp < entry < sl):
                    self.logger.warning(f"Invalid price relationships for short: TP={tp}, Entry={entry}, SL={sl}")
                    return False
            
        except Exception as e:
            self.logger.error(f"Error validating values: {str(e)}")
            return False
            
        return True

    @staticmethod
    def check_confidence_threshold(parsed: Dict, min_confidence: float = 0.65) -> bool:
        """Check if the confidence is above the minimum threshold"""
        return float(parsed['confidence']) >= min_confidence
