from typing import Dict, Optional
import time
import asyncio
from datetime import datetime
import yaml
from utils.logger import get_logger
from utils.enums import ActionType
from core.llm_prompt_builder import LLMPromptBuilder
from core.llm_response_parser import LLMResponseParser
from core.strategy_state import StrategyState
from core.risk_guard import RiskGuard
from data.market_fetcher import MarketFetcher
from execution.position_tracker import PositionTracker
from data.trade_logger import TradeLogger
from execution.trade_executor import TradeExecutor
from llm.model_runner import ModelRunner
from llm.performance_feedback import PerformanceFeedback

class ScalperEngine:
    """Main trading engine coordinating all components"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # Initialize components for multi-symbol support
        self.market_fetcher = MarketFetcher(config)
        self.position_tracker = PositionTracker(config)
        self.trade_executor = TradeExecutor(config)
        self.trade_logger = TradeLogger(config)
        self.performance_feedback = PerformanceFeedback(config)
        
        # Create strategy state managers per symbol
        self.strategy_states = {
            symbol: StrategyState(config) 
            for symbol in config.get('symbols', ['BTC/USDT:USDT'])
        }
        
        # Initialize model and prompt components
        self.model_runner = ModelRunner(config)
        self.prompt_builder = LLMPromptBuilder()
        self.response_parser = LLMResponseParser(config)
        
        # Risk management per symbol
        self.risk_guards = {
            symbol: RiskGuard(config)
            for symbol in config.get('symbols', ['BTC/USDT:USDT'])
        }
        
        # Symbol selection parameters
        self.min_market_quality = config.get('min_market_quality', 0.7)
        self.max_active_symbols = config.get('max_active_symbols', 3)
        self.symbol_cooldown = config.get('symbol_cooldown_seconds', 300)
        self.active_symbols = set()
        self.symbol_last_trade = {}

    async def initialize(self):
        """Initialize all components"""
        try:
            # Initialize market fetcher first
            self.logger.info("Initializing market fetcher...")
            await self.market_fetcher.initialize()
            
            # Initialize components that depend on market_fetcher
            self.position_tracker = PositionTracker(self.market_fetcher.exchange)
            self.trade_executor = TradeExecutor(self.market_fetcher.exchange, self.config)
            
            # Initialize LLM
            await self.model_runner.initialize()
            self.logger.info("LLM model initialized")
            
            # Load trade history into performance feedback
            historical_trades = self.trade_logger.trades
            for trade in historical_trades[-self.config['performance_window']:]:
                self.performance_feedback.add_trade(trade)
                
            # Track active symbols with validation
            self.active_symbols = self.config.get('symbols', [])
            if not self.active_symbols:
                self.logger.warning("No trading symbols configured!")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize engine: {str(e)}")
            raise
            
    async def run_trading_cycle(self):
        """Run one complete trading cycle across all symbols"""
        try:
            # Update market data for all symbols
            market_data = await self.market_fetcher.update_market_data()
            
            # Get best trading opportunities
            opportunities = self.market_fetcher.get_best_trading_opportunities(
                min_quality=self.min_market_quality
            )
            
            # Filter out symbols on cooldown
            current_time = time.time()
            active_opportunities = [
                opp for opp in opportunities
                if current_time - self.symbol_last_trade.get(opp['symbol'], 0) > self.symbol_cooldown
            ]
            
            # Process each viable opportunity
            for opportunity in active_opportunities[:self.max_active_symbols]:
                symbol = opportunity['symbol']
                
                try:
                    # Get symbol-specific components
                    strategy_state = self.strategy_states[symbol]
                    risk_guard = self.risk_guards[symbol]
                    
                    # Update strategy state
                    strategy_state.update(market_data[symbol])
                    
                    # Check if we should trade this symbol
                    if not self._should_trade_symbol(symbol, opportunity):
                        continue
                    
                    # Get trading decision
                    decision = await self._get_trading_decision(
                        symbol,
                        market_data[symbol],
                        strategy_state,
                        opportunity
                    )
                    
                    if decision:
                        # Validate decision with risk guard
                        if risk_guard.validate_trade(decision, market_data[symbol], strategy_state.get_state()):
                            # Execute trade
                            trade_result = await self.trade_executor.execute_trade(decision)
                            
                            if trade_result:
                                # Update symbol tracking
                                self.active_symbols.add(symbol)
                                self.symbol_last_trade[symbol] = current_time
                                
                                # Log trade and update feedback
                                await self.trade_logger.log_trade(trade_result)
                                await self.performance_feedback.process_trade(trade_result)
                    
                except Exception as e:
                    self.logger.error(f"Error processing symbol {symbol}: {str(e)}")
                    
        except Exception as e:
            self.logger.error(f"Error in trading cycle: {str(e)}")

    def _should_trade_symbol(self, symbol: str, opportunity: Dict) -> bool:
        """Determine if we should trade a symbol"""
        # Check if we're already at max active symbols
        if len(self.active_symbols) >= self.max_active_symbols and symbol not in self.active_symbols:
            return False
            
        # Check market quality
        if opportunity['score'] < self.min_market_quality:
            return False
            
        # Check manipulation metrics
        metrics = opportunity.get('metrics', {})
        if metrics.get('manipulation_safety', 0) < 0.7:  # Require high safety
            return False
            
        # Check trading conditions
        if metrics.get('spread_quality', 0) < 0.5:  # Require decent spreads
            return False
            
        return True

    async def _get_trading_decision(
        self,
        symbol: str,
        market_data: Dict,
        strategy_state: StrategyState,
        opportunity: Dict
    ) -> Optional[Dict]:
        """Get trading decision for a specific symbol"""
        try:
            # Build prompt with symbol context
            prompt = self.prompt_builder.build_prompt(
                market_data=market_data,
                position_data=self.position_tracker.get_position(symbol),
                strategy_data=strategy_state.get_state(),
                performance_data=self.performance_feedback.get_metrics(),
                opportunity_data=opportunity
            )
            
            # Get model response
            response = await self.model_runner.get_trading_decision(prompt)
            if not response:
                return None
                
            # Parse and validate response
            decision = await self.response_parser.parse_response(response, prompt)
            if not decision:
                return None
                
            # Add symbol to decision
            decision['symbol'] = symbol
            return decision
            
        except Exception as e:
            self.logger.error(f"Error getting trading decision for {symbol}: {str(e)}")
            return None

    async def process_symbol(self, symbol: str) -> Optional[Dict]:
        """
        Process a single trading symbol
        
        Parameters
        ----------
        symbol : str
            Trading pair symbol
            
        Returns
        -------
        Optional[Dict]
            Execution result if any
        """
        try:
            # Get market data
            market_data = await self.market_fetcher.get_market_data(symbol)
            
            # Get position data
            position_data = await self.position_tracker.get_position(symbol)
            
            # Update strategy state
            self.strategy_state.update_market_regime(
                market_data['price_history'],
                market_data['volume_history']
            )
            
            strategy_state = self.strategy_state.get_state_summary()
            
            # Get performance metrics
            performance_data = self.performance_feedback.get_performance_summary()
            
            # Build prompt
            prompt = self.prompt_builder.build_prompt(
                market_data,
                position_data,
                strategy_state,
                performance_data
            )
            
            # Get LLM decision
            response = await self.model_runner.get_trading_decision(prompt)
            if not response:
                return None
                
            # Parse and validate response
            parsed_response = await self.response_parser.parse_response(response, prompt)
            if not parsed_response:
                return None
                
            # Check confidence threshold
            if not self.response_parser.check_confidence_threshold(parsed_response):
                self.logger.info(
                    f"Decision confidence {parsed_response['confidence']:.2f} "
                    f"below threshold"
                )
                return None
                
            # Validate against risk parameters
            is_valid, reason = self.risk_guard.validate_trade(
                parsed_response,
                market_data,
                position_data,
                strategy_state
            )
            
            if not is_valid:
                self.logger.warning(f"Trade rejected: {reason}")
                return None
                
            # Execute trade
            result = await self.trade_executor.execute_trade(
                symbol,
                parsed_response,
                position_data
            )
            
            if result['success']:
                # Log trade
                trade_data = {
                    'symbol': symbol,
                    'action': parsed_response['action'],
                    'entry_price': parsed_response['entry_price'],
                    'position_size': parsed_response['position_size'],
                    'pnl': position_data.get('pnl_usd', 0),
                    'timestamp': datetime.now().timestamp()
                }
                
                self.trade_logger.log_trade(trade_data)
                
                # Update performance feedback
                self.performance_feedback.add_trade(trade_data)
                
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing symbol {symbol}: {str(e)}")
            return None
            
    async def close(self):
        """Cleanup all components"""
        try:
            # Close positions if configured
            if self.config.get('close_positions_on_shutdown', True):
                for symbol in self.active_symbols:
                    position = await self.position_tracker.get_position(symbol)
                    if position and position['size'] > 0:
                        await self.trade_executor.execute_trade(
                            symbol,
                            {'action': ActionType.CLOSE.value},
                            position
                        )
                        
            # Close components
            await self.market_fetcher.close()
            await self.model_runner.close()
            await self.trade_executor.close()
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {str(e)}")
            raise
    
    async def close_all_positions(self):
        """Close all open positions"""
        try:
            if self.trade_executor:
                for symbol in self.active_symbols:
                    position = await self.position_tracker.get_position(symbol)
                    if position and position['size'] != 0:
                        await self.trade_executor.execute_trade(
                            symbol=symbol,
                            action={'action': 'close'},
                            current_position=position
                        )
        except Exception as e:
            self.logger.error(f"Error closing positions: {str(e)}")
            raise
