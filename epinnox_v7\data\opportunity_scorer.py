from typing import Dict, List, Optional
import numpy as np
import pandas as pd
from utils.logger import get_logger

class OpportunityScorer:
    """Advanced opportunity scoring system"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # Configure scoring weights
        self.weights = config.get('opportunity_weights', {
            'technical': 0.25,
            'volatility': 0.20,
            'liquidity': 0.15,
            'safety': 0.25,
            'trend': 0.15
        })
        
        # Historical opportunity scores
        self.score_history = {}
        
        # Performance tracking
        self.performance_impact = {}

    def calculate_opportunity_score(
        self,
        symbol: str,
        market_data: Dict,
        technical_analysis: Dict,
        correlation_data: Dict
    ) -> Dict:
        """Calculate comprehensive opportunity score"""
        try:
            scores = {}
            
            # Technical setup score
            scores['technical'] = self._calculate_technical_score(technical_analysis)
            
            # Volatility opportunity score
            scores['volatility'] = self._calculate_volatility_score(market_data)
            
            # Liquidity score
            scores['liquidity'] = self._calculate_liquidity_score(market_data)
            
            # Safety score
            scores['safety'] = self._calculate_safety_score(
                technical_analysis,
                correlation_data
            )
            
            # Trend score
            scores['trend'] = self._calculate_trend_score(
                technical_analysis,
                correlation_data
            )
            
            # Calculate weighted total score
            total_score = 0
            for component, score in scores.items():
                total_score += score * self.weights.get(component, 0)
            
            # Calculate additional metrics
            volatility = self._calculate_volatility_metrics(market_data)
            risk_metrics = self._calculate_risk_metrics(
                technical_analysis,
                correlation_data
            )
            
            # Update history
            self._update_score_history(symbol, scores, total_score)
            
            return {
                'total_score': float(total_score),
                'component_scores': scores,
                'volatility_metrics': volatility,
                'risk_metrics': risk_metrics,
                'score_stability': self._calculate_score_stability(symbol)
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating opportunity score: {str(e)}")
            return {'total_score': 0.0}

    def _calculate_technical_score(self, technical_analysis: Dict) -> float:
        """Calculate technical analysis score"""
        score = 0.0
        
        try:
            # Pattern quality
            patterns = technical_analysis.get('candlestick_patterns', [])
            pattern_score = 0.0
            if patterns:
                pattern_score = np.mean([p.get('strength', 0) for p in patterns])
            score += pattern_score * 0.3
            
            # Support/Resistance proximity
            sr_levels = technical_analysis.get('support_resistance', [])
            if sr_levels:
                closest_level = min([abs(level['strength']) for level in sr_levels])
                sr_score = 1 - min(closest_level, 1.0)
                score += sr_score * 0.3
            
            # Trend strength
            trend = technical_analysis.get('market_structure', {})
            trend_strength = trend.get('strength', 0)
            score += trend_strength * 0.4
            
            return float(score)
            
        except Exception as e:
            self.logger.error(f"Error in technical score calculation: {str(e)}")
            return 0.0

    def _calculate_volatility_score(self, market_data: Dict) -> float:
        """Calculate volatility opportunity score"""
        try:
            # Recent volatility
            volatility = market_data.get('volatility', 0)
            
            # Normalize volatility score (assume 2% is optimal)
            optimal_vol = 0.02
            vol_score = 1 - min(abs(volatility - optimal_vol) / optimal_vol, 1)
            
            # Consider volume surge
            volume_surge = market_data.get('volume_surge', 1)
            vol_confirmation = min(volume_surge / 2, 1)  # Cap at 2x normal volume
            
            # Combine scores
            return float(vol_score * 0.7 + vol_confirmation * 0.3)
            
        except Exception as e:
            self.logger.error(f"Error in volatility score calculation: {str(e)}")
            return 0.0

    def _calculate_liquidity_score(self, market_data: Dict) -> float:
        """Calculate liquidity score"""
        try:
            # Spread quality
            spread = market_data.get('spread_pct', 100)
            spread_score = max(0, 1 - (spread / 0.1))  # 0.1% spread as baseline
            
            # Order book depth
            depth = market_data.get('order_book_depth', 0)
            depth_score = min(depth / 1000000, 1)  # Normalize to $1M depth
            
            # Recent volume
            volume = market_data.get('volume_24h', 0)
            volume_score = min(volume / 10000000, 1)  # Normalize to $10M daily volume
            
            # Combine scores
            return float(spread_score * 0.4 + depth_score * 0.3 + volume_score * 0.3)
            
        except Exception as e:
            self.logger.error(f"Error in liquidity score calculation: {str(e)}")
            return 0.0

    def _calculate_safety_score(
        self,
        technical_analysis: Dict,
        correlation_data: Dict
    ) -> float:
        """Calculate safety score"""
        try:
            # Manipulation safety
            manipulation = technical_analysis.get('manipulation_metrics', {})
            manip_safety = 1 - manipulation.get('overall_score', 1.0)
            
            # Correlation stability
            stability = correlation_data.get('stability', {})
            cor_stability = np.mean(list(stability.values()) or [0])
            
            # Natural trading metrics
            natural = manipulation.get('natural_trading', {})
            natural_score = natural.get('confidence', 0)
            
            # Combine scores
            return float(manip_safety * 0.4 + cor_stability * 0.3 + natural_score * 0.3)
            
        except Exception as e:
            self.logger.error(f"Error in safety score calculation: {str(e)}")
            return 0.0

    def _calculate_trend_score(
        self,
        technical_analysis: Dict,
        correlation_data: Dict
    ) -> float:
        """Calculate trend score"""
        try:
            # Market structure
            structure = technical_analysis.get('market_structure', {})
            trend_strength = structure.get('strength', 0)
            
            # Trend confirmation from correlations
            trend_cors = correlation_data.get('trend', {}).get('medium_term', {})
            if trend_cors:
                trend_conf = np.mean(list(trend_cors.values()))
            else:
                trend_conf = 0
            
            # Order flow confirmation
            flow = technical_analysis.get('order_flow', {})
            flow_conf = abs(flow.get('delta_momentum', 0.5) - 0.5) * 2  # Convert to 0-1
            
            # Combine scores
            return float(trend_strength * 0.4 + trend_conf * 0.3 + flow_conf * 0.3)
            
        except Exception as e:
            self.logger.error(f"Error in trend score calculation: {str(e)}")
            return 0.0

    def _calculate_volatility_metrics(self, market_data: Dict) -> Dict:
        """Calculate detailed volatility metrics"""
        try:
            volatility = market_data.get('volatility', 0)
            
            return {
                'current_volatility': float(volatility),
                'volatility_trend': market_data.get('volatility_trend', 'stable'),
                'optimal_range': bool(0.01 <= volatility <= 0.03),
                'volume_confirmation': float(market_data.get('volume_surge', 1))
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating volatility metrics: {str(e)}")
            return {}

    def _calculate_risk_metrics(
        self,
        technical_analysis: Dict,
        correlation_data: Dict
    ) -> Dict:
        """Calculate risk metrics for opportunity"""
        try:
            # Get market structure
            structure = technical_analysis.get('market_structure', {})
            
            # Get correlation insights
            correlations = correlation_data.get('price', {}).get('medium_term', {})
            
            return {
                'trend_strength': float(structure.get('strength', 0)),
                'trend_reliability': float(
                    structure.get('reliability', 0)
                ),
                'correlation_risk': float(
                    np.mean(list(correlations.values()) or [0])
                ),
                'manipulation_risk': float(
                    technical_analysis.get('manipulation_metrics', {}).get(
                        'overall_score', 1.0
                    )
                )
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating risk metrics: {str(e)}")
            return {}

    def _update_score_history(
        self,
        symbol: str,
        component_scores: Dict,
        total_score: float
    ):
        """Update score history for stability calculation"""
        if symbol not in self.score_history:
            self.score_history[symbol] = []
            
        self.score_history[symbol].append({
            'timestamp': pd.Timestamp.now(),
            'component_scores': component_scores,
            'total_score': total_score
        })
        
        # Keep only recent history
        max_history = 100
        if len(self.score_history[symbol]) > max_history:
            self.score_history[symbol] = self.score_history[symbol][-max_history:]

    def _calculate_score_stability(self, symbol: str) -> float:
        """Calculate score stability metric"""
        try:
            history = self.score_history.get(symbol, [])
            if len(history) < 2:
                return 0.0
                
            # Calculate score variations
            variations = []
            for i in range(len(history)-1):
                curr = history[i]['total_score']
                next_score = history[i+1]['total_score']
                variations.append(abs(curr - next_score))
                
            if variations:
                return float(1 - np.mean(variations))
            return 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating score stability: {str(e)}")
            return 0.0
