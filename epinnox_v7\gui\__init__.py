"""
GUI Module for Epinnox v7

This module provides the graphical user interface components for the Epinnox v7
LLM-powered crypto futures scalping system, including strategy visualization,
backtesting dashboard, and live simulation monitoring.
"""

from .dashboard import EpinnoxDashboard
from .strategy_tab import StrategyTab
from .parameter_sweeper import ParameterSweeper
from .llm_sim_tab import LLMSimTab

__all__ = ['EpinnoxDashboard', 'StrategyTab', 'ParameterSweeper', 'LLMSimTab']
