"""Technical Analysis module for calculating common indicators across multiple timeframes"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from utils.logger import get_logger
from scipy.signal import argrelextrema
from collections import defaultdict
from sklearn.cluster import DBSCAN

class TechnicalAnalysis:
    """Calculates technical indicators across multiple timeframes"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # Configure timeframes to track (in minutes)
        self.timeframes = [1, 5, 15, 30, 60]  # 1m, 5m, 15m, 30m, 1h
        
        # Historical data cache per symbol and timeframe
        self.price_cache = {}
        self.volume_cache = {}
        self.high_cache = {}
        self.low_cache = {}
        
        # Support/Resistance levels cache
        self.sr_levels = defaultdict(lambda: defaultdict(list))
        
        # Trend line cache
        self.trend_lines = defaultdict(lambda: defaultdict(list))

    def update_data(self, symbol: str, price: float, volume: float, high: float, low: float, timestamp: int):
        """Update historical data caches with new data point"""
        if symbol not in self.price_cache:
            self.price_cache[symbol] = {tf: [] for tf in self.timeframes}
            self.volume_cache[symbol] = {tf: [] for tf in self.timeframes}
            self.high_cache[symbol] = {tf: [] for tf in self.timeframes}
            self.low_cache[symbol] = {tf: [] for tf in self.timeframes}
            
        # Update all timeframes
        for timeframe in self.timeframes:
            self.price_cache[symbol][timeframe].append(price)
            self.volume_cache[symbol][timeframe].append(volume)
            self.high_cache[symbol][timeframe].append(high)
            self.low_cache[symbol][timeframe].append(low)
            
            # Keep only needed history for each timeframe
            max_length = 500  # Increased for better S/R detection
            self.price_cache[symbol][timeframe] = self.price_cache[symbol][timeframe][-max_length:]
            self.volume_cache[symbol][timeframe] = self.volume_cache[symbol][timeframe][-max_length:]
            self.high_cache[symbol][timeframe] = self.high_cache[symbol][timeframe][-max_length:]
            self.low_cache[symbol][timeframe] = self.low_cache[symbol][timeframe][-max_length:]
            
            # Update support/resistance levels periodically
            if len(self.price_cache[symbol][timeframe]) >= 100:
                self._update_support_resistance(symbol, timeframe)
            
    def calculate_indicators(self, symbol: str) -> Dict:
        """Calculate technical indicators for all timeframes"""
        indicators = {}
        
        for timeframe in self.timeframes:
            if symbol not in self.price_cache or timeframe not in self.price_cache[symbol]:
                # Initialize empty indicators
                indicators[f'{timeframe}m'] = self._get_empty_indicators()
                continue
                
            prices = np.array(self.price_cache[symbol][timeframe])
            volumes = np.array(self.volume_cache[symbol][timeframe])
            highs = np.array(self.high_cache[symbol][timeframe])
            lows = np.array(self.low_cache[symbol][timeframe])
            
            if len(prices) < 2:
                indicators[f'{timeframe}m'] = self._get_empty_indicators()
                continue
                
            # Convert to pandas for calculations
            df = pd.DataFrame({
                'close': prices,
                'high': highs,
                'low': lows,
                'volume': volumes
            })
            
            # Calculate indicators
            indicators[f'{timeframe}m'] = {
                'sma_20': self._calculate_sma(df['close'], 20),
                'sma_50': self._calculate_sma(df['close'], 50),
                'sma_200': self._calculate_sma(df['close'], 200),
                'rsi': self._calculate_rsi(df['close'], 14),
                'macd': self._calculate_macd(df['close']),
                'bb': self._calculate_bollinger_bands(df['close'], 20),
                'volume_sma': self._calculate_sma(df['volume'], 20),
                'atr': self._calculate_atr(df, 14),
                'pivot_points': self._calculate_pivot_points(df),
                'support_resistance': self.sr_levels[symbol][timeframe],
                'volume_profile': self._calculate_volume_profile(df),
                'market_structure': self._analyze_market_structure(df),
                'volatility_ratio': self._calculate_volatility_ratio(df),
                'trend_lines': self.trend_lines[symbol][timeframe],
                'fibonacci_levels': self._calculate_fibonacci_levels(df),
                'candlestick_patterns': self._detect_candlestick_patterns(df),
                'chart_patterns': self._detect_chart_patterns(df),
                'manipulation_metrics': self._detect_manipulation(df),
                'order_flow': self._analyze_order_flow(df),
                'correlations': self._analyze_correlations(df),
            }
        
        return indicators
        
    def _get_empty_indicators(self) -> Dict:
        """Return empty indicator structure"""
        return {
            'sma_20': None,
            'sma_50': None,
            'sma_200': None,
            'rsi': None,
            'macd': {'macd': None, 'signal': None, 'hist': None},
            'bb': {'upper': None, 'middle': None, 'lower': None},
            'volume_sma': None,
            'atr': None,
            'pivot_points': {'r3': None, 'r2': None, 'r1': None, 'pp': None, 's1': None, 's2': None, 's3': None},
            'support_resistance': [],
            'volume_profile': [],
            'market_structure': {'trend': None, 'strength': None},
            'volatility_ratio': None,
            'trend_lines': []
        }
        
    def _update_support_resistance(self, symbol: str, timeframe: int):
        """Update support and resistance levels using cluster analysis"""
        prices = np.array(self.price_cache[symbol][timeframe])
        volumes = np.array(self.volume_cache[symbol][timeframe])
        
        if len(prices) < 50:
            return
        
        # Find local maxima and minima
        order = int(len(prices) * 0.1)  # 10% of the data length for local extrema
        maxima = argrelextrema(prices, np.greater, order=order)[0]
        minima = argrelextrema(prices, np.less, order=order)[0]
        
        # Collect all potential S/R points
        sr_points = []
        
        # Process resistance (maxima)
        for max_idx in maxima:
            price_level = prices[max_idx]
            volume_weight = volumes[max_idx] / np.mean(volumes)
            sr_points.append({
                'price': float(price_level),
                'type': 'resistance',
                'volume': float(volume_weight),
                'index': int(max_idx)
            })
        
        # Process support (minima)
        for min_idx in minima:
            price_level = prices[min_idx]
            volume_weight = volumes[min_idx] / np.mean(volumes)
            sr_points.append({
                'price': float(price_level),
                'type': 'support',
                'volume': float(volume_weight),
                'index': int(min_idx)
            })
        
        if not sr_points:
            return
        
        # Perform cluster analysis
        price_array = np.array([p['price'] for p in sr_points]).reshape(-1, 1)
        
        # Determine optimal number of clusters
        max_clusters = min(10, len(sr_points))
        if max_clusters < 2:
            return
            
        # Use standard deviation to determine epsilon for DBSCAN
        price_std = np.std([p['price'] for p in sr_points])
        eps = price_std * 0.01  # 1% of price standard deviation
        
        # Apply DBSCAN clustering
        clustering = DBSCAN(eps=eps, min_samples=2).fit(price_array)
        
        # Process clusters
        clusters = {}
        for i, label in enumerate(clustering.labels_):
            if label == -1:  # Skip noise points
                continue
            if label not in clusters:
                clusters[label] = []
            clusters[label].append(sr_points[i])
        
        # Calculate cluster metrics
        levels = []
        for cluster in clusters.values():
            # Calculate weighted average price for the cluster
            total_weight = sum(point['volume'] for point in cluster)
            avg_price = sum(point['price'] * point['volume'] for point in cluster) / total_weight
            
            # Determine cluster type (majority voting)
            support_count = sum(1 for point in cluster if point['type'] == 'support')
            resistance_count = len(cluster) - support_count
            cluster_type = 'support' if support_count > resistance_count else 'resistance'
            
            # Calculate cluster strength based on:
            # 1. Number of points in cluster
            # 2. Total volume
            # 3. Recency of points
            cluster_size_score = len(cluster) / len(sr_points)
            volume_score = total_weight / len(cluster)
            
            # Calculate recency score (more recent points have higher weight)
            max_idx = max(point['index'] for point in cluster)
            recency_score = max_idx / len(prices)
            
            # Combine scores
            strength = (cluster_size_score + volume_score + recency_score) / 3
            
            # Count touches (price approaching within 0.1%)
            touches = sum(1 for price in prices 
                        if abs(price - avg_price) < avg_price * 0.001)
            
            levels.append({
                'price': float(avg_price),
                'type': cluster_type,
                'strength': float(strength),
                'touches': int(touches),
                'points_in_cluster': len(cluster)
            })
        
        # Sort by strength and keep top levels
        levels.sort(key=lambda x: x['strength'] * x['points_in_cluster'], reverse=True)
        self.sr_levels[symbol][timeframe] = levels[:10]
        
    def _calculate_sma(self, series: pd.Series, period: int) -> float:
        """Calculate Simple Moving Average"""
        if len(series) < period:
            return None
        return float(series.rolling(window=period).mean().iloc[-1])
        
    def _calculate_rsi(self, series: pd.Series, period: int) -> float:
        """Calculate Relative Strength Index"""
        if len(series) < period + 1:
            return None
            
        delta = series.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return float(rsi.iloc[-1])
        
    def _calculate_macd(self, series: pd.Series) -> Dict:
        """Calculate MACD (12,26,9)"""
        if len(series) < 26:
            return {'macd': None, 'signal': None, 'hist': None}
            
        exp1 = series.ewm(span=12, adjust=False).mean()
        exp2 = series.ewm(span=26, adjust=False).mean()
        macd = exp1 - exp2
        signal = macd.ewm(span=9, adjust=False).mean()
        hist = macd - signal
        
        return {
            'macd': float(macd.iloc[-1]),
            'signal': float(signal.iloc[-1]),
            'hist': float(hist.iloc[-1])
        }
        
    def _calculate_bollinger_bands(self, series: pd.Series, period: int) -> Dict:
        """Calculate Bollinger Bands"""
        if len(series) < period:
            return {'upper': None, 'middle': None, 'lower': None}
            
        middle = series.rolling(window=period).mean()
        std = series.rolling(window=period).std()
        
        return {
            'upper': float(middle.iloc[-1] + (std.iloc[-1] * 2)),
            'middle': float(middle.iloc[-1]),
            'lower': float(middle.iloc[-1] - (std.iloc[-1] * 2))
        }
        
    def _calculate_atr(self, df: pd.DataFrame, period: int) -> float:
        """Calculate Average True Range"""
        if len(df) < period:
            return None
            
        high = df['high']
        low = df['low']
        close = df['close']
        
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean()
        
        return float(atr.iloc[-1])
        
    def _calculate_pivot_points(self, df: pd.DataFrame) -> Dict:
        """Calculate Pivot Points (Floor Method)"""
        if len(df) < 1:
            return {'r3': None, 'r2': None, 'r1': None, 'pp': None, 's1': None, 's2': None, 's3': None}
            
        high = df['high'].iloc[-1]
        low = df['low'].iloc[-1]
        close = df['close'].iloc[-1]
        
        pp = (high + low + close) / 3
        r1 = 2 * pp - low
        s1 = 2 * pp - high
        r2 = pp + (high - low)
        s2 = pp - (high - low)
        r3 = high + 2 * (pp - low)
        s3 = low - 2 * (high - pp)
        
        return {
            'r3': float(r3),
            'r2': float(r2),
            'r1': float(r1),
            'pp': float(pp),
            's1': float(s1),
            's2': float(s2),
            's3': float(s3)
        }
        
    def _calculate_volume_profile(self, df: pd.DataFrame) -> List:
        """Calculate Volume Profile"""
        if len(df) < 10:
            return []
            
        # Create price bins
        price_range = df['close'].max() - df['close'].min()
        bin_size = price_range / 10
        bins = np.linspace(df['close'].min(), df['close'].max(), 11)
        
        # Calculate volume per price level
        vol_profile = []
        for i in range(len(bins)-1):
            mask = (df['close'] >= bins[i]) & (df['close'] < bins[i+1])
            volume = df.loc[mask, 'volume'].sum()
            vol_profile.append({
                'price_level': float(bins[i]),
                'volume': float(volume)
            })
            
        return vol_profile
        
    def _analyze_market_structure(self, df: pd.DataFrame) -> Dict:
        """Analyze market structure (HH/HL pattern)"""
        if len(df) < 10:
            return {'trend': None, 'strength': None}
            
        # Find swing highs and lows
        highs = df['high'].rolling(5, center=True).max()
        lows = df['low'].rolling(5, center=True).min()
        
        # Check for higher highs and higher lows (uptrend)
        higher_highs = (highs.diff() > 0).sum() / len(highs)
        higher_lows = (lows.diff() > 0).sum() / len(lows)
        
        trend_strength = higher_highs + higher_lows - 1  # -1 to 1 scale
        
        trend = 'uptrend' if trend_strength > 0.2 else 'downtrend' if trend_strength < -0.2 else 'sideways'
        
        return {
            'trend': trend,
            'strength': float(abs(trend_strength))
        }
        
    def _calculate_volatility_ratio(self, df: pd.DataFrame) -> float:
        """Calculate volatility ratio (current vs. historical)"""
        if len(df) < 20:
            return None
            
        current_vol = df['close'].tail(10).std()
        historical_vol = df['close'].head(len(df)-10).std()
        
        return float(current_vol / historical_vol) if historical_vol != 0 else None
    
    def _detect_trend_lines(self, symbol: str, timeframe: int):
        """Detect trend lines using linear regression on swing points"""
        prices = np.array(self.price_cache[symbol][timeframe])
        if len(prices) < 20:
            return
            
        # Find swing highs and lows
        highs = self.high_cache[symbol][timeframe]
        lows = self.low_cache[symbol][timeframe]
        
        # Find potential trend line points using local extrema
        window = 5  # Window size for local extrema
        high_points = []
        low_points = []
        
        for i in range(window, len(highs) - window):
            # Check if this is a local high
            if highs[i] == max(highs[i-window:i+window+1]):
                high_points.append((i, highs[i]))
            # Check if this is a local low    
            if lows[i] == min(lows[i-window:i+window+1]):
                low_points.append((i, lows[i]))
        
        trend_lines = []
        
        # Find resistance trend lines (connect highs)
        if len(high_points) >= 2:
            for i in range(len(high_points)-1):
                for j in range(i+1, len(high_points)):
                    x1, y1 = high_points[i]
                    x2, y2 = high_points[j]
                    
                    # Calculate line parameters
                    slope = (y2 - y1) / (x2 - x1)
                    intercept = y1 - slope * x1
                    
                    # Check if other points are below this line (with small tolerance)
                    points_below = sum(1 for x, y in high_points 
                                    if y <= slope * x + intercept + (y1 * 0.001))
                    
                    if points_below / len(high_points) >= 0.8:  # At least 80% points below
                        strength = points_below / len(high_points)
                        trend_lines.append({
                            'type': 'resistance',
                            'slope': float(slope),
                            'intercept': float(intercept),
                            'start_x': int(x1),
                            'end_x': int(x2),
                            'strength': float(strength)
                        })
        
        # Find support trend lines (connect lows)
        if len(low_points) >= 2:
            for i in range(len(low_points)-1):
                for j in range(i+1, len(low_points)):
                    x1, y1 = low_points[i]
                    x2, y2 = low_points[j]
                    
                    # Calculate line parameters
                    slope = (y2 - y1) / (x2 - x1)
                    intercept = y1 - slope * x1
                    
                    # Check if other points are above this line (with small tolerance)
                    points_above = sum(1 for x, y in low_points 
                                    if y >= slope * x + intercept - (y1 * 0.001))
                    
                    if points_above / len(low_points) >= 0.8:  # At least 80% points above
                        strength = points_above / len(low_points)
                        trend_lines.append({
                            'type': 'support',
                            'slope': float(slope),
                            'intercept': float(intercept),
                            'start_x': int(x1),
                            'end_x': int(x2),
                            'strength': float(strength)
                        })
        
        # Sort by strength and keep top 5 of each type
        trend_lines.sort(key=lambda x: x['strength'], reverse=True)
        resistance_lines = [t for t in trend_lines if t['type'] == 'resistance'][:5]
        support_lines = [t for t in trend_lines if t['type'] == 'support'][:5]
        
        self.trend_lines[symbol][timeframe] = resistance_lines + support_lines
    
    def _calculate_fibonacci_levels(self, df: pd.DataFrame) -> Dict:
        """Calculate Fibonacci retracement and extension levels"""
        if len(df) < 2:
            return {}
            
        # Find recent significant swing high and low
        window = 20  # Look back period for swing points
        recent_data = df.tail(window)
        
        high = recent_data['high'].max()
        low = recent_data['low'].min()
        high_idx = recent_data['high'].idxmax()
        low_idx = recent_data['low'].idxmin()
        
        # Determine trend direction
        trend = 'uptrend' if high_idx > low_idx else 'downtrend'
        price_range = high - low
        
        # Fibonacci ratios
        fib_retracement = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1]
        fib_extension = [1.272, 1.414, 1.618, 2.000, 2.414, 2.618]
        
        levels = {
            'trend': trend,
            'swing_high': float(high),
            'swing_low': float(low),
            'retracement_levels': {},
            'extension_levels': {}
        }
        
        # Calculate retracement levels
        for ratio in fib_retracement:
            if trend == 'uptrend':
                level = high - (price_range * ratio)
            else:
                level = low + (price_range * ratio)
            levels['retracement_levels'][str(ratio)] = float(level)
        
        # Calculate extension levels
        for ratio in fib_extension:
            if trend == 'uptrend':
                level = high + (price_range * (ratio - 1))
            else:
                level = low - (price_range * (ratio - 1))
            levels['extension_levels'][str(ratio)] = float(level)
        
        return levels
    
    def _detect_candlestick_patterns(self, df: pd.DataFrame) -> List[Dict]:
        """Detect Japanese candlestick patterns"""
        if len(df) < 3:
            return []
            
        patterns = []
        latest_candles = df.tail(3)  # We'll look at last 3 candles
        
        # Helper function to determine if a candle is bullish or bearish
        def is_bullish(open_price, close_price):
            return close_price > open_price
        
        def body_size(open_price, close_price):
            return abs(close_price - open_price)
        
        def upper_shadow(open_price, close_price, high):
            return high - max(open_price, close_price)
        
        def lower_shadow(open_price, close_price, low):
            return min(open_price, close_price) - low
        
        # Get latest candle data
        current = latest_candles.iloc[-1]
        prev = latest_candles.iloc[-2]
        prev2 = latest_candles.iloc[-3]
        
        # Doji
        if body_size(current['open'], current['close']) <= (current['high'] - current['low']) * 0.1:
            patterns.append({
                'name': 'doji',
                'type': 'reversal',
                'strength': 0.7
            })
        
        # Hammer/Hanging Man
        body_range = body_size(current['open'], current['close'])
        lower_wick = lower_shadow(current['open'], current['close'], current['low'])
        upper_wick = upper_shadow(current['open'], current['close'], current['high'])
        
        if lower_wick > body_range * 2 and upper_wick < body_range * 0.5:
            pattern_type = 'hammer' if not is_bullish(prev['open'], prev['close']) else 'hanging_man'
            patterns.append({
                'name': pattern_type,
                'type': 'reversal',
                'strength': 0.8
            })
        
        # Engulfing
        if (current['open'] < prev['close'] and current['close'] > prev['open'] and 
            is_bullish(current['open'], current['close']) and 
            not is_bullish(prev['open'], prev['close'])):
            patterns.append({
                'name': 'bullish_engulfing',
                'type': 'reversal',
                'strength': 0.9
            })
        elif (current['open'] > prev['close'] and current['close'] < prev['open'] and 
              not is_bullish(current['open'], current['close']) and 
              is_bullish(prev['open'], prev['close'])):
            patterns.append({
                'name': 'bearish_engulfing',
                'type': 'reversal',
                'strength': 0.9
            })
        
        # Morning/Evening Star
        if (not is_bullish(prev2['open'], prev2['close']) and
            body_size(prev['open'], prev['close']) <= (prev['high'] - prev['low']) * 0.3 and
            is_bullish(current['open'], current['close'])):
            patterns.append({
                'name': 'morning_star',
                'type': 'reversal',
                'strength': 0.95
            })
        elif (is_bullish(prev2['open'], prev2['close']) and
              body_size(prev['open'], prev['close']) <= (prev['high'] - prev['low']) * 0.3 and
              not is_bullish(current['open'], current['close'])):
            patterns.append({
                'name': 'evening_star',
                'type': 'reversal',
                'strength': 0.95
            })
        
        return patterns

    def _detect_chart_patterns(self, df: pd.DataFrame) -> List[Dict]:
        """Detect technical chart patterns"""
        if len(df) < 20:
            return []
            
        patterns = []
        
        # Get recent price action
        recent_highs = df['high'].tail(20)
        recent_lows = df['low'].tail(20)
        recent_closes = df['close'].tail(20)
        
        # Double Top
        if self._is_double_top(recent_highs):
            patterns.append({
                'name': 'double_top',
                'type': 'reversal',
                'strength': 0.85
            })
        
        # Double Bottom
        if self._is_double_bottom(recent_lows):
            patterns.append({
                'name': 'double_bottom',
                'type': 'reversal',
                'strength': 0.85
            })
        
        # Head and Shoulders
        if self._is_head_and_shoulders(recent_highs):
            patterns.append({
                'name': 'head_and_shoulders',
                'type': 'reversal',
                'strength': 0.9
            })
        
        # Inverse Head and Shoulders
        if self._is_inverse_head_and_shoulders(recent_lows):
            patterns.append({
                'name': 'inverse_head_and_shoulders',
                'type': 'reversal',
                'strength': 0.9
            })
        
        # Triangle Patterns
        triangle = self._detect_triangle_pattern(recent_highs, recent_lows)
        if triangle:
            patterns.append(triangle)
        
        return patterns

    def _is_double_top(self, highs: pd.Series) -> bool:
        """Detect double top pattern"""
        peaks = argrelextrema(highs.values, np.greater, order=3)[0]
        if len(peaks) < 2:
            return False
            
        # Get the two highest peaks
        peak_values = highs.iloc[peaks]
        highest_peaks = peak_values.nlargest(2)
        
        # Check if peaks are within 1% of each other
        return abs(highest_peaks.iloc[0] - highest_peaks.iloc[1]) <= highest_peaks.iloc[0] * 0.01

    def _is_double_bottom(self, lows: pd.Series) -> bool:
        """Detect double bottom pattern"""
        troughs = argrelextrema(lows.values, np.less, order=3)[0]
        if len(troughs) < 2:
            return False
            
        # Get the two lowest troughs
        trough_values = lows.iloc[troughs]
        lowest_troughs = trough_values.nsmallest(2)
        
        # Check if troughs are within 1% of each other
        return abs(lowest_troughs.iloc[0] - lowest_troughs.iloc[1]) <= lowest_troughs.iloc[0] * 0.01

    def _is_head_and_shoulders(self, highs: pd.Series) -> bool:
        """Detect head and shoulders pattern"""
        peaks = argrelextrema(highs.values, np.greater, order=3)[0]
        if len(peaks) < 3:
            return False
            
        # Get three consecutive peaks
        peak_values = highs.iloc[peaks][-3:]
        
        # Check if middle peak is highest and shoulders are within 2% of each other
        return (peak_values.iloc[1] > peak_values.iloc[0] and 
                peak_values.iloc[1] > peak_values.iloc[2] and
                abs(peak_values.iloc[0] - peak_values.iloc[2]) <= peak_values.iloc[0] * 0.02)

    def _is_inverse_head_and_shoulders(self, lows: pd.Series) -> bool:
        """Detect inverse head and shoulders pattern"""
        troughs = argrelextrema(lows.values, np.less, order=3)[0]
        if len(troughs) < 3:
            return False
            
        # Get three consecutive troughs
        trough_values = lows.iloc[troughs][-3:]
        
        # Check if middle trough is lowest and shoulders are within 2% of each other
        return (trough_values.iloc[1] < trough_values.iloc[0] and 
                trough_values.iloc[1] < trough_values.iloc[2] and
                abs(trough_values.iloc[0] - trough_values.iloc[2]) <= trough_values.iloc[0] * 0.02)

    def _detect_triangle_pattern(self, highs: pd.Series, lows: pd.Series) -> Optional[Dict]:
        """Detect triangle patterns (ascending, descending, symmetric)"""
        # Get linear regression slopes for highs and lows
        x = np.arange(len(highs))
        high_slope = np.polyfit(x, highs, 1)[0]
        low_slope = np.polyfit(x, lows, 1)[0]
        
        # Determine triangle type based on slopes
        if abs(high_slope) < 0.0001 and low_slope > 0.0001:
            return {
                'name': 'ascending_triangle',
                'type': 'continuation',
                'strength': 0.8
            }
        elif high_slope < -0.0001 and abs(low_slope) < 0.0001:
            return {
                'name': 'descending_triangle',
                'type': 'continuation',
                'strength': 0.8
            }
        elif abs(high_slope + low_slope) < 0.0001:
            return {
                'name': 'symmetric_triangle',
                'type': 'continuation',
                'strength': 0.75
            }
        
        return None
    
    def _detect_manipulation(self, df: pd.DataFrame) -> Dict:
        """Detect potential market manipulation patterns"""
        if len(df) < 50:
            return {}
            
        metrics = {
            'spoofing_indicators': self._detect_spoofing(df),
            'wash_trading': self._detect_wash_trading(df),
            'momentum_ignition': self._detect_momentum_ignition(df),
            'layering_indicators': self._detect_layering(df),
            'natural_trading': self._evaluate_natural_trading(df)
        }
        
        # Calculate overall manipulation score (0-1, where 0 is natural trading)
        manipulation_signals = [
            metrics['spoofing_indicators']['score'],
            metrics['wash_trading']['score'],
            metrics['momentum_ignition']['score'],
            metrics['layering_indicators']['score']
        ]
        
        metrics['overall_score'] = 1 - metrics['natural_trading']['confidence']
        metrics['safe_to_trade'] = metrics['overall_score'] < 0.3  # Consider safe if manipulation score < 0.3
        
        return metrics

    def _detect_spoofing(self, df: pd.DataFrame) -> Dict:
        """Detect potential spoofing patterns"""
        # Calculate volume profile and price movements
        volume_std = df['volume'].std()
        price_changes = df['close'].diff()
        
        # Look for large orders that get cancelled (indicated by volume spikes followed by price reversals)
        volume_spikes = df['volume'] > (df['volume'].mean() + 2 * volume_std)
        price_reversals = (price_changes * price_changes.shift(1)) < 0
        
        # Calculate spoofing score based on correlation of volume spikes and price reversals
        spoof_events = (volume_spikes & price_reversals).sum()
        spoof_score = min(1.0, spoof_events / len(df))
        
        return {
            'score': float(spoof_score),
            'volume_spikes_count': int(volume_spikes.sum()),
            'price_reversal_count': int(price_reversals.sum())
        }

    def _detect_wash_trading(self, df: pd.DataFrame) -> Dict:
        """Detect potential wash trading patterns"""
        # Look for repetitive trades with similar volumes
        volumes = df['volume'].values
        
        # Calculate volume pattern repetition
        volume_changes = np.diff(volumes)
        volume_pattern_score = np.corrcoef(volume_changes[:-1], volume_changes[1:])[0, 1]
        volume_pattern_score = max(0, volume_pattern_score)  # Only consider positive correlations
        
        # Check for unusual volume patterns without price impact
        price_impact = abs(df['close'] - df['open']) / df['volume']
        unusual_patterns = (price_impact < price_impact.mean() * 0.5).sum() / len(df)
        
        wash_score = (volume_pattern_score + unusual_patterns) / 2
        
        return {
            'score': float(wash_score),
            'volume_pattern_score': float(volume_pattern_score),
            'low_impact_trades': float(unusual_patterns)
        }

    def _detect_momentum_ignition(self, df: pd.DataFrame) -> Dict:
        """Detect potential momentum ignition patterns"""
        # Look for sudden price movements with high volume followed by reversals
        returns = df['close'].pct_change()
        volume_ratio = df['volume'] / df['volume'].rolling(20).mean()
        
        # Detect aggressive price movements
        aggressive_moves = (abs(returns) > returns.std() * 2) & (volume_ratio > 2)
        
        # Check for reversals after aggressive moves
        reversals = []
        for i in range(len(df)-1):
            if aggressive_moves.iloc[i]:
                # Check if price reverses in the next few candles
                future_return = df['close'].iloc[i+1:i+5].pct_change().sum()
                reversals.append(returns.iloc[i] * future_return < 0)
        
        if not reversals:
            return {'score': 0.0, 'aggressive_moves': 0, 'reversal_rate': 0.0}
            
        reversal_rate = sum(reversals) / len(reversals)
        ignition_score = min(1.0, (aggressive_moves.sum() / len(df)) * reversal_rate)
        
        return {
            'score': float(ignition_score),
            'aggressive_moves': int(aggressive_moves.sum()),
            'reversal_rate': float(reversal_rate)
        }

    def _detect_layering(self, df: pd.DataFrame) -> Dict:
        """Detect potential layering patterns"""
        # Analyze price levels and volume distribution
        price_levels = pd.qcut(df['close'], q=10, duplicates='drop')
        volume_per_level = df.groupby(price_levels)['volume'].sum()
        
        # Calculate volume concentration
        volume_concentration = (volume_per_level.max() / volume_per_level.sum())
        
        # Detect one-sided pressure
        price_pressure = (df['close'] - df['open']).rolling(10).sum()
        one_sided_pressure = abs(price_pressure).mean() / df['close'].std()
        
        layering_score = min(1.0, (volume_concentration + one_sided_pressure) / 3)
        
        return {
            'score': float(layering_score),
            'volume_concentration': float(volume_concentration),
            'price_pressure': float(one_sided_pressure)
        }

    def _evaluate_natural_trading(self, df: pd.DataFrame) -> Dict:
        """Evaluate indicators of natural trading activity"""
        # Calculate metrics that indicate natural market behavior
        
        # 1. Volume consistency
        volume_consistency = 1 - (df['volume'].std() / df['volume'].mean())
        
        # 2. Price-volume correlation (should be positive in natural markets)
        price_changes = df['close'].pct_change()
        volume_correlation = price_changes.abs().corr(df['volume'])
        
        # 3. Bid-ask spread stability (using high-low as proxy)
        spread_proxy = (df['high'] - df['low']) / df['close']
        spread_stability = 1 - spread_proxy.std()
        
        # 4. Market efficiency coefficient (random walk = 0.5)
        returns = np.log(df['close'] / df['close'].shift(1))
        hurst_exp = self._calculate_hurst_exponent(returns.dropna())
        efficiency_score = 1 - abs(hurst_exp - 0.5)
        
        # Combine metrics
        natural_score = np.mean([
            volume_consistency,
            volume_correlation,
            spread_stability,
            efficiency_score
        ])
        
        return {
            'confidence': float(natural_score),
            'volume_consistency': float(volume_consistency),
            'price_volume_correlation': float(volume_correlation),
            'spread_stability': float(spread_stability),
            'market_efficiency': float(efficiency_score)
        }

    def _calculate_hurst_exponent(self, returns: pd.Series) -> float:
        """Calculate Hurst exponent to measure randomness vs. trend-following"""
        if len(returns) < 100:
            return 0.5
            
        # Calculate range of cumulative returns
        cumulative = returns.cumsum()
        rolling_mean = returns.rolling(window=len(returns), min_periods=1).mean()
        z = cumulative - rolling_mean
        r = z.max() - z.min()  # Range
        s = returns.std()  # Standard deviation
        
        if s == 0:
            return 0.5
            
        return np.log(r/s) / np.log(len(returns)/2)
    
    def _analyze_order_flow(self, df: pd.DataFrame) -> Dict:
        """Analyze order flow to detect institutional activity"""
        if len(df) < 50:
            return {}
            
        # Calculate delta (buy vs sell volume)
        df['delta'] = (df['close'] - df['open']) * df['volume']
        
        # Identify large orders (institutional activity)
        volume_mean = df['volume'].mean()
        volume_std = df['volume'].std()
        large_orders = df[df['volume'] > volume_mean + (2 * volume_std)]
        
        # Analyze cumulative delta
        cumulative_delta = df['delta'].cumsum()
        
        # Calculate buying/selling pressure
        buying_pressure = df[df['delta'] > 0]['delta'].sum()
        selling_pressure = abs(df[df['delta'] < 0]['delta'].sum())
        
        # Detect absorption (large volume with little price movement)
        price_impact = abs(df['close'] - df['open']) / df['volume']
        absorption_zones = df[price_impact < price_impact.mean() * 0.5]
        
        # Identify institutional levels (price levels with high volume and absorption)
        price_levels = pd.qcut(df['close'], q=10, duplicates='drop')
        level_metrics = df.groupby(price_levels).agg({
            'volume': 'sum',
            'delta': 'sum'
        })
        
        institutional_levels = []
        for level in level_metrics.index:
            metrics = level_metrics.loc[level]
            if metrics['volume'] > volume_mean * 2:
                institutional_levels.append({
                    'price': float(level.mid),
                    'volume': float(metrics['volume']),
                    'delta': float(metrics['delta']),
                    'type': 'accumulation' if metrics['delta'] > 0 else 'distribution'
                })
        
        return {
            'institutional_levels': institutional_levels,
            'buying_pressure': float(buying_pressure),
            'selling_pressure': float(selling_pressure),
            'large_orders_count': len(large_orders),
            'absorption_zones': [
                {
                    'price': float(row['close']),
                    'volume': float(row['volume']),
                    'delta': float(row['delta'])
                }
                for _, row in absorption_zones.iterrows()
            ],
            'cumulative_delta': float(cumulative_delta.iloc[-1]),
            'delta_momentum': float((cumulative_delta.diff().tail(10) > 0).mean())
        }
    
    def _analyze_correlations(self, df: pd.DataFrame) -> Dict:
        """Analyze correlations with related instruments"""
        # This would normally pull data for correlated assets
        # For now we'll return a mock structure that you would populate with real data
        return {
            'correlation_metrics': {
                'market_correlation': {
                    'btc_correlation': 0.85,  # Example correlation with BTC
                    'eth_correlation': 0.75,  # Example correlation with ETH
                    'sector_correlation': 0.65,  # Example correlation with sector
                },
                'divergences': {
                    'price_volume_divergence': self._check_price_volume_divergence(df),
                    'momentum_divergence': self._check_momentum_divergence(df),
                    'relative_strength_divergence': self._check_relative_strength_divergence(df)
                },
                'rotation_analysis': {
                    'sector_rotation': 'risk_on',  # Example sector rotation state
                    'capital_flow': 'inflow',  # Example capital flow direction
                    'relative_volume': 1.25  # Example relative volume vs sector
                }
            }
        }

    def _check_price_volume_divergence(self, df: pd.DataFrame) -> Dict:
        """Check for price-volume divergences"""
        if len(df) < 10:
            return {'present': False, 'type': None, 'strength': 0}
            
        # Calculate recent price and volume trends
        price_trend = df['close'].tail(10).pct_change().mean()
        volume_trend = df['volume'].tail(10).pct_change().mean()
        
        # Check for divergences
        bullish_divergence = price_trend < 0 and volume_trend > 0
        bearish_divergence = price_trend > 0 and volume_trend < 0
        
        if bullish_divergence or bearish_divergence:
            divergence_type = 'bullish' if bullish_divergence else 'bearish'
            strength = abs(price_trend - volume_trend)
            return {
                'present': True,
                'type': divergence_type,
                'strength': float(strength)
            }
        
        return {'present': False, 'type': None, 'strength': 0}

    def _check_momentum_divergence(self, df: pd.DataFrame) -> Dict:
        """Check for momentum divergences using RSI"""
        if len(df) < 14:
            return {'present': False, 'type': None, 'strength': 0}
            
        # Calculate RSI
        rsi = self._calculate_rsi(df['close'], 14)
        
        # Get price and RSI trends
        price_higher = df['close'].iloc[-1] > df['close'].iloc[-2]
        rsi_higher = rsi[-1] > rsi[-2]
        
        # Check for divergences
        bullish_divergence = not price_higher and rsi_higher
        bearish_divergence = price_higher and not rsi_higher
        
        if bullish_divergence or bearish_divergence:
            divergence_type = 'bullish' if bullish_divergence else 'bearish'
            strength = abs(df['close'].pct_change().iloc[-1] - (rsi[-1] - rsi[-2])/100)
            return {
                'present': True,
                'type': divergence_type,
                'strength': float(strength)
            }
        
        return {'present': False, 'type': None, 'strength': 0}

    def _check_relative_strength_divergence(self, df: pd.DataFrame) -> Dict:
        """Check for relative strength divergences against market"""
        # This would normally compare against market benchmark
        # For now return a mock structure
        return {
            'present': False,
            'type': None,
            'strength': 0
        }
