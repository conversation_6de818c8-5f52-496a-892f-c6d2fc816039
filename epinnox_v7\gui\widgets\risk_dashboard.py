"""
Risk Dashboard Widget for Epinnox v7

Real-time risk metrics and circuit breaker status monitoring.
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QProgressBar, QGroupBox, QFrame,
    QPushButton, QTableWidget, QTableWidgetItem,
    QHeaderView, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette
from typing import Dict, Any, List
from datetime import datetime, timedelta

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from utils.logger import get_logger

logger = get_logger()


class RiskDashboardWidget(QWidget):
    """Risk monitoring and circuit breaker dashboard"""
    
    # Signals
    risk_limit_breached = pyqtSignal(str, float)  # metric, value
    circuit_breaker_triggered = pyqtSignal(str)   # reason
    emergency_stop_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger()
        
        # Risk data
        self.risk_metrics = {
            'daily_pnl': 0.0,
            'daily_pnl_pct': 0.0,
            'unrealized_pnl': 0.0,
            'margin_usage': 0.0,
            'margin_ratio': 0.0,
            'max_drawdown': 0.0,
            'consecutive_losses': 0,
            'volatility': 0.0,
            'correlation_risk': 0.0
        }
        
        self.risk_limits = {
            'max_daily_loss_usd': 1000.0,
            'max_daily_loss_pct': 5.0,
            'max_margin_ratio': 0.8,
            'max_drawdown_pct': 10.0,
            'max_consecutive_losses': 5,
            'max_volatility': 0.1,
            'max_correlation': 0.7
        }
        
        self.circuit_breaker_status = {
            'daily_loss_halt': False,
            'margin_halt': False,
            'volatility_halt': False,
            'consecutive_loss_halt': False,
            'emergency_stop': False
        }
        
        # UI Setup
        self.setup_ui()
        self.setup_styling()
        
        # Update timer (will only update when called with real data)
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_displays)
        self.update_timer.start(5000)  # Refresh displays every 5 seconds
        
        # Initialize with real data (zeros until positions exist)
        self.load_real_data()
        
        self.logger.info("Risk Dashboard Widget initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Header
        header_layout = QHBoxLayout()
        
        title = QLabel("Risk Dashboard")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Overall risk status
        self.risk_status_label = QLabel("🟢 Risk Status: Normal")
        self.risk_status_label.setFont(QFont("Arial", 12, QFont.Bold))
        header_layout.addWidget(self.risk_status_label)
        
        layout.addLayout(header_layout)
        
        # Risk metrics grid
        metrics_layout = QHBoxLayout()
        
        # Left column - P&L and Margin
        left_column = self.create_pnl_margin_section()
        metrics_layout.addWidget(left_column)
        
        # Right column - Risk limits and circuit breakers
        right_column = self.create_risk_limits_section()
        metrics_layout.addWidget(right_column)
        
        layout.addLayout(metrics_layout)
        
        # Circuit breaker status
        self.circuit_breaker_section = self.create_circuit_breaker_section()
        layout.addWidget(self.circuit_breaker_section)
        
        # Risk alerts table
        self.alerts_table = self.create_alerts_table()
        layout.addWidget(self.alerts_table)
    
    def create_pnl_margin_section(self) -> QGroupBox:
        """Create P&L and margin monitoring section"""
        group = QGroupBox("P&L & Margin")
        layout = QGridLayout(group)
        
        # Daily P&L
        layout.addWidget(QLabel("Daily P&L:"), 0, 0)
        self.daily_pnl_label = QLabel("$0.00")
        self.daily_pnl_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(self.daily_pnl_label, 0, 1)
        
        self.daily_pnl_bar = QProgressBar()
        self.daily_pnl_bar.setRange(-1000, 1000)
        self.daily_pnl_bar.setValue(0)
        layout.addWidget(self.daily_pnl_bar, 0, 2)
        
        # Daily P&L %
        layout.addWidget(QLabel("Daily P&L %:"), 1, 0)
        self.daily_pnl_pct_label = QLabel("0.00%")
        layout.addWidget(self.daily_pnl_pct_label, 1, 1)
        
        self.daily_pnl_pct_bar = QProgressBar()
        self.daily_pnl_pct_bar.setRange(-10, 10)
        self.daily_pnl_pct_bar.setValue(0)
        layout.addWidget(self.daily_pnl_pct_bar, 1, 2)
        
        # Unrealized P&L
        layout.addWidget(QLabel("Unrealized P&L:"), 2, 0)
        self.unrealized_pnl_label = QLabel("$0.00")
        layout.addWidget(self.unrealized_pnl_label, 2, 1)
        
        # Margin Usage
        layout.addWidget(QLabel("Margin Usage:"), 3, 0)
        self.margin_usage_label = QLabel("0.00%")
        layout.addWidget(self.margin_usage_label, 3, 1)
        
        self.margin_usage_bar = QProgressBar()
        self.margin_usage_bar.setRange(0, 100)
        self.margin_usage_bar.setValue(0)
        layout.addWidget(self.margin_usage_bar, 3, 2)
        
        # Max Drawdown
        layout.addWidget(QLabel("Max Drawdown:"), 4, 0)
        self.max_drawdown_label = QLabel("0.00%")
        layout.addWidget(self.max_drawdown_label, 4, 1)
        
        self.max_drawdown_bar = QProgressBar()
        self.max_drawdown_bar.setRange(0, 20)
        self.max_drawdown_bar.setValue(0)
        layout.addWidget(self.max_drawdown_bar, 4, 2)
        
        return group
    
    def create_risk_limits_section(self) -> QGroupBox:
        """Create risk limits monitoring section"""
        group = QGroupBox("Risk Limits")
        layout = QGridLayout(group)
        
        # Consecutive Losses
        layout.addWidget(QLabel("Consecutive Losses:"), 0, 0)
        self.consecutive_losses_label = QLabel("0 / 5")
        layout.addWidget(self.consecutive_losses_label, 0, 1)
        
        self.consecutive_losses_bar = QProgressBar()
        self.consecutive_losses_bar.setRange(0, 5)
        self.consecutive_losses_bar.setValue(0)
        layout.addWidget(self.consecutive_losses_bar, 0, 2)
        
        # Volatility
        layout.addWidget(QLabel("Market Volatility:"), 1, 0)
        self.volatility_label = QLabel("0.00%")
        layout.addWidget(self.volatility_label, 1, 1)
        
        self.volatility_bar = QProgressBar()
        self.volatility_bar.setRange(0, 20)
        self.volatility_bar.setValue(0)
        layout.addWidget(self.volatility_bar, 1, 2)
        
        # Correlation Risk
        layout.addWidget(QLabel("Correlation Risk:"), 2, 0)
        self.correlation_label = QLabel("0.00")
        layout.addWidget(self.correlation_label, 2, 1)
        
        self.correlation_bar = QProgressBar()
        self.correlation_bar.setRange(0, 100)
        self.correlation_bar.setValue(0)
        layout.addWidget(self.correlation_bar, 2, 2)
        
        # Risk Score
        layout.addWidget(QLabel("Overall Risk Score:"), 3, 0)
        self.risk_score_label = QLabel("Low")
        self.risk_score_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(self.risk_score_label, 3, 1)
        
        self.risk_score_bar = QProgressBar()
        self.risk_score_bar.setRange(0, 100)
        self.risk_score_bar.setValue(0)
        layout.addWidget(self.risk_score_bar, 3, 2)
        
        return group
    
    def create_circuit_breaker_section(self) -> QGroupBox:
        """Create circuit breaker status section"""
        group = QGroupBox("Circuit Breaker Status")
        layout = QGridLayout(group)
        
        # Circuit breaker indicators
        self.cb_indicators = {}
        
        cb_types = [
            ("Daily Loss", "daily_loss_halt"),
            ("Margin", "margin_halt"),
            ("Volatility", "volatility_halt"),
            ("Consecutive Loss", "consecutive_loss_halt"),
            ("Emergency Stop", "emergency_stop")
        ]
        
        for i, (name, key) in enumerate(cb_types):
            row = i // 3
            col = (i % 3) * 2
            
            layout.addWidget(QLabel(f"{name}:"), row, col)
            
            indicator = QLabel("🟢 Normal")
            indicator.setFont(QFont("Arial", 10, QFont.Bold))
            layout.addWidget(indicator, row, col + 1)
            
            self.cb_indicators[key] = indicator
        
        # Manual controls
        controls_layout = QHBoxLayout()
        
        self.test_cb_button = QPushButton("Test Circuit Breakers")
        self.test_cb_button.clicked.connect(self.test_circuit_breakers)
        controls_layout.addWidget(self.test_cb_button)
        
        self.reset_cb_button = QPushButton("Reset All")
        self.reset_cb_button.clicked.connect(self.reset_circuit_breakers)
        controls_layout.addWidget(self.reset_cb_button)
        
        controls_layout.addStretch()
        
        layout.addLayout(controls_layout, (len(cb_types) // 3) + 1, 0, 1, 6)
        
        return group
    
    def create_alerts_table(self) -> QTableWidget:
        """Create risk alerts table"""
        table = QTableWidget()
        table.setMaximumHeight(150)
        
        # Set columns
        columns = ["Time", "Level", "Type", "Message", "Value"]
        table.setColumnCount(len(columns))
        table.setHorizontalHeaderLabels(columns)
        
        # Configure table
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # Initialize alerts table with startup message only
        self.initialize_alerts_table(table)
        
        return table
    
    def setup_styling(self):
        """Setup widget styling"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QProgressBar {
                border: 2px solid #cccccc;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f5f5f5;
            }
            QTableWidget::item {
                padding: 4px;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 6px;
                border: none;
                border-bottom: 2px solid #2196F3;
                font-weight: bold;
            }
        """)
    
    def load_real_data(self):
        """Load real risk data from actual positions and trades"""
        # Initialize with zeros - real data only
        self.risk_metrics = {
            'daily_pnl': 0.0,
            'daily_pnl_pct': 0.0,
            'unrealized_pnl': 0.0,
            'margin_usage': 0.0,
            'margin_ratio': 0.0,
            'max_drawdown': 0.0,
            'consecutive_losses': 0,
            'volatility': 0.0,
            'correlation_risk': 0.0
        }

        self.update_displays()
        self.logger.info("Risk dashboard initialized with real data (zeros until positions exist)")
    
    def update_risk_metrics(self, positions_data=None, account_balance=0.0):
        """Update risk metrics with real position and account data"""
        try:
            # Calculate real P&L from positions
            total_unrealized_pnl = 0.0
            total_margin_used = 0.0

            if positions_data:
                for position in positions_data.values():
                    total_unrealized_pnl += position.get('unrealized_pnl', 0.0)
                    total_margin_used += position.get('margin_used', 0.0)

            # Update metrics with real data
            self.risk_metrics['unrealized_pnl'] = total_unrealized_pnl

            # Calculate margin usage percentage
            if account_balance > 0:
                self.risk_metrics['margin_usage'] = (total_margin_used / account_balance) * 100
                self.risk_metrics['margin_ratio'] = total_margin_used / account_balance
            else:
                self.risk_metrics['margin_usage'] = 0.0
                self.risk_metrics['margin_ratio'] = 0.0

            # Daily P&L calculation would need trade history - for now keep at 0
            # This should be calculated from completed trades today

            # Check for limit breaches
            self.check_risk_limits()

            # Update displays
            self.update_displays()

        except Exception as e:
            self.logger.error(f"Error updating risk metrics: {e}")
            # Keep current values on error

    def refresh_displays(self):
        """Refresh displays without changing data (for timer updates)"""
        self.update_displays()
    
    def update_displays(self):
        """Update all display elements"""
        metrics = self.risk_metrics
        
        # Daily P&L
        daily_pnl = metrics['daily_pnl']
        self.daily_pnl_label.setText(f"${daily_pnl:.2f}")
        if daily_pnl >= 0:
            self.daily_pnl_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.daily_pnl_label.setStyleSheet("color: red; font-weight: bold;")
        
        self.daily_pnl_bar.setValue(int(daily_pnl))
        self.update_progress_bar_color(self.daily_pnl_bar, daily_pnl, -500, 500)
        
        # Daily P&L %
        daily_pnl_pct = metrics['daily_pnl_pct']
        self.daily_pnl_pct_label.setText(f"{daily_pnl_pct:.2f}%")
        self.daily_pnl_pct_bar.setValue(int(daily_pnl_pct))
        self.update_progress_bar_color(self.daily_pnl_pct_bar, daily_pnl_pct, -5, 5)
        
        # Unrealized P&L
        unrealized_pnl = metrics['unrealized_pnl']
        self.unrealized_pnl_label.setText(f"${unrealized_pnl:.2f}")
        if unrealized_pnl >= 0:
            self.unrealized_pnl_label.setStyleSheet("color: green;")
        else:
            self.unrealized_pnl_label.setStyleSheet("color: red;")
        
        # Margin Usage
        margin_usage = metrics['margin_usage']
        self.margin_usage_label.setText(f"{margin_usage:.1f}%")
        self.margin_usage_bar.setValue(int(margin_usage))
        self.update_progress_bar_color(self.margin_usage_bar, margin_usage, 0, 80)
        
        # Max Drawdown
        max_drawdown = metrics['max_drawdown']
        self.max_drawdown_label.setText(f"{max_drawdown:.2f}%")
        self.max_drawdown_bar.setValue(int(max_drawdown))
        self.update_progress_bar_color(self.max_drawdown_bar, max_drawdown, 0, 10)
        
        # Consecutive Losses
        consecutive_losses = metrics['consecutive_losses']
        self.consecutive_losses_label.setText(f"{consecutive_losses} / 5")
        self.consecutive_losses_bar.setValue(consecutive_losses)
        self.update_progress_bar_color(self.consecutive_losses_bar, consecutive_losses, 0, 5)
        
        # Volatility
        volatility = metrics['volatility'] * 100
        self.volatility_label.setText(f"{volatility:.2f}%")
        self.volatility_bar.setValue(int(volatility))
        self.update_progress_bar_color(self.volatility_bar, volatility, 0, 10)
        
        # Correlation Risk
        correlation = metrics['correlation_risk'] * 100
        self.correlation_label.setText(f"{correlation:.1f}%")
        self.correlation_bar.setValue(int(correlation))
        self.update_progress_bar_color(self.correlation_bar, correlation, 0, 70)
        
        # Overall Risk Score
        risk_score = self.calculate_risk_score()
        self.risk_score_bar.setValue(risk_score)
        
        if risk_score < 30:
            self.risk_score_label.setText("Low")
            self.risk_score_label.setStyleSheet("color: green; font-weight: bold;")
            self.risk_status_label.setText("🟢 Risk Status: Normal")
        elif risk_score < 70:
            self.risk_score_label.setText("Medium")
            self.risk_score_label.setStyleSheet("color: orange; font-weight: bold;")
            self.risk_status_label.setText("🟡 Risk Status: Elevated")
        else:
            self.risk_score_label.setText("High")
            self.risk_score_label.setStyleSheet("color: red; font-weight: bold;")
            self.risk_status_label.setText("🔴 Risk Status: High")
        
        # Update circuit breaker indicators
        self.update_circuit_breaker_indicators()
    
    def update_progress_bar_color(self, bar: QProgressBar, value: float, min_val: float, max_val: float):
        """Update progress bar color based on value"""
        if max_val != min_val:
            ratio = abs(value - min_val) / abs(max_val - min_val)
        else:
            ratio = 0
        
        if ratio < 0.5:
            color = "#4CAF50"  # Green
        elif ratio < 0.8:
            color = "#FF9800"  # Orange
        else:
            color = "#f44336"  # Red
        
        bar.setStyleSheet(f"""
            QProgressBar::chunk {{
                background-color: {color};
                border-radius: 3px;
            }}
        """)
    
    def calculate_risk_score(self) -> int:
        """Calculate overall risk score (0-100)"""
        metrics = self.risk_metrics
        limits = self.risk_limits
        
        score = 0
        
        # Daily P&L risk (0-20 points)
        daily_loss_ratio = abs(min(0, metrics['daily_pnl'])) / limits['max_daily_loss_usd']
        score += min(20, daily_loss_ratio * 20)
        
        # Margin risk (0-20 points)
        margin_ratio = metrics['margin_usage'] / 100
        score += min(20, margin_ratio * 25)
        
        # Drawdown risk (0-20 points)
        drawdown_ratio = metrics['max_drawdown'] / limits['max_drawdown_pct']
        score += min(20, drawdown_ratio * 20)
        
        # Consecutive losses risk (0-20 points)
        loss_ratio = metrics['consecutive_losses'] / limits['max_consecutive_losses']
        score += min(20, loss_ratio * 20)
        
        # Volatility risk (0-20 points)
        vol_ratio = metrics['volatility'] / limits['max_volatility']
        score += min(20, vol_ratio * 20)
        
        return int(score)
    
    def check_risk_limits(self):
        """Check if any risk limits are breached"""
        metrics = self.risk_metrics
        limits = self.risk_limits
        
        # Check daily loss limit
        if abs(min(0, metrics['daily_pnl'])) >= limits['max_daily_loss_usd']:
            self.circuit_breaker_status['daily_loss_halt'] = True
            self.risk_limit_breached.emit('daily_loss', metrics['daily_pnl'])
        
        # Check margin limit
        if metrics['margin_usage'] / 100 >= limits['max_margin_ratio']:
            self.circuit_breaker_status['margin_halt'] = True
            self.risk_limit_breached.emit('margin', metrics['margin_usage'])
        
        # Check volatility limit
        if metrics['volatility'] >= limits['max_volatility']:
            self.circuit_breaker_status['volatility_halt'] = True
            self.risk_limit_breached.emit('volatility', metrics['volatility'])
        
        # Check consecutive losses
        if metrics['consecutive_losses'] >= limits['max_consecutive_losses']:
            self.circuit_breaker_status['consecutive_loss_halt'] = True
            self.risk_limit_breached.emit('consecutive_losses', metrics['consecutive_losses'])
    
    def update_circuit_breaker_indicators(self):
        """Update circuit breaker status indicators"""
        for key, indicator in self.cb_indicators.items():
            if self.circuit_breaker_status[key]:
                indicator.setText("🔴 ACTIVE")
                indicator.setStyleSheet("color: red; font-weight: bold; background: #ffebee; padding: 2px;")
            else:
                indicator.setText("🟢 Normal")
                indicator.setStyleSheet("color: green; font-weight: bold;")
    
    def test_circuit_breakers(self):
        """Test circuit breaker functionality"""
        reply = QMessageBox.question(
            self,
            'Test Circuit Breakers',
            'This will temporarily activate all circuit breakers for testing.\n\nProceed?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Activate all circuit breakers
            for key in self.circuit_breaker_status:
                self.circuit_breaker_status[key] = True
            
            self.update_circuit_breaker_indicators()
            
            # Add test alert
            self.add_alert("WARNING", "Test", "Circuit breaker test activated", "N/A")
            
            # Auto-reset after 5 seconds
            QTimer.singleShot(5000, self.reset_circuit_breakers)
    
    def reset_circuit_breakers(self):
        """Reset all circuit breakers"""
        for key in self.circuit_breaker_status:
            self.circuit_breaker_status[key] = False
        
        self.update_circuit_breaker_indicators()
        self.add_alert("INFO", "System", "Circuit breakers reset", "N/A")
    
    def initialize_alerts_table(self, table: QTableWidget):
        """Initialize alerts table with system startup message only"""
        startup_time = datetime.now().strftime("%H:%M:%S")
        startup_alerts = [
            (startup_time, "INFO", "System", "Risk monitoring started", "N/A")
        ]

        table.setRowCount(len(startup_alerts))

        for row, (time, level, type_, message, value) in enumerate(startup_alerts):
            table.setItem(row, 0, QTableWidgetItem(time))

            level_item = QTableWidgetItem(level)
            if level == "WARNING":
                level_item.setBackground(QColor(255, 235, 59))
            elif level == "ERROR":
                level_item.setBackground(QColor(244, 67, 54))
            table.setItem(row, 1, level_item)

            table.setItem(row, 2, QTableWidgetItem(type_))
            table.setItem(row, 3, QTableWidgetItem(message))
            table.setItem(row, 4, QTableWidgetItem(value))
    
    def add_alert(self, level: str, type_: str, message: str, value: str):
        """Add a new alert to the table"""
        table = self.alerts_table
        
        # Insert new row at top
        table.insertRow(0)
        
        # Add data
        current_time = datetime.now().strftime("%H:%M:%S")
        table.setItem(0, 0, QTableWidgetItem(current_time))
        
        level_item = QTableWidgetItem(level)
        if level == "WARNING":
            level_item.setBackground(QColor(255, 235, 59))
        elif level == "ERROR":
            level_item.setBackground(QColor(244, 67, 54))
        table.setItem(0, 1, level_item)
        
        table.setItem(0, 2, QTableWidgetItem(type_))
        table.setItem(0, 3, QTableWidgetItem(message))
        table.setItem(0, 4, QTableWidgetItem(value))
        
        # Keep only last 20 alerts
        if table.rowCount() > 20:
            table.removeRow(20)
        
        self.logger.info(f"Risk alert: [{level}] {type_}: {message}")
    
    def update_risk_metric(self, metric: str, value: float):
        """Update a specific risk metric"""
        if metric in self.risk_metrics:
            self.risk_metrics[metric] = value
            self.update_displays()
    
    def get_risk_status(self) -> Dict[str, Any]:
        """Get current risk status"""
        return {
            'metrics': self.risk_metrics.copy(),
            'limits': self.risk_limits.copy(),
            'circuit_breakers': self.circuit_breaker_status.copy(),
            'risk_score': self.calculate_risk_score(),
            'timestamp': datetime.now().isoformat()
        }
