"""
Rule Engine for YAML-based Strategy Definitions

This module provides a flexible rule engine that can parse and evaluate
YAML-based trading strategies with complex conditions and actions.
"""

import yaml
import numpy as np
from typing import Dict, List, Optional, Any
from utils.logger import get_logger
from utils.enums import ActionType, PositionSide


class RuleEngine:
    """
    Advanced rule engine for evaluating YAML-based trading strategies
    
    Supports:
    - Complex condition evaluation (AND/OR logic)
    - Technical indicator conditions
    - Price action conditions
    - Time-based conditions
    - Risk management rules
    """
    
    def __init__(self, strategy_config: Dict):
        """
        Initialize rule engine with strategy configuration
        
        Parameters:
            strategy_config: YAML-loaded strategy configuration
        """
        self.strategy = strategy_config
        self.logger = get_logger()
        
        # Validate strategy configuration
        self._validate_strategy()
    
    def _validate_strategy(self):
        """Validate strategy configuration structure"""
        required_sections = ['name', 'description', 'rules']
        for section in required_sections:
            if section not in self.strategy:
                raise ValueError(f"Missing required section '{section}' in strategy")
        
        rules = self.strategy['rules']
        required_rule_types = ['long_entry', 'short_entry', 'exit']
        for rule_type in required_rule_types:
            if rule_type not in rules:
                self.logger.warning(f"Missing rule type '{rule_type}' in strategy")
    
    def evaluate_conditions(self, market_data: Dict, indicators: Dict, position: Dict) -> Dict:
        """
        Evaluate strategy conditions and return trading decision
        
        Parameters:
            market_data: Current market data
            indicators: Technical indicators by timeframe
            position: Current position information
            
        Returns:
            Dict with action, confidence, and reasoning
        """
        try:
            rules = self.strategy.get('rules', {})
            
            # Evaluate different condition groups
            long_result = self._evaluate_condition_group(
                rules.get('long_entry', []), market_data, indicators, position
            )
            short_result = self._evaluate_condition_group(
                rules.get('short_entry', []), market_data, indicators, position
            )
            exit_result = self._evaluate_condition_group(
                rules.get('exit', []), market_data, indicators, position
            )
            
            # Determine action based on conditions and current position
            current_side = position.get('side', PositionSide.NONE.value)
            
            # Exit conditions take priority
            if exit_result['met'] and current_side != PositionSide.NONE.value:
                return {
                    'action': ActionType.CLOSE.value,
                    'confidence': exit_result['confidence'],
                    'reason': f"Exit: {'; '.join(exit_result['reasons'])}"
                }
            
            # Entry conditions
            if long_result['met'] and current_side != PositionSide.LONG.value:
                return {
                    'action': ActionType.LONG.value,
                    'confidence': long_result['confidence'],
                    'reason': f"Long entry: {'; '.join(long_result['reasons'])}"
                }
            elif short_result['met'] and current_side != PositionSide.SHORT.value:
                return {
                    'action': ActionType.SHORT.value,
                    'confidence': short_result['confidence'],
                    'reason': f"Short entry: {'; '.join(short_result['reasons'])}"
                }
            
            # No conditions met
            return {
                'action': ActionType.HOLD.value,
                'confidence': 0.5,
                'reason': "No conditions met"
            }
            
        except Exception as e:
            self.logger.error(f"Error evaluating strategy conditions: {str(e)}")
            return {
                'action': ActionType.HOLD.value,
                'confidence': 0.0,
                'reason': f"Evaluation error: {str(e)}"
            }
    
    def _evaluate_condition_group(self, conditions: List[Dict], market_data: Dict, 
                                 indicators: Dict, position: Dict) -> Dict:
        """
        Evaluate a group of conditions with specified logic
        
        Parameters:
            conditions: List of condition dictionaries
            market_data: Current market data
            indicators: Technical indicators
            position: Current position
            
        Returns:
            Dict with met status, confidence, and reasons
        """
        if not conditions:
            return {'met': False, 'confidence': 0.0, 'reasons': []}
        
        # Check if this is a logical group (AND/OR)
        if len(conditions) == 1 and 'logic' in conditions[0]:
            return self._evaluate_logical_group(conditions[0], market_data, indicators, position)
        
        # Default to AND logic for multiple conditions
        met_conditions = []
        confidence_scores = []
        reasons = []
        
        for condition in conditions:
            result = self._evaluate_single_condition(condition, market_data, indicators, position)
            met_conditions.append(result['met'])
            if result['met']:
                confidence_scores.append(result['confidence'])
                reasons.append(result['reason'])
        
        # All conditions must be met (AND logic)
        all_met = all(met_conditions)
        avg_confidence = np.mean(confidence_scores) if confidence_scores else 0.0
        
        return {
            'met': all_met,
            'confidence': avg_confidence,
            'reasons': reasons
        }
    
    def _evaluate_logical_group(self, logical_group: Dict, market_data: Dict,
                               indicators: Dict, position: Dict) -> Dict:
        """Evaluate a logical group with AND/OR operations"""
        logic_type = logical_group.get('logic', 'AND').upper()
        sub_conditions = logical_group.get('conditions', [])
        
        results = []
        for condition in sub_conditions:
            if isinstance(condition, dict) and 'logic' in condition:
                # Nested logical group
                result = self._evaluate_logical_group(condition, market_data, indicators, position)
            else:
                # Single condition
                result = self._evaluate_single_condition(condition, market_data, indicators, position)
            results.append(result)
        
        if logic_type == 'AND':
            met = all(r['met'] for r in results)
            confidence = np.mean([r['confidence'] for r in results if r['met']]) if met else 0.0
        elif logic_type == 'OR':
            met = any(r['met'] for r in results)
            confidence = np.max([r['confidence'] for r in results if r['met']]) if met else 0.0
        else:
            raise ValueError(f"Unknown logic type: {logic_type}")
        
        reasons = [r['reason'] for r in results if r['met']]
        
        return {
            'met': met,
            'confidence': confidence,
            'reasons': reasons
        }
    
    def _evaluate_single_condition(self, condition: Dict, market_data: Dict,
                                  indicators: Dict, position: Dict) -> Dict:
        """
        Evaluate a single trading condition
        
        Supported condition types:
        - price_above/price_below: Price comparisons
        - indicator_above/indicator_below: Technical indicator comparisons
        - indicator_cross_above/indicator_cross_below: Indicator crossovers
        - volume_surge: Volume spike detection
        - time_range: Time-based conditions
        - position_pnl: Position PnL conditions
        """
        try:
            condition_type = condition.get('type')
            
            if condition_type in ['price_above', 'price_below']:
                return self._evaluate_price_condition(condition, market_data)
            elif condition_type in ['indicator_above', 'indicator_below']:
                return self._evaluate_indicator_condition(condition, indicators)
            elif condition_type in ['indicator_cross_above', 'indicator_cross_below']:
                return self._evaluate_crossover_condition(condition, indicators)
            elif condition_type == 'volume_surge':
                return self._evaluate_volume_condition(condition, market_data)
            elif condition_type == 'time_range':
                return self._evaluate_time_condition(condition, market_data)
            elif condition_type in ['position_pnl_above', 'position_pnl_below']:
                return self._evaluate_position_condition(condition, position)
            else:
                self.logger.warning(f"Unknown condition type: {condition_type}")
                return {'met': False, 'confidence': 0.0, 'reason': f"Unknown condition: {condition_type}"}
                
        except Exception as e:
            self.logger.error(f"Error evaluating condition {condition}: {str(e)}")
            return {'met': False, 'confidence': 0.0, 'reason': f"Condition error: {str(e)}"}
    
    def _evaluate_price_condition(self, condition: Dict, market_data: Dict) -> Dict:
        """Evaluate price-based conditions"""
        price = market_data.get('price', 0)
        threshold = condition.get('value', 0)
        condition_type = condition.get('type')
        
        if condition_type == 'price_above':
            met = price > threshold
            confidence = min(1.0, (price - threshold) / threshold * 10) if met else 0.0
        else:  # price_below
            met = price < threshold
            confidence = min(1.0, (threshold - price) / threshold * 10) if met else 0.0
        
        return {
            'met': met,
            'confidence': confidence,
            'reason': f"Price {price:.4f} vs {threshold:.4f}"
        }
    
    def _evaluate_indicator_condition(self, condition: Dict, indicators: Dict) -> Dict:
        """Evaluate technical indicator conditions"""
        indicator_name = condition.get('indicator')
        timeframe = condition.get('timeframe', '1m')
        threshold = condition.get('value', 0)
        condition_type = condition.get('type')
        
        value = indicators.get(timeframe, {}).get(indicator_name, 0)
        
        if condition_type == 'indicator_above':
            met = value > threshold
            confidence = min(1.0, abs(value - threshold) / max(abs(threshold), 1)) if met else 0.0
        else:  # indicator_below
            met = value < threshold
            confidence = min(1.0, abs(threshold - value) / max(abs(threshold), 1)) if met else 0.0
        
        return {
            'met': met,
            'confidence': confidence,
            'reason': f"{indicator_name}({timeframe}): {value:.4f} vs {threshold:.4f}"
        }
    
    def _evaluate_crossover_condition(self, condition: Dict, indicators: Dict) -> Dict:
        """Evaluate indicator crossover conditions"""
        # This would require historical indicator values
        # For now, return a placeholder
        return {
            'met': False,
            'confidence': 0.0,
            'reason': "Crossover conditions not implemented in backtest mode"
        }
    
    def _evaluate_volume_condition(self, condition: Dict, market_data: Dict) -> Dict:
        """Evaluate volume-based conditions"""
        volume_surge = market_data.get('volume_surge', 100)
        threshold = condition.get('value', 150)
        
        met = volume_surge > threshold
        confidence = min(1.0, (volume_surge - threshold) / threshold) if met else 0.0
        
        return {
            'met': met,
            'confidence': confidence,
            'reason': f"Volume surge: {volume_surge:.1f}% vs {threshold:.1f}%"
        }
    
    def _evaluate_time_condition(self, condition: Dict, market_data: Dict) -> Dict:
        """Evaluate time-based conditions"""
        from datetime import datetime
        
        current_hour = datetime.fromtimestamp(market_data.get('timestamp', 0)).hour
        start_hour = condition.get('start_hour', 0)
        end_hour = condition.get('end_hour', 23)
        
        if start_hour <= end_hour:
            met = start_hour <= current_hour <= end_hour
        else:  # Crosses midnight
            met = current_hour >= start_hour or current_hour <= end_hour
        
        return {
            'met': met,
            'confidence': 1.0 if met else 0.0,
            'reason': f"Time {current_hour}:00 in range {start_hour}:00-{end_hour}:00"
        }
    
    def _evaluate_position_condition(self, condition: Dict, position: Dict) -> Dict:
        """Evaluate position-based conditions"""
        pnl_pct = position.get('unrealized_pnl_pct', 0)
        threshold = condition.get('value', 0)
        condition_type = condition.get('type')
        
        if condition_type == 'position_pnl_above':
            met = pnl_pct > threshold
        else:  # position_pnl_below
            met = pnl_pct < threshold
        
        confidence = 1.0 if met else 0.0
        
        return {
            'met': met,
            'confidence': confidence,
            'reason': f"Position PnL: {pnl_pct:.2f}% vs {threshold:.2f}%"
        }
