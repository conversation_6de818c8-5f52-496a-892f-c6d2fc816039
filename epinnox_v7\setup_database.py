#!/usr/bin/env python3
"""
Database Setup and Integration Script for Epinnox v7

This script:
1. Installs required database dependencies
2. Initializes the database schema
3. Tests database connectivity
4. Provides sample data insertion
5. Demonstrates database operations

Run with: python setup_database.py
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timezone, timedelta
import json

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from utils.config_validator import ConfigValidator
from utils.logger import get_logger

logger = get_logger()


def install_dependencies():
    """Install required database dependencies"""
    print("📦 Installing database dependencies...")
    
    try:
        import subprocess
        
        # Install SQLAlchemy and related packages
        packages = [
            'sqlalchemy>=2.0.0',
            'alembic>=1.12.0',  # For database migrations
            'psycopg2-binary>=2.9.0'  # PostgreSQL adapter
        ]
        
        for package in packages:
            print(f"  Installing {package}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"    ✅ {package} installed successfully")
            else:
                print(f"    ⚠️  {package} installation warning: {result.stderr}")
        
        print("✅ Database dependencies installation completed")
        return True
        
    except Exception as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def test_database_setup():
    """Test database setup and operations"""
    print("\n🧪 Testing Database Setup...")
    
    try:
        # Load configuration
        config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
        
        # Import database components
        from database.database_manager import DatabaseManager
        from database.repositories import (
            TradeRepository, BacktestRepository, 
            OptimizationRepository, LLMRepository
        )
        
        # Initialize database
        print("  ├─ Initializing database...")
        db_manager = DatabaseManager(config)
        
        # Test health check
        print("  ├─ Testing database health...")
        health = db_manager.health_check()
        if health['status'] == 'healthy':
            print("    ✅ Database is healthy")
        else:
            print(f"    ❌ Database health check failed: {health.get('error', 'Unknown error')}")
            return False
        
        # Test basic operations
        print("  ├─ Testing basic operations...")
        with db_manager.get_session() as session:
            # Test repositories
            trade_repo = TradeRepository(session)
            backtest_repo = BacktestRepository(session)
            llm_repo = LLMRepository(session)
            
            print("    ✅ Repository initialization successful")
        
        # Get database stats
        print("  ├─ Getting database statistics...")
        stats = db_manager.get_database_stats()
        print(f"    📊 Tables: {stats.get('table_counts', {})}")
        
        print("✅ Database setup test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database setup test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_sample_data():
    """Create sample data for testing"""
    print("\n📝 Creating Sample Data...")
    
    try:
        config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
        
        from database.database_manager import DatabaseManager
        from database.repositories import (
            TradeRepository, BacktestRepository, LLMRepository
        )
        
        db_manager = DatabaseManager(config)
        
        with db_manager.get_session() as session:
            trade_repo = TradeRepository(session)
            backtest_repo = BacktestRepository(session)
            llm_repo = LLMRepository(session)
            
            # Create sample trade
            print("  ├─ Creating sample trade...")
            sample_trade = trade_repo.create_trade({
                'trade_id': f'SAMPLE_TRADE_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
                'strategy_name': 'Simple Momentum Strategy',
                'symbol': 'DOGE/USDT:USDT',
                'side': 'LONG',
                'entry_price': 0.08,
                'exit_price': 0.081,
                'quantity': 1000,
                'leverage': 20,
                'entry_time': datetime.now(timezone.utc),
                'exit_time': datetime.now(timezone.utc) + timedelta(minutes=5),
                'duration_seconds': 300,
                'pnl_usd': 12.5,
                'pnl_pct': 1.25,
                'fees_usd': 0.5,
                'stop_loss': 0.079,
                'take_profit': 0.082,
                'market_regime': 'trending',
                'llm_confidence': 0.75,
                'llm_reasoning': 'Strong upward momentum with high volume confirmation'
            })
            print(f"    ✅ Sample trade created: {sample_trade.trade_id}")
            
            # Create sample backtest
            print("  ├─ Creating sample backtest...")
            sample_backtest = backtest_repo.create_backtest({
                'backtest_id': f'SAMPLE_BT_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
                'strategy_name': 'Simple Momentum Strategy',
                'symbol': 'DOGE/USDT:USDT',
                'initial_balance': 500.0,
                'leverage': 20,
                'timeframe': '1m',
                'start_date': datetime.now(timezone.utc) - timedelta(days=1),
                'end_date': datetime.now(timezone.utc),
                'strategy_config': {
                    'rsi_long_threshold': 30,
                    'rsi_short_threshold': 70,
                    'volume_surge_threshold': 150
                },
                'total_trades': 25,
                'win_rate': 68.0,
                'total_pnl': 45.75,
                'total_pnl_pct': 9.15,
                'max_drawdown_pct': 2.3,
                'sharpe_ratio': 1.85,
                'performance_grade': 'B+',
                'execution_time_seconds': 12.5,
                'data_points_processed': 1440
            })
            print(f"    ✅ Sample backtest created: {sample_backtest.backtest_id}")
            
            # Create sample LLM prediction
            print("  ├─ Creating sample LLM prediction...")
            sample_prediction = llm_repo.create_prediction({
                'prediction_id': f'SAMPLE_PRED_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
                'symbol': 'DOGE/USDT:USDT',
                'timestamp': datetime.now(timezone.utc),
                'current_price': 0.08,
                'market_data': {
                    'volume': 1000000,
                    'volatility': 0.015,
                    'spread': 0.0001
                },
                'predicted_action': 'LONG',
                'confidence': 0.75,
                'reasoning': 'Technical indicators show bullish momentum',
                'entry_price': 0.08,
                'stop_loss': 0.079,
                'take_profit': 0.082,
                'position_size': 50.0
            })
            print(f"    ✅ Sample prediction created: {sample_prediction.prediction_id}")
            
            session.commit()
        
        print("✅ Sample data creation completed")
        return True
        
    except Exception as e:
        print(f"❌ Sample data creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_repository_operations():
    """Test repository operations"""
    print("\n🔍 Testing Repository Operations...")
    
    try:
        config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
        
        from database.database_manager import DatabaseManager
        from database.repositories import (
            TradeRepository, BacktestRepository, LLMRepository
        )
        
        db_manager = DatabaseManager(config)
        
        with db_manager.get_session() as session:
            trade_repo = TradeRepository(session)
            backtest_repo = BacktestRepository(session)
            llm_repo = LLMRepository(session)
            
            # Test trade operations
            print("  ├─ Testing trade repository...")
            trades = trade_repo.get_trades_by_symbol('DOGE/USDT:USDT', limit=5)
            print(f"    📊 Found {len(trades)} trades for DOGE/USDT:USDT")
            
            performance = trade_repo.get_performance_summary(days=30)
            print(f"    📈 Performance: {performance['total_trades']} trades, "
                  f"{performance['win_rate']:.1f}% win rate, "
                  f"${performance['total_pnl']:.2f} PnL")
            
            # Test backtest operations
            print("  ├─ Testing backtest repository...")
            backtests = backtest_repo.get_recent_backtests(days=7)
            print(f"    📊 Found {len(backtests)} recent backtests")
            
            best_backtests = backtest_repo.get_best_backtests(limit=3)
            print(f"    🏆 Top {len(best_backtests)} performing backtests")
            
            # Test LLM operations
            print("  ├─ Testing LLM repository...")
            predictions = llm_repo.get_predictions_by_symbol('DOGE/USDT:USDT', limit=5)
            print(f"    🤖 Found {len(predictions)} LLM predictions")
            
            accuracy_stats = llm_repo.get_accuracy_stats(days=7)
            print(f"    🎯 Accuracy stats: {accuracy_stats}")
        
        print("✅ Repository operations test completed")
        return True
        
    except Exception as e:
        print(f"❌ Repository operations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def demonstrate_integration():
    """Demonstrate database integration with existing components"""
    print("\n🔗 Demonstrating Database Integration...")
    
    try:
        # Show how to integrate with backtester
        print("  ├─ Integration with Backtester...")
        print("    💡 Backtester can now save results to database automatically")
        print("    💡 Historical results can be loaded from database")
        print("    💡 Performance comparison across multiple backtests")
        
        # Show how to integrate with LLM simulation
        print("  ├─ Integration with LLM Simulation...")
        print("    💡 LLM predictions automatically saved to database")
        print("    💡 Accuracy tracking over time")
        print("    💡 Performance analysis of LLM decisions")
        
        # Show how to integrate with GUI
        print("  ├─ Integration with GUI Dashboard...")
        print("    💡 Load historical data for visualization")
        print("    💡 Real-time performance monitoring")
        print("    💡 Strategy comparison and analysis")
        
        print("✅ Database integration demonstration completed")
        return True
        
    except Exception as e:
        print(f"❌ Integration demonstration failed: {e}")
        return False


def main():
    """Main setup function"""
    print("🚀 Epinnox v7 Database Setup and Integration")
    print("=" * 50)
    
    # Step 1: Install dependencies
    if not install_dependencies():
        print("❌ Dependency installation failed. Exiting.")
        return False
    
    # Step 2: Test database setup
    if not test_database_setup():
        print("❌ Database setup test failed. Exiting.")
        return False
    
    # Step 3: Create sample data
    if not create_sample_data():
        print("❌ Sample data creation failed. Continuing anyway.")
    
    # Step 4: Test repository operations
    if not test_repository_operations():
        print("❌ Repository operations test failed. Continuing anyway.")
    
    # Step 5: Demonstrate integration
    demonstrate_integration()
    
    print("\n" + "=" * 50)
    print("🎉 DATABASE SETUP COMPLETED SUCCESSFULLY!")
    print("\n📋 Next Steps:")
    print("  1. ✅ Database is ready for use")
    print("  2. 🔗 Integrate with existing components")
    print("  3. 📊 Start using persistent data storage")
    print("  4. 🖥️  Update GUI to use database")
    print("  5. 📈 Enable advanced analytics")
    
    print("\n💡 Usage Examples:")
    print("  • python main.py --gui  # GUI with database integration")
    print("  • python test_database_integration.py  # Test database features")
    print("  • Check logs/database/ for database logs")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
