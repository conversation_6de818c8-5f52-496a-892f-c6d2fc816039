import asyncio
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List
import json
from utils.logger import get_logger

class SystemHealth(Enum):
    OK = "ok"
    RISKY = "risky"
    PAUSED = "paused"

class HeartbeatMonitor:
    """Monitors and logs system health metrics"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # Setup log file
        log_dir = Path(config['logging']['log_dir'])
        log_dir.mkdir(parents=True, exist_ok=True)
        self.heartbeat_file = log_dir / "heartbeat.log"
        
        # Metrics state
        self.active_symbols: List[str] = []
        self.total_pnl = 0.0
        self.win_rate = 0.0
        self.drawdown = 0.0
        self.system_health = SystemHealth.OK
        
        # Thread safety
        self._lock = asyncio.Lock()
        self._running = False
        
    async def start(self):
        """Start the heartbeat monitor"""
        self._running = True
        await self._heartbeat_loop()
        
    async def stop(self):
        """Stop the heartbeat monitor"""
        self._running = False
        
    def update_metrics(self, 
                      active_symbols: List[str],
                      total_pnl: float,
                      win_rate: float,
                      drawdown: float):
        """Update current system metrics"""
        self.active_symbols = active_symbols
        self.total_pnl = total_pnl
        self.win_rate = win_rate
        self.drawdown = drawdown
        
        # Update system health
        self._assess_system_health()
        
    async def _heartbeat_loop(self):
        """Main heartbeat logging loop"""
        interval = self.config['logging']['heartbeat_interval']
        
        while self._running:
            try:
                await self._log_heartbeat()
                await asyncio.sleep(interval)
            except Exception as e:
                self.logger.error(f"Error in heartbeat loop: {str(e)}")
                await asyncio.sleep(5)
                
    async def _log_heartbeat(self):
        """Log current system state"""
        try:
            async with self._lock:
                heartbeat = {
                    'timestamp': datetime.now().isoformat(),
                    'active_symbols': len(self.active_symbols),
                    'symbols': self.active_symbols,
                    'total_pnl': round(self.total_pnl, 2),
                    'win_rate': round(self.win_rate * 100, 1),
                    'drawdown': round(self.drawdown * 100, 1),
                    'health': self.system_health.value
                }
                
                # Write to log file
                with open(self.heartbeat_file, 'a') as f:
                    f.write(json.dumps(heartbeat) + '\n')
                    
        except Exception as e:
            self.logger.error(f"Failed to log heartbeat: {str(e)}")
            
    def _assess_system_health(self):
        """Assess overall system health based on metrics"""
        if not self.active_symbols:
            self.system_health = SystemHealth.PAUSED
            return
            
        # Check risk thresholds
        risk_limits = self.config['risk_limits']
        
        if (
            self.drawdown >= risk_limits['max_drawdown_pct'] or
            self.win_rate < risk_limits['min_win_rate']
        ):
            self.system_health = SystemHealth.RISKY
        else:
            self.system_health = SystemHealth.OK
