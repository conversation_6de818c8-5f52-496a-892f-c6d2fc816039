"""
Database Manager for Epinnox v7

Handles database connections, migrations, and provides high-level database operations.
Supports both SQLite (development) and PostgreSQL (production).
"""

import os
import json
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime, timezone, timedelta
from contextlib import contextmanager

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from sqlalchemy.exc import SQLAlchemyError

from .models import Base, TradeRecord, StrategyPerformance, BacktestResult
from utils.logger import get_logger

logger = get_logger()


class DatabaseManager:
    """
    Central database manager for Epinnox v7
    
    Provides:
    - Database connection management
    - Schema creation and migration
    - Session management
    - High-level database operations
    - Backup and restore functionality
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize database manager
        
        Args:
            config: Database configuration dictionary
        """
        self.config = config
        self.engine = None
        self.SessionLocal = None
        self._setup_database()
    
    def _setup_database(self):
        """Setup database connection and session factory"""
        try:
            # Get database configuration
            db_config = self.config.get('database', {})
            db_type = db_config.get('type', 'sqlite')
            
            if db_type == 'sqlite':
                self._setup_sqlite(db_config)
            elif db_type == 'postgresql':
                self._setup_postgresql(db_config)
            else:
                raise ValueError(f"Unsupported database type: {db_type}")
            
            # Create session factory
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # Create tables if they don't exist
            self._create_tables()
            
            logger.info(f"Database initialized successfully ({db_type})")
            
        except Exception as e:
            logger.error(f"Database setup failed: {str(e)}")
            raise
    
    def _setup_sqlite(self, db_config: Dict[str, Any]):
        """Setup SQLite database"""
        db_path = db_config.get('path', 'data/epinnox.db')
        
        # Ensure directory exists
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Create engine with SQLite-specific settings
        self.engine = create_engine(
            f'sqlite:///{db_path}',
            poolclass=StaticPool,
            connect_args={
                'check_same_thread': False,
                'timeout': 30
            },
            echo=db_config.get('echo', False)
        )
        
        # Enable WAL mode for better concurrency
        with self.engine.connect() as conn:
            conn.execute(text('PRAGMA journal_mode=WAL'))
            conn.execute(text('PRAGMA synchronous=NORMAL'))
            conn.execute(text('PRAGMA cache_size=10000'))
            conn.execute(text('PRAGMA temp_store=MEMORY'))
            conn.commit()
    
    def _setup_postgresql(self, db_config: Dict[str, Any]):
        """Setup PostgreSQL database"""
        # Build connection string
        host = db_config.get('host', 'localhost')
        port = db_config.get('port', 5432)
        database = db_config.get('database', 'epinnox')
        username = db_config.get('username', 'epinnox')
        password = db_config.get('password', '')
        
        connection_string = f'postgresql://{username}:{password}@{host}:{port}/{database}'
        
        # Create engine with PostgreSQL-specific settings
        self.engine = create_engine(
            connection_string,
            pool_size=db_config.get('pool_size', 10),
            max_overflow=db_config.get('max_overflow', 20),
            pool_pre_ping=True,
            echo=db_config.get('echo', False)
        )
    
    def _create_tables(self):
        """Create all database tables"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created/verified successfully")
        except Exception as e:
            logger.error(f"Failed to create database tables: {str(e)}")
            raise
    
    @contextmanager
    def get_session(self):
        """
        Get database session with automatic cleanup
        
        Usage:
            with db_manager.get_session() as session:
                # Use session here
                pass
        """
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {str(e)}")
            raise
        finally:
            session.close()
    
    def health_check(self) -> Dict[str, Any]:
        """
        Perform database health check
        
        Returns:
            Dict with health status and metrics
        """
        try:
            with self.get_session() as session:
                # Test basic connectivity
                result = session.execute(text('SELECT 1')).scalar()
                
                # Get table counts
                trade_count = session.query(TradeRecord).count()
                backtest_count = session.query(BacktestResult).count()
                strategy_count = session.query(StrategyPerformance).count()
                
                return {
                    'status': 'healthy',
                    'connectivity': result == 1,
                    'table_counts': {
                        'trades': trade_count,
                        'backtests': backtest_count,
                        'strategies': strategy_count
                    },
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }
                
        except Exception as e:
            logger.error(f"Database health check failed: {str(e)}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
    
    def backup_database(self, backup_path: Optional[str] = None) -> str:
        """
        Create database backup
        
        Args:
            backup_path: Optional custom backup path
            
        Returns:
            Path to backup file
        """
        try:
            if backup_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = f'backups/epinnox_backup_{timestamp}.sql'
            
            # Ensure backup directory exists
            Path(backup_path).parent.mkdir(parents=True, exist_ok=True)
            
            # For SQLite, copy the file
            if 'sqlite' in str(self.engine.url):
                import shutil
                db_path = str(self.engine.url).replace('sqlite:///', '')
                shutil.copy2(db_path, backup_path.replace('.sql', '.db'))
                backup_path = backup_path.replace('.sql', '.db')
            else:
                # For PostgreSQL, use pg_dump (would need implementation)
                raise NotImplementedError("PostgreSQL backup not yet implemented")
            
            logger.info(f"Database backup created: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"Database backup failed: {str(e)}")
            raise
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive database statistics
        
        Returns:
            Dictionary with database statistics
        """
        try:
            with self.get_session() as session:
                stats = {}
                
                # Table counts
                stats['table_counts'] = {
                    'trades': session.query(TradeRecord).count(),
                    'backtests': session.query(BacktestResult).count(),
                    'strategies': session.query(StrategyPerformance).count()
                }
                
                # Recent activity (last 24 hours)
                yesterday = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
                
                stats['recent_activity'] = {
                    'trades_today': session.query(TradeRecord).filter(
                        TradeRecord.created_at >= yesterday
                    ).count(),
                    'backtests_today': session.query(BacktestResult).filter(
                        BacktestResult.created_at >= yesterday
                    ).count()
                }
                
                # Performance summary
                if stats['table_counts']['trades'] > 0:
                    total_pnl = session.query(
                        text('SUM(pnl_usd) as total_pnl')
                    ).select_from(TradeRecord).scalar() or 0
                    
                    avg_pnl = session.query(
                        text('AVG(pnl_usd) as avg_pnl')
                    ).select_from(TradeRecord).scalar() or 0
                    
                    stats['performance_summary'] = {
                        'total_pnl': float(total_pnl),
                        'average_pnl': float(avg_pnl)
                    }
                
                # Database size (for SQLite)
                if 'sqlite' in str(self.engine.url):
                    db_path = str(self.engine.url).replace('sqlite:///', '')
                    if Path(db_path).exists():
                        stats['database_size_mb'] = Path(db_path).stat().st_size / 1024 / 1024
                
                stats['timestamp'] = datetime.now(timezone.utc).isoformat()
                return stats
                
        except Exception as e:
            logger.error(f"Failed to get database stats: {str(e)}")
            return {'error': str(e)}
    
    def cleanup_old_data(self, days_to_keep: int = 90) -> Dict[str, int]:
        """
        Clean up old data beyond retention period
        
        Args:
            days_to_keep: Number of days of data to retain
            
        Returns:
            Dictionary with cleanup statistics
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
            
            with self.get_session() as session:
                # Count records to be deleted
                old_trades = session.query(TradeRecord).filter(
                    TradeRecord.created_at < cutoff_date
                ).count()
                
                old_backtests = session.query(BacktestResult).filter(
                    BacktestResult.created_at < cutoff_date
                ).count()
                
                # Delete old records
                session.query(TradeRecord).filter(
                    TradeRecord.created_at < cutoff_date
                ).delete()
                
                session.query(BacktestResult).filter(
                    BacktestResult.created_at < cutoff_date
                ).delete()
                
                session.commit()
                
                cleanup_stats = {
                    'trades_deleted': old_trades,
                    'backtests_deleted': old_backtests,
                    'cutoff_date': cutoff_date.isoformat()
                }
                
                logger.info(f"Database cleanup completed: {cleanup_stats}")
                return cleanup_stats
                
        except Exception as e:
            logger.error(f"Database cleanup failed: {str(e)}")
            raise
    
    def close(self):
        """Close database connections"""
        if self.engine:
            self.engine.dispose()
            logger.info("Database connections closed")


# Global database manager instance
_db_manager: Optional[DatabaseManager] = None


def get_database_manager(config: Optional[Dict[str, Any]] = None) -> DatabaseManager:
    """
    Get global database manager instance
    
    Args:
        config: Database configuration (required for first call)
        
    Returns:
        DatabaseManager instance
    """
    global _db_manager
    
    if _db_manager is None:
        if config is None:
            raise ValueError("Database configuration required for first initialization")
        _db_manager = DatabaseManager(config)
    
    return _db_manager


def close_database():
    """Close global database manager"""
    global _db_manager
    if _db_manager:
        _db_manager.close()
        _db_manager = None
