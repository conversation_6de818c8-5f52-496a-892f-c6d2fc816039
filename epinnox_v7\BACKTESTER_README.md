# Epinnox v7 Strategy Backtester

## Overview

The Epinnox v7 Strategy Backtester is a comprehensive backtesting system that allows you to test trading strategies on historical data before deploying them live. It supports both LLM-driven and rule-based strategies with detailed performance analysis.

## Features

- **Historical Data Fetching**: Automatic OHLCV data retrieval via CCXT
- **Dual Strategy Modes**: LLM-driven and YAML rule-based strategies
- **Comprehensive Metrics**: Win rate, Sharpe ratio, drawdown, profit factor, etc.
- **Risk Management**: Integrated with existing RiskGuard system
- **Signal Analysis**: Time-of-day performance and signal frequency analysis
- **Results Export**: JSON export with equity curves and trade logs
- **Strategy Comparison**: Side-by-side strategy performance comparison

## Quick Start

### 1. Basic Backtest

```python
from strategy_builder.backtester import Backtester

# Initialize backtester
backtester = Backtester()

# Fetch historical data
data = await backtester.fetch_historical_data(
    symbol='DOGE/USDT:USDT',
    timeframe='1m',
    days=7,
    exchange_id='binance'
)

# Load strategy
strategy = backtester.load_strategy('strategy_builder/strategies/simple_momentum.yaml')

# Run backtest
results = await backtester.run_backtest(
    symbol='DOGE/USDT:USDT',
    ohlcv_data=data,
    strategy_config=strategy,
    use_llm=False,
    initial_balance=500.0,
    leverage=20
)

# Analyze results
analysis = backtester.analyze_results(results)
print(f"Performance Grade: {analysis['performance_grade']}")
```

### 2. Running the Test Suite

```bash
cd epinnox_v7
python test_backtester.py
```

This will run comprehensive tests including:
- Data fetching validation
- Rule-based strategy testing
- Strategy comparison
- Edge case testing
- Full DOGE/USDT backtest with 20x leverage

## Strategy Configuration

### YAML Strategy Format

Strategies are defined in YAML files with the following structure:

```yaml
name: "Strategy Name"
description: "Strategy description"

# Risk management
risk_management:
  max_position_size_pct: 2.0
  stop_loss_pct: 0.5
  take_profit_pct: 1.0

# Trading rules
rules:
  long_entry:
    - type: "indicator_above"
      indicator: "rsi"
      timeframe: "1m"
      value: 55
    
    - type: "volume_surge"
      value: 120

  short_entry:
    - type: "indicator_below"
      indicator: "rsi"
      timeframe: "1m"
      value: 45

  exit:
    - type: "position_pnl_above"
      value: 0.8
    - type: "position_pnl_below"
      value: -0.4
```

### Supported Condition Types

- **Price Conditions**: `price_above`, `price_below`
- **Indicator Conditions**: `indicator_above`, `indicator_below`
- **Volume Conditions**: `volume_surge`
- **Position Conditions**: `position_pnl_above`, `position_pnl_below`
- **Time Conditions**: `time_range`
- **Logical Operators**: `AND`, `OR` logic groups

## API Reference

### Backtester Class

#### `__init__(config_path: str)`
Initialize backtester with configuration file.

#### `fetch_historical_data(symbol, timeframe, days, exchange_id)`
Fetch historical OHLCV data from exchange.

**Parameters:**
- `symbol`: Trading pair (e.g., 'DOGE/USDT:USDT')
- `timeframe`: Candle timeframe ('1m', '5m', '15m', '1h', '1d')
- `days`: Number of days of historical data
- `exchange_id`: Exchange name ('binance', 'huobi', etc.)

#### `run_backtest(symbol, ohlcv_data, strategy_config, use_llm, initial_balance, leverage)`
Run comprehensive backtest.

**Returns:** `BacktestResults` object with performance metrics.

#### `analyze_results(results)`
Analyze backtest results and provide insights.

**Returns:** Dictionary with performance grade, strengths, weaknesses, and recommendations.

### BacktestResults Class

Contains comprehensive backtest metrics:

```python
@dataclass
class BacktestResults:
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_pnl: float
    total_pnl_pct: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    max_drawdown: float
    max_drawdown_pct: float
    sharpe_ratio: float
    calmar_ratio: float
    avg_trade_duration: float
    trades_per_day: float
    signal_accuracy: float
    signal_frequency: Dict[str, int]
    time_of_day_performance: Dict[str, float]
    equity_curve: List[Tuple[float, float]]
    trades: List[BacktestTrade]
```

## Integration with Epinnox v7

The backtester is fully integrated with the existing Epinnox v7 system:

- **ScalperEngine**: Uses the same trading logic
- **RiskGuard**: Applies the same risk management rules
- **PositionTracker**: Tracks positions and performance
- **StrategyState**: Maintains market regime awareness
- **PerformanceFeedback**: Provides adaptive feedback

## Results Export

Backtest results are automatically exported to `logs/backtest_results/` in JSON format:

```json
{
  "metadata": {
    "symbol": "DOGE/USDT:USDT",
    "start_time": "2024-01-01T00:00:00",
    "end_time": "2024-01-07T23:59:00",
    "initial_balance": 500.0,
    "final_balance": 650.0
  },
  "performance_metrics": {
    "total_trades": 45,
    "win_rate": 62.22,
    "total_pnl": 150.0,
    "total_pnl_pct": 30.0,
    "sharpe_ratio": 1.25
  },
  "equity_curve": [...],
  "trades": [...]
}
```

## Performance Grading

The backtester provides automatic performance grading:

- **Grade A**: Win rate ≥60%, Profit factor ≥1.5, Sharpe ≥1.0
- **Grade B**: Win rate ≥50%, Profit factor ≥1.2, Sharpe ≥0.5
- **Grade C**: Win rate ≥40%, Profit factor ≥1.0
- **Grade D**: Positive PnL
- **Grade F**: Negative PnL

## Best Practices

1. **Start Small**: Begin with small position sizes and low leverage
2. **Test Multiple Timeframes**: Validate strategies across different timeframes
3. **Use Sufficient Data**: Test with at least 1000 candles for statistical significance
4. **Consider Transaction Costs**: Factor in spreads and fees
5. **Validate Forward**: Test on out-of-sample data
6. **Monitor Drawdown**: Keep maximum drawdown below 10%
7. **Diversify**: Test multiple strategies and symbols

## Troubleshooting

### Common Issues

1. **Data Fetching Errors**: Check internet connection and exchange API limits
2. **Strategy Loading Errors**: Validate YAML syntax and required fields
3. **Memory Issues**: Reduce data size or timeframe for large datasets
4. **Performance Issues**: Use smaller datasets for initial testing

### Debug Mode

Enable debug logging in the configuration:

```yaml
debug_mode: true
log_level: "DEBUG"
```

## Examples

See the `test_backtester.py` file for comprehensive examples of:
- Basic backtesting
- Strategy comparison
- Edge case handling
- Performance analysis

## Support

For issues or questions:
1. Check the logs in `logs/` directory
2. Review the configuration in `config/scalper_config.yaml`
3. Examine the strategy files in `strategy_builder/strategies/`
4. Run the test suite to validate installation
