# Scalping Breakout Strategy for Epinnox v7
# This strategy looks for breakouts from consolidation with volume confirmation
# Designed for 1-minute timeframe scalping

name: "Scalping Breakout Strategy"
description: "High-frequency breakout strategy with tight risk management"
version: "1.0"
author: "Epinnox v7"

# Risk management parameters
risk_management:
  max_position_size_pct: 1.5  # Maximum 1.5% of account per trade
  stop_loss_pct: 0.3          # Tight 0.3% stop loss
  take_profit_pct: 0.6        # 0.6% take profit (2:1 RR)
  max_drawdown_pct: 3.0       # Stop trading if drawdown exceeds 3%
  max_trades_per_hour: 10     # Limit to 10 trades per hour

# Trading rules with complex logic
rules:
  # Long entry conditions
  long_entry:
    - logic: "AND"
      conditions:
        # Breakout conditions
        - type: "price_above"
          indicator: "bb_upper"
          timeframe: "1m"
          description: "Price breaks above Bollinger Band upper"
        
        - type: "volume_surge"
          value: 150
          description: "Strong volume surge (50% above average)"
        
        # Momentum confirmation
        - logic: "OR"
          conditions:
            - type: "indicator_above"
              indicator: "rsi"
              timeframe: "1m"
              value: 60
              description: "RSI shows momentum"
            
            - type: "indicator_cross_above"
              indicator: "macd"
              timeframe: "1m"
              value: 0
              description: "MACD crosses above zero"

  # Short entry conditions
  short_entry:
    - logic: "AND"
      conditions:
        # Breakdown conditions
        - type: "price_below"
          indicator: "bb_lower"
          timeframe: "1m"
          description: "Price breaks below Bollinger Band lower"
        
        - type: "volume_surge"
          value: 150
          description: "Strong volume surge (50% above average)"
        
        # Momentum confirmation
        - logic: "OR"
          conditions:
            - type: "indicator_below"
              indicator: "rsi"
              timeframe: "1m"
              value: 40
              description: "RSI shows bearish momentum"
            
            - type: "indicator_cross_below"
              indicator: "macd"
              timeframe: "1m"
              value: 0
              description: "MACD crosses below zero"

  # Exit conditions (tight management)
  exit:
    - logic: "OR"
      conditions:
        # Profit targets
        - type: "position_pnl_above"
          value: 0.5
          description: "Take profit at 0.5%"
        
        # Stop losses
        - type: "position_pnl_below"
          value: -0.25
          description: "Stop loss at -0.25%"
        
        # Technical exits
        - type: "indicator_above"
          indicator: "rsi"
          timeframe: "1m"
          value: 75
          description: "RSI overbought exit"
        
        - type: "indicator_below"
          indicator: "rsi"
          timeframe: "1m"
          value: 25
          description: "RSI oversold exit"
        
        # Time-based exit (scalping)
        - type: "position_duration"
          value: 300  # 5 minutes maximum
          description: "Maximum hold time for scalping"

# Advanced time filters
time_filters:
  # High-activity periods only
  active_sessions:
    - name: "London Open"
      start_hour: 8
      end_hour: 10
      timezone: "UTC"
    
    - name: "New York Open"
      start_hour: 13
      end_hour: 15
      timezone: "UTC"
    
    - name: "Asian Close"
      start_hour: 6
      end_hour: 8
      timezone: "UTC"
  
  # Avoid low-liquidity periods
  blackout_periods:
    - start_hour: 22
      end_hour: 6
      description: "Low liquidity overnight"

# Market condition requirements
market_filters:
  # Volatility requirements
  min_volatility: 0.002      # Minimum 0.2% volatility for breakouts
  max_volatility: 0.02       # Maximum 2% volatility (avoid chaos)
  
  # Liquidity requirements
  min_volume_ratio: 1.2      # Minimum 120% of average volume
  max_spread_pct: 0.05       # Maximum 0.05% spread
  
  # Market structure
  min_range_pct: 0.1         # Minimum 0.1% price range
  max_range_pct: 1.0         # Maximum 1% price range

# Performance optimization
optimization:
  # Dynamic position sizing
  volatility_scaling: true
  volume_scaling: true
  
  # Adaptive parameters
  rsi_period: 14
  bb_period: 20
  bb_std: 2.0
  macd_fast: 12
  macd_slow: 26
  macd_signal: 9

# Performance targets (aggressive for scalping)
targets:
  min_win_rate: 55           # Higher win rate required
  min_profit_factor: 1.5     # Strong profit factor
  max_drawdown: 2.0          # Very tight drawdown control
  min_sharpe_ratio: 1.0      # Good risk-adjusted returns
  max_trade_duration: 10     # Maximum 10 minutes per trade
