from typing import Dict, Optional, List, Tuple
import ccxt.async_support as ccxt
from datetime import datetime, timedelta
import numpy as np
from utils.enums import PositionSide, MarketRegime
from utils.logger import get_logger, log_error

class PositionTracker:
    """Tracks and manages futures positions with enhanced historical tracking"""
    
    def __init__(self, exchange: ccxt.Exchange):
        self.exchange = exchange
        self.logger = get_logger()
        
        # Cache position data
        self._positions = {}
        self._balances = {}
        self.last_update = {}
        
        # Historical tracking
        self._position_history = {}  # Symbol -> List[Dict] tracking position changes
        self._balance_history = []   # List of balance snapshots
        self._peak_balance = 0       # Track highest balance for drawdown calc
        self._trade_performance = {} # Symbol -> Dict tracking trade metrics
        self._correlation_data = {}  # Store position correlation data
        
        # Performance metrics
        self._daily_roi = []        # Track daily ROI
        self._max_drawdown = 0      # Maximum drawdown seen
        self._win_rate = 0          # Current win rate
        self._profit_factor = 0     # Profit factor (gross profit / gross loss)
        
        # Risk metrics
        self._risk_exposure = {}    # Track risk exposure per symbol
        self._volatility_metrics = {} # Store volatility data
        
    async def get_position(self, symbol: str) -> Dict:
        """
        Get current position data for a symbol
        
        Returns
        -------
        Dict
            Position data including:
            - Side (long/short/none)
            - Size
            - Entry price
            - Unrealized PnL
            - Liquidation price
        """
        try:
            # Fetch position
            position = await self._fetch_position(symbol)
            
            # Handle case where there is no position at all
            if not position:
                return {
                    'side': PositionSide.NONE.value,
                    'size': 0,
                    'entry_price': None,
                    'leverage': 1,  # Default leverage when no position
                    'liq_price': None,
                    'pnl_usd': 0,
                    'pnl_pct': 0,
                    'liq_buffer_pct': 100,  # No position = no liquidation risk
                }
            
            # Handle case where position exists but size is 0
            if position.get('size', 0) == 0:
                return {
                    'side': PositionSide.NONE.value,
                    'size': 0,
                    'entry_price': None,
                    'leverage': position.get('leverage', 1),
                    'liq_price': None,
                    'pnl_usd': 0,
                    'pnl_pct': 0,
                    'liq_buffer_pct': 100
                }
            
            # Calculate metrics for active position
            entry_price = position.get('entry_price', 0)
            current_price = position.get('mark_price', 0)
            size = position.get('size', 0)
            side = PositionSide.LONG.value if size > 0 else PositionSide.SHORT.value
            
            # Calculate PnL
            if entry_price and current_price and size:
                pnl_usd = (current_price - entry_price) * abs(size)
                pnl_pct = ((current_price / entry_price) - 1) * 100
                if side == PositionSide.SHORT.value:
                    pnl_usd = -pnl_usd
                    pnl_pct = -pnl_pct
            else:
                pnl_usd = 0
                pnl_pct = 0
            
            return {
                'side': side,
                'size': abs(size),
                'entry_price': entry_price,
                'leverage': position.get('leverage', 1),
                'liq_price': position.get('liquidation_price'),
                'pnl_usd': pnl_usd,
                'pnl_pct': pnl_pct,
                'liq_buffer_pct': self._calculate_liquidation_buffer(
                    current_price, 
                    position.get('liquidation_price', 0),
                    side
                )
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error fetching position data: {str(e)}")
            raise
            
    async def get_balance(self) -> Dict:
        """Get account balance information"""
        try:
            balance = await self.exchange.fetch_balance()
            
            return {
                'total': balance['total']['USDT'],
                'free': balance['free']['USDT'],
                'used': balance['used']['USDT']
            }
            
        except Exception as e:
            log_error(f"Error fetching balance: {str(e)}")
            raise
            
    async def _fetch_position(self, symbol: str) -> Optional[Dict]:
        """Fetch position from exchange"""
        try:
            positions = await self.exchange.fetch_positions([symbol])
            return positions[0] if positions else None
            
        except Exception as e:
            log_error(f"Error fetching position from exchange: {str(e)}")
            raise
            
    def update_position_cache(self, symbol: str, position_data: Dict):
        """Update cached position data"""
        self._positions[symbol] = position_data
        self.last_update[symbol] = datetime.now()
        
    async def close(self):
        """Clean up resources"""
        # Nothing to clean up currently, but might be needed in future
    async def get_account_metrics(self) -> Dict:
        """Get comprehensive account metrics including historical performance and enhanced LLM awareness"""
        try:
            # Get base metrics
            balance = await self.exchange.fetch_balance()
            positions = await self.exchange.fetch_positions()
            
            # Calculate base metrics
            total_balance = float(balance['total']['USDT'])
            free_balance = float(balance['free']['USDT'])
            used_margin = float(balance['used']['USDT'])
            
            # Calculate position metrics
            total_position_value = 0
            total_unrealized_pnl = 0
            position_count = 0
            max_leverage_used = 0
            
            for position in positions:
                if float(position.get('size', 0)) != 0:
                    position_count += 1
                    total_position_value += abs(float(position['notional']))
                    total_unrealized_pnl += float(position.get('unrealizedPnl', 0))
                    max_leverage_used = max(max_leverage_used, float(position.get('leverage', 0)))
            
            # Get performance metrics
            performance = await self.get_performance_metrics()
            
            # Get portfolio correlation analysis
            portfolio_correlation = await self.analyze_portfolio_correlation() if position_count > 1 else {
                'diversification_score': 1.0,
                'avg_correlation': 0,
                'high_correlation_pairs': []
            }
            
            # Calculate risk-adjusted metrics for LLM awareness
            avg_roi = performance['overall_performance']['avg_daily_roi']
            max_dd = performance['overall_performance']['max_drawdown']
            
            # Calculate Sharpe ratio (using daily ROI)
            roi_array = np.array(self._daily_roi[-30:] if len(self._daily_roi) > 30 else self._daily_roi)
            sharpe_ratio = np.mean(roi_array) / np.std(roi_array) * np.sqrt(365) if len(roi_array) > 0 and np.std(roi_array) > 0 else 0
            
            # Calculate Calmar ratio (annualized return / max drawdown)
            calmar_ratio = (avg_roi * 365 / max_dd) if max_dd > 0 else 0
            
            # Calculate volatility metrics
            volatility_metrics = {}
            active_symbols = list(self._positions.keys())
            if active_symbols and 'volatility_metrics' in performance:
                volatility_metrics = {
                    'overall_volatility': np.mean([m.get('current', 0) for m in performance.get('volatility_metrics', {}).values()]),
                    'symbols': {s: self._volatility_metrics.get(s, {}) for s in active_symbols}
                }
            
            return {
                'account_metrics': {
                    'total_balance': total_balance,
                    'free_balance': free_balance,
                    'used_margin': used_margin,
                    'available_margin': free_balance,
                    'total_position_value': total_position_value,
                    'unrealized_pnl': total_unrealized_pnl,
                    'position_count': position_count,
                    'max_leverage_used': max_leverage_used
                },
                'risk_metrics': {
                    'account_risk': float(total_position_value / total_balance if total_balance > 0 else 0),
                    'margin_usage_pct': float((used_margin / total_balance * 100) if total_balance > 0 else 0),
                    'available_margin_pct': float((free_balance / total_balance * 100) if total_balance > 0 else 0),
                    'max_drawdown': max_dd,
                    'risk_exposure': performance['risk_metrics']['total_risk_exposure'],
                    'sharpe_ratio': sharpe_ratio,
                    'calmar_ratio': calmar_ratio
                },
                'performance_metrics': {
                    'win_rate': performance['overall_performance']['win_rate'],
                    'profit_factor': performance['overall_performance']['profit_factor'],
                    'avg_daily_roi': avg_roi,
                    'correlation_risk': portfolio_correlation.get('avg_correlation', 0),
                    'highly_correlated_pairs': portfolio_correlation.get('high_correlation_pairs', []),
                    'diversification_score': portfolio_correlation.get('diversification_score', 1.0)
                },
                'volatility_metrics': volatility_metrics,
                'market_analysis': {
                    'detected_patterns': {s: self.detect_market_patterns(s, self._position_history.get(s, [])[-30:]) 
                                        for s in active_symbols if s in self._position_history}
                },
                'trading_capacity': {
                    'max_new_position_size': self._calculate_max_position_size(free_balance),
                    'safe_position_size': self._calculate_safe_position_size(free_balance, total_position_value / total_balance if total_balance > 0 else 0),
                    'position_size_limit_reached': (total_position_value / total_balance if total_balance > 0 else 0) > 0.8,
                    'margin_limit_reached': (used_margin / total_balance if total_balance > 0 else 0) > 0.7
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error fetching account metrics: {str(e)}")
            raise

    def _calculate_risk_score(self, account_risk: float, margin_usage: float, position_count: int) -> float:
        """Calculate overall risk score (0-1, higher is riskier)"""
        # Weight factors
        risk_weight = 0.4
        margin_weight = 0.4
        position_weight = 0.2
        
        # Calculate component scores
        risk_score = min(account_risk, 1.0) * risk_weight
        margin_score = margin_usage * margin_weight
        position_score = min(position_count / 5, 1.0) * position_weight  # Assume 5 positions is max
        
        return risk_score + margin_score + position_score

    def _calculate_max_position_size(self, free_balance: float) -> float:
        """Calculate maximum allowed position size"""
        # Use exchange limits and account settings
        exchange_max = self.exchange.options.get('maxPositionSize', float('inf'))
        account_max = free_balance * 0.95  # Keep 5% buffer
        
        return min(exchange_max, account_max)

    def _calculate_safe_position_size(self, free_balance: float, current_risk: float) -> float:
        """Calculate safe position size based on risk metrics"""
        base_size = free_balance * 0.02  # 2% base size
        
        # Reduce size if account is already at risk
        if current_risk > 0.5:  # Above 50% account risk
            risk_multiplier = max(0, (0.8 - current_risk) / 0.3)  # Scale down to zero as risk approaches 80%
            base_size *= risk_multiplier
            
        return base_size
    
    async def update_historical_metrics(self, symbol: str, position_data: Dict):
        """Update historical position and performance metrics"""
        now = datetime.now()
        
        # Update position history
        if symbol not in self._position_history:
            self._position_history[symbol] = []
        self._position_history[symbol].append({
            'timestamp': now,
            'position': position_data,
        })
        
        # Update trade performance
        if position_data['pnl_usd'] != 0:
            if symbol not in self._trade_performance:
                self._trade_performance[symbol] = {
                    'total_pnl': 0,
                    'win_count': 0,
                    'loss_count': 0,
                    'total_trades': 0,
                    'gross_profit': 0,
                    'gross_loss': 0
                }
            
            perf = self._trade_performance[symbol]
            perf['total_pnl'] += position_data['pnl_usd']
            if position_data['pnl_usd'] > 0:
                perf['win_count'] += 1
                perf['gross_profit'] += position_data['pnl_usd']
            else:
                perf['loss_count'] += 1
                perf['gross_loss'] += abs(position_data['pnl_usd'])
            perf['total_trades'] += 1
            
            # Update win rate and profit factor
            total_trades = perf['win_count'] + perf['loss_count']
            self._win_rate = perf['win_count'] / total_trades if total_trades > 0 else 0
            self._profit_factor = perf['gross_profit'] / perf['gross_loss'] if perf['gross_loss'] > 0 else 0
        
        # Update balance history and drawdown
        balance = await self.get_balance()
        self._balance_history.append({
            'timestamp': now,
            'balance': balance['total']
        })
        
        # Update peak balance and calculate drawdown
        if balance['total'] > self._peak_balance:
            self._peak_balance = balance['total']
        current_drawdown = ((self._peak_balance - balance['total']) / self._peak_balance) * 100 if self._peak_balance > 0 else 0
        self._max_drawdown = max(self._max_drawdown, current_drawdown)
        
        # Calculate daily ROI
        if len(self._balance_history) > 1:
            prev_balance = self._balance_history[-2]['balance']
            daily_roi = ((balance['total'] - prev_balance) / prev_balance) * 100
            self._daily_roi.append(daily_roi)
            
        # Update risk exposure
        self._update_risk_exposure(symbol, position_data)
        
        return {
            'win_rate': self._win_rate,
            'profit_factor': self._profit_factor,
            'max_drawdown': self._max_drawdown,
            'daily_roi': self._daily_roi[-1] if self._daily_roi else 0
        }

    def _update_risk_exposure(self, symbol: str, position_data: Dict):
        """Update risk exposure metrics for a symbol"""
        self._risk_exposure[symbol] = {
            'position_size': position_data['size'],
            'notional_value': position_data['size'] * position_data.get('entry_price', 0),
            'leverage': position_data['leverage'],
            'liquidation_risk': 100 - position_data['liq_buffer_pct']
        }
        
    async def get_performance_metrics(self) -> Dict:
        """Get comprehensive performance metrics"""
        metrics = {
            'overall_performance': {
                'win_rate': self._win_rate,
                'profit_factor': self._profit_factor,
                'max_drawdown': self._max_drawdown,
                'avg_daily_roi': sum(self._daily_roi) / len(self._daily_roi) if self._daily_roi else 0
            },
            'risk_metrics': {
                'total_risk_exposure': sum(risk['notional_value'] for risk in self._risk_exposure.values()),
                'avg_leverage': sum(risk['leverage'] for risk in self._risk_exposure.values()) / len(self._risk_exposure) if self._risk_exposure else 0,
                'highest_liq_risk': max((risk['liquidation_risk'] for risk in self._risk_exposure.values()), default=0)
            },
            'historical_data': {
                'balance_history': self._balance_history[-10:],  # Last 10 balance points
                'daily_roi_history': self._daily_roi[-10:],     # Last 10 daily ROIs
            }
        }
        
        # Add correlation data if we have multiple positions
        if len(self._positions) > 1:
            metrics['correlation_metrics'] = await self._calculate_position_correlations()
            
        return metrics
        
    async def _calculate_position_correlations(self) -> Dict:
        """Calculate correlation between different positions"""
        correlations = {}
        position_pairs = [(s1, s2) for s1 in self._positions for s2 in self._positions if s1 < s2]
        
        for sym1, sym2 in position_pairs:
            # Calculate correlation based on recent price movements
            correlation = await self._compute_price_correlation(sym1, sym2)
            correlations[f"{sym1}-{sym2}"] = correlation
            
        return correlations
    
    async def _compute_price_correlation(self, symbol1: str, symbol2: str, timeframes: List[str] = None) -> Dict:
        """
        Calculate correlation between price movements of different assets across timeframes
        
        Parameters
        ----------
        symbol1 : str
            First trading pair symbol
        symbol2 : str
            Second trading pair symbol
        timeframes : List[str], optional
            List of timeframes to analyze (e.g., ['1h', '4h', '1d'])
            
        Returns
        -------
        Dict
            Correlation metrics for different timeframes
        """
        if timeframes is None:
            timeframes = ['1h', '4h', '1d']
            
        correlations = {}
        
        try:
            for tf in timeframes:
                # Fetch historical price data for both symbols
                ohlcv1 = await self.exchange.fetch_ohlcv(symbol1, tf, limit=30)
                ohlcv2 = await self.exchange.fetch_ohlcv(symbol2, tf, limit=30)
                
                # Extract closing prices
                prices1 = [candle[4] for candle in ohlcv1]
                prices2 = [candle[4] for candle in ohlcv2]
                
                # Ensure same length
                min_len = min(len(prices1), len(prices2))
                if min_len < 10:  # Need at least 10 data points for meaningful correlation
                    continue
                    
                prices1 = prices1[:min_len]
                prices2 = prices2[:min_len]
                
                # Calculate returns
                returns1 = np.diff(prices1) / prices1[:-1]
                returns2 = np.diff(prices2) / prices2[:-1]
                
                # Calculate correlation coefficient
                if len(returns1) > 1 and len(returns2) > 1:
                    correlation = np.corrcoef(returns1, returns2)[0, 1]
                    correlations[tf] = float(correlation) if not np.isnan(correlation) else 0
                    
            # Calculate the overall correlation as weighted average
            weights = {'1h': 0.5, '4h': 0.3, '1d': 0.2}
            avail_tfs = set(correlations.keys()) & set(weights.keys())
            
            if avail_tfs:
                overall = sum(correlations[tf] * weights[tf] for tf in avail_tfs) / sum(weights[tf] for tf in avail_tfs)
            else:
                overall = 0
                
            result = {
                'overall': overall,
                'timeframes': correlations,
                'updated_at': datetime.now().isoformat()
            }
            
            # Store result in correlation data
            pair_key = f"{symbol1}-{symbol2}"
            self._correlation_data[pair_key] = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error calculating correlation: {str(e)}")
            return {'overall': 0, 'timeframes': {}}
            
    async def analyze_portfolio_correlation(self) -> Dict:
        """
        Analyze the correlation structure of the entire portfolio
        
        Returns
        -------
        Dict
            Portfolio correlation analysis including:
            - Average correlation
            - Highest correlations
            - Diversification score
        """
        positions = list(self._positions.keys())
        if len(positions) < 2:
            return {'diversification_score': 1.0, 'avg_correlation': 0, 'high_correlation_pairs': []}
            
        # Calculate all pairwise correlations
        correlations = {}
        for i, sym1 in enumerate(positions):
            for sym2 in positions[i+1:]:
                pair_key = f"{sym1}-{sym2}"
                
                # Use cached data if available and recent
                if pair_key in self._correlation_data:
                    data = self._correlation_data[pair_key]
                    timestamp = datetime.fromisoformat(data.get('updated_at', '2000-01-01T00:00:00'))
                    
                    # Use cached data if less than 1 hour old
                    if datetime.now() - timestamp < timedelta(hours=1):
                        correlations[pair_key] = data['overall']
                        continue
                
                # Otherwise compute new correlation
                data = await self._compute_price_correlation(sym1, sym2)
                correlations[pair_key] = data['overall']
                
        # Calculate metrics
        if correlations:
            avg_correlation = sum(abs(c) for c in correlations.values()) / len(correlations)
            high_correlation_pairs = [
                {'pair': pair, 'correlation': corr} 
                for pair, corr in correlations.items() 
                if abs(corr) > 0.7
            ]
            
            # Diversification score: 1.0 (perfectly diversified) to 0.0 (completely correlated)
            diversification_score = 1.0 - avg_correlation
            
            return {
                'diversification_score': diversification_score,
                'avg_correlation': avg_correlation,
                'high_correlation_pairs': high_correlation_pairs
            }
        else:
            return {'diversification_score': 1.0, 'avg_correlation': 0, 'high_correlation_pairs': []}
    
    def _calculate_volatility(self, symbol: str, window: int = 14) -> float:
        """Calculate historical volatility for a symbol"""
        if symbol not in self._position_history or len(self._position_history[symbol]) < window:
            return 0
        
        # Get closing prices from position history
        closes = [entry['position']['entry_price'] for entry in self._position_history[symbol][-window:]]
        
        # Calculate log returns
        log_returns = np.diff(np.log(closes))
        
        # Calculate and return volatility (standard deviation of returns)
        return np.std(log_returns) * 100  # Convert to percentage

    async def update_volatility_metrics(self, symbol: str):
        """Update volatility metrics for a symbol"""
        try:
            # Calculate and store volatility
            volatility = self._calculate_volatility(symbol)
            self._volatility_metrics[symbol] = {
                'volatility': volatility,
                'timestamp': datetime.now()
            }
            
            # Update position size based on volatility (scaling factor)
            if symbol in self._positions:
                position = self._positions[symbol]
                new_size = position['size'] * (1 + volatility / 100)  # Scale position size by volatility
                new_size = max(min(new_size, position['max_size']), position['min_size'])  # Clamp to min/max size
                
                # Update position with new size
                await self.exchange.change_position_size(symbol, new_size)
                
                self.logger.info(f"🔄 Adjusted position size for {symbol} to {new_size} based on volatility")
            
        except Exception as e:
            self.logger.error(f"Error updating volatility metrics for {symbol}: {str(e)}")
            raise

    async def track_market_volatility(self, symbol: str, price_data: List[float]) -> Dict:
        """
        Track and analyze market volatility for adaptive position sizing
        
        Parameters
        ----------
        symbol : str
            Trading pair symbol
        price_data : List[float]
            Recent price data for volatility calculation
            
        Returns
        -------
        Dict
            Volatility metrics and market regime assessment
        """
        if len(price_data) < 10:
            self.logger.warning(f"Insufficient data points for volatility calculation: {len(price_data)}")
            return {'volatility': 0, 'regime': MarketRegime.UNKNOWN.value}
            
        # Calculate returns
        returns = np.diff(price_data) / price_data[:-1]
        
        # Calculate volatility metrics
        volatility = np.std(returns) * np.sqrt(252 * 24 * 60)  # Annualized volatility
        recent_volatility = np.std(returns[-10:]) * np.sqrt(252 * 24 * 60)
        
        # Detect volatility regime
        if recent_volatility > volatility * 1.5:
            regime = MarketRegime.VOLATILE.value
        elif recent_volatility < volatility * 0.5:
            regime = MarketRegime.RANGING.value
        else:
            regime = MarketRegime.NORMAL.value
        
        # Store metrics
        self._volatility_metrics[symbol] = {
            'current': recent_volatility,
            'historical': volatility,
            'ratio': recent_volatility / volatility if volatility > 0 else 1.0,
            'regime': regime,
            'timestamp': datetime.now(),
            'data_points': len(price_data)
        }
        
        return self._volatility_metrics[symbol]
        
    def _adjust_position_size_for_volatility(self, symbol: str, base_size: float) -> float:
        """
        Adjust position size based on current volatility
        
        Parameters
        ----------
        symbol : str
            Trading pair symbol
        base_size : float
            Base position size before adjustment
            
        Returns
        -------
        float
            Volatility-adjusted position size
        """
        vol_metrics = self._volatility_metrics.get(symbol, {})
        
        # Default to no adjustment if no volatility data
        if not vol_metrics:
            return base_size
        
        # Scale position inversely with volatility ratio
        vol_ratio = vol_metrics.get('ratio', 1.0)
        
        if vol_ratio > 1.5:
            # High volatility - reduce position size
            adjustment = max(0.5, 1.0 / vol_ratio)
        elif vol_ratio < 0.5:
            # Low volatility - can increase position slightly
            adjustment = min(1.3, 1.0 / vol_ratio)
        else:
            # Normal volatility - no significant adjustment
            adjustment = 1.0
            
        return base_size * adjustment
    
    def detect_market_patterns(self, symbol: str, price_data: List[float], volume_data: List[float] = None) -> Dict:
        """
        Detect common market patterns using simple algorithmic techniques
        
        Parameters
        ----------
        symbol : str
            Trading pair symbol
        price_data : List[float]
            Recent price data for pattern detection
        volume_data : List[float], optional
            Recent volume data to supplement pattern detection
            
        Returns
        -------
        Dict
            Detected patterns and their confidence scores
        """
        if len(price_data) < 20:
            return {'patterns': [], 'confidence': 0}
            
        patterns = []
        confidence_scores = []
        
        # Normalize price for pattern detection
        norm_prices = np.array(price_data) / price_data[0]
        
        # Helper function to detect specific patterns
        def detect_double_top():
            # Look for M shape in recent prices
            if len(norm_prices) < 30:
                return 0.0
                
            # Use a simple sliding window to look for peaks
            window = norm_prices[-30:]
            mid_point = len(window) // 2
            
            left_half = window[:mid_point]
            right_half = window[mid_point:]
            
            left_peak = max(left_half)
            right_peak = max(right_half)
            
            left_peak_idx = np.argmax(left_half)
            right_peak_idx = np.argmax(right_half) + mid_point
            
            middle_trough = min(window[left_peak_idx:right_peak_idx])
            
            # Check if we have two similar peaks with a trough in between
            peaks_similar = abs(left_peak - right_peak) / left_peak < 0.02
            significant_trough = (left_peak - middle_trough) / left_peak > 0.02
            
            if peaks_similar and significant_trough:
                return 0.8
            elif peaks_similar:
                return 0.5
            else:
                return 0.0

        def detect_breakout():
            if len(norm_prices) < 20:
                return 0.0
                
            # Define lookback periods
            recent = norm_prices[-5:]
            history = norm_prices[-20:-5]
            
            # Find historical range
            hist_max = max(history)
            hist_min = min(history)
            hist_range = hist_max - hist_min
            
            # Check if recent prices break the range
            if max(recent) > hist_max + 0.01 and recent[-1] > recent[-2]:
                return min(1.0, 0.5 + (max(recent) - hist_max) / hist_range)
            elif min(recent) < hist_min - 0.01 and recent[-1] < recent[-2]:
                return min(1.0, 0.5 + (hist_min - min(recent)) / hist_range)
            else:
                return 0.0
                
        # Check for different patterns
        double_top_confidence = detect_double_top()
        if double_top_confidence > 0.5:
            patterns.append('double_top')
            confidence_scores.append(double_top_confidence)
            
        breakout_confidence = detect_breakout()
        if breakout_confidence > 0.5:
            patterns.append('breakout')
            confidence_scores.append(breakout_confidence)
        
        # Calculate overall confidence as an average if we have patterns
        overall_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        
        return {
            'patterns': patterns,
            'confidence': overall_confidence,
            'pattern_details': {
                'double_top': double_top_confidence,
                'breakout': breakout_confidence
            }
        }
