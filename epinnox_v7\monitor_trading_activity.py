#!/usr/bin/env python3
"""
Real-Time Trading Activity Monitor
Monitor LLM trading decisions, market conditions, and system performance
"""

import sys
import time
import asyncio
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from utils.logger import get_logger

class TradingActivityMonitor:
    """Monitor live trading activity and system performance"""
    
    def __init__(self):
        self.logger = get_logger()
        self.start_time = datetime.now()
        self.monitoring = False
        
        # Statistics
        self.stats = {
            'total_decisions': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'hold_signals': 0,
            'risk_breaches': 0,
            'volatility_alerts': 0,
            'last_balance_check': None,
            'system_uptime': 0
        }
        
        # Recent activity log
        self.recent_activity: List[Dict[str, Any]] = []
        self.max_activity_log = 100
        
    def start_monitoring(self):
        """Start monitoring trading activity"""
        self.monitoring = True
        self.logger.info("🔍 Trading Activity Monitor started")
        print("🔍 Epinnox v7 Trading Activity Monitor")
        print("=" * 60)
        print("📊 Monitoring live trading decisions and market conditions...")
        print("🔄 Press Ctrl+C to stop monitoring")
        print("=" * 60)
        
    def stop_monitoring(self):
        """Stop monitoring"""
        self.monitoring = False
        self.logger.info("🛑 Trading Activity Monitor stopped")
        
    def log_activity(self, activity_type: str, data: Dict[str, Any]):
        """Log trading activity"""
        activity = {
            'timestamp': datetime.now(),
            'type': activity_type,
            'data': data
        }
        
        self.recent_activity.append(activity)
        
        # Keep only recent activities
        if len(self.recent_activity) > self.max_activity_log:
            self.recent_activity = self.recent_activity[-self.max_activity_log:]
            
        # Update statistics
        self.update_stats(activity_type, data)
        
    def update_stats(self, activity_type: str, data: Dict[str, Any]):
        """Update monitoring statistics"""
        if activity_type == 'llm_decision':
            self.stats['total_decisions'] += 1
            action = data.get('action', 'hold').lower()
            if action == 'buy':
                self.stats['buy_signals'] += 1
            elif action == 'sell':
                self.stats['sell_signals'] += 1
            else:
                self.stats['hold_signals'] += 1
                
        elif activity_type == 'risk_breach':
            self.stats['risk_breaches'] += 1
            
        elif activity_type == 'volatility_alert':
            self.stats['volatility_alerts'] += 1
            
        elif activity_type == 'balance_check':
            self.stats['last_balance_check'] = datetime.now()
            
    def display_status(self):
        """Display current monitoring status"""
        uptime = datetime.now() - self.start_time
        self.stats['system_uptime'] = uptime.total_seconds()
        
        print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} | System Status")
        print("-" * 40)
        print(f"🕐 Uptime: {str(uptime).split('.')[0]}")
        print(f"🤖 LLM Decisions: {self.stats['total_decisions']}")
        print(f"📈 Buy Signals: {self.stats['buy_signals']}")
        print(f"📉 Sell Signals: {self.stats['sell_signals']}")
        print(f"⏸️  Hold Signals: {self.stats['hold_signals']}")
        print(f"⚠️  Risk Breaches: {self.stats['risk_breaches']}")
        print(f"📊 Volatility Alerts: {self.stats['volatility_alerts']}")
        
        if self.stats['last_balance_check']:
            last_check = self.stats['last_balance_check']
            print(f"💰 Last Balance Check: {last_check.strftime('%H:%M:%S')}")
        
        # Show recent activity
        if self.recent_activity:
            print("\n📋 Recent Activity:")
            for activity in self.recent_activity[-5:]:  # Show last 5 activities
                timestamp = activity['timestamp'].strftime('%H:%M:%S')
                activity_type = activity['type']
                print(f"   {timestamp} | {activity_type}")
                
    def monitor_log_file(self, log_file_path: str = "logs/epinnox_v7.log"):
        """Monitor log file for trading activity"""
        try:
            # Try to read the log file
            log_path = Path(log_file_path)
            if not log_path.exists():
                print(f"⚠️  Log file not found: {log_file_path}")
                return
                
            # Read existing log content
            with open(log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # Process recent log entries
            for line in lines[-50:]:  # Check last 50 lines
                self.parse_log_line(line.strip())
                
        except Exception as e:
            self.logger.error(f"Error monitoring log file: {str(e)}")
            
    def parse_log_line(self, line: str):
        """Parse log line for trading activity"""
        try:
            if 'Risk limit breached' in line and 'volatility' in line:
                # Extract volatility value
                parts = line.split('volatility = ')
                if len(parts) > 1:
                    volatility = float(parts[1])
                    self.log_activity('volatility_alert', {
                        'volatility': volatility,
                        'message': line
                    })
                    
            elif 'LLM decision' in line or 'Trading decision' in line:
                self.log_activity('llm_decision', {
                    'message': line
                })
                
            elif 'Balance:' in line and 'USDT' in line:
                self.log_activity('balance_check', {
                    'message': line
                })
                
            elif 'Trade executed' in line:
                self.log_activity('trade_execution', {
                    'message': line
                })
                
        except Exception as e:
            # Ignore parsing errors
            pass
            
    async def run_monitoring_loop(self):
        """Main monitoring loop"""
        self.start_monitoring()
        
        try:
            while self.monitoring:
                # Monitor log file for new activity
                self.monitor_log_file()
                
                # Display status every 30 seconds
                self.display_status()
                
                # Wait before next check
                await asyncio.sleep(30)
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
            
        finally:
            self.stop_monitoring()
            
    def generate_summary_report(self):
        """Generate summary report"""
        uptime = datetime.now() - self.start_time
        
        print("\n" + "=" * 60)
        print("📊 TRADING ACTIVITY SUMMARY REPORT")
        print("=" * 60)
        print(f"⏰ Monitoring Period: {str(uptime).split('.')[0]}")
        print(f"🤖 Total LLM Decisions: {self.stats['total_decisions']}")
        print(f"📈 Buy Signals: {self.stats['buy_signals']}")
        print(f"📉 Sell Signals: {self.stats['sell_signals']}")
        print(f"⏸️  Hold Signals: {self.stats['hold_signals']}")
        print(f"⚠️  Risk Breaches: {self.stats['risk_breaches']}")
        print(f"📊 Volatility Alerts: {self.stats['volatility_alerts']}")
        
        if self.stats['total_decisions'] > 0:
            buy_pct = (self.stats['buy_signals'] / self.stats['total_decisions']) * 100
            sell_pct = (self.stats['sell_signals'] / self.stats['total_decisions']) * 100
            hold_pct = (self.stats['hold_signals'] / self.stats['total_decisions']) * 100
            
            print(f"\n📈 Decision Distribution:")
            print(f"   Buy:  {buy_pct:.1f}%")
            print(f"   Sell: {sell_pct:.1f}%")
            print(f"   Hold: {hold_pct:.1f}%")
            
        print("=" * 60)

async def main():
    """Main entry point"""
    monitor = TradingActivityMonitor()
    
    try:
        await monitor.run_monitoring_loop()
    finally:
        monitor.generate_summary_report()

if __name__ == "__main__":
    asyncio.run(main())
