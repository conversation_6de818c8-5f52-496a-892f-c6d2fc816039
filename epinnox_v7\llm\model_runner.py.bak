import os
import asyncio
import time
from typing import Dict, Optional
import requests
from utils.logger import get_logger, log_error
from pathlib import Path
from datetime import datetime

# Import PyTorch if available
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

class ModelRunner:
    """Manages local LLM inference through HTTP API"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # LLM API settings - with fallbacks
        llm_config = config.get('llm', {})
        self.api_url = llm_config.get('api_url', "http://localhost:1234/v1/completions")
        self.model_name = llm_config.get('model_name', config.get('llm_model', "Phi-3.1-mini-128k-instruct.Q4_K_M.gguf"))
        
        # Model parameters
        self.max_tokens = llm_config.get('max_tokens', 512)
        self.temperature = llm_config.get('temperature', 0.3)
        self.timeout = llm_config.get('timeout', 30)
        self.retry_attempts = llm_config.get('retry_attempts', 3)
        self.retry_delay = llm_config.get('retry_delay', 1)
        
        # Connection state
        self.api_available = False
        
        # Debug logging setup
        self.debug_mode = config.get('debug_mode', False)
        if self.debug_mode:
            log_dir = Path(config['logging']['log_dir'])
            log_dir.mkdir(parents=True, exist_ok=True)
            self.prompt_log = log_dir / "llm_prompts.log"
            self.response_log = log_dir / "llm_responses.log"
            self._file_lock = asyncio.Lock()
            
    async def initialize(self):
        """Verify API connection with retries"""
        for attempt in range(self.retry_attempts):
            try:
                # Test API connection
                headers = {"Content-Type": "application/json"}
                test_payload = {
                    "prompt": "test",
                    "max_tokens": 1,
                    "model": self.model_name
                }
                
                response = requests.post(
                    self.api_url, 
                    json=test_payload,
                    headers=headers, 
                    timeout=self.timeout
                )
                response.raise_for_status()
                
                self.api_available = True
                self.logger.info(f"LLM API connection verified: {self.model_name}")
                return
                
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                self.logger.warning(f"LLM API connection attempt {attempt + 1} failed: {str(e)}")
                if attempt < self.retry_attempts - 1:
                    self.logger.info(f"Retrying in {self.retry_delay} seconds...")
                    await asyncio.sleep(self.retry_delay)
                    
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 404:
                    self.logger.error(f"LLM API endpoint not found at {self.api_url}")
                    # Don't retry on 404
                    break
                else:
                    self.logger.warning(f"HTTP error during LLM API test: {str(e)}")
                    if attempt < self.retry_attempts - 1:
                        await asyncio.sleep(self.retry_delay)
                        
            except Exception as e:
                self.logger.error(f"Unexpected error testing LLM API: {str(e)}")
                if attempt < self.retry_attempts - 1:
                    await asyncio.sleep(self.retry_delay)
                    
        # If we get here, all attempts failed
        self.logger.warning("LLM API unavailable - will use fallback mode")
        self.api_available = False
        
    async def get_trading_decision(self, prompt: str) -> Optional[str]:
        """Get trading decision with fallback behavior if API fails"""
        if not self.api_available:
            return self._get_fallback_response("LLM API unavailable")

        for attempt in range(self.retry_attempts):            try:
                headers = {"Content-Type": "application/json"}
                
                payload = {
                    "prompt": prompt,
                    "temperature": self.temperature,
                    "max_tokens": self.max_tokens,
                    "model": self.model_name
                }
                
                response = requests.post(
                    self.api_url,
                    json=payload,
                    headers=headers,
                    timeout=self.timeout
                )
                response.raise_for_status()
                
                result = response.json()
                model_output = result["choices"][0]["text"]
                
                await self._log_debug(prompt, model_output)
                return model_output
                
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                self.logger.warning(f"LLM API request failed (attempt {attempt + 1}): {str(e)}")
                if attempt < self.retry_attempts - 1:
                    await asyncio.sleep(self.retry_delay)
                else:
                    self.api_available = False
                    return self._get_fallback_response(f"Connection error: {str(e)}")
                    
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 404:
                    self.logger.error(f"LLM API endpoint not found: {str(e)}")
                    self.api_available = False
                    return self._get_fallback_response("API endpoint not found")
                else:
                    self.logger.warning(f"HTTP error in LLM request: {str(e)}")
                    if attempt < self.retry_attempts - 1:
                        await asyncio.sleep(self.retry_delay)
                    else:
                        return self._get_fallback_response(f"HTTP error: {str(e)}")
                        
            except Exception as e:
                self.logger.error(f"Unexpected error in get_trading_decision: {str(e)}")
                return self._get_fallback_response("Internal error")
                
    def _get_fallback_response(self, reason: str) -> str:
        """Generate a conservative fallback response when API fails"""
        fallback = {
            "action": "hold",
            "confidence": 0.5,
            "reason": f"Using fallback response: {reason}"
        }
        return str(fallback).replace("'", '"')

    async def close(self):
        """Clean up model resources"""
        try:
            # Clear CUDA cache if available
            if TORCH_AVAILABLE and hasattr(torch.cuda, 'is_available') and torch.cuda.is_available():
                torch.cuda.empty_cache()
            self.api_available = False
        except Exception as e:
            self.logger.error(f"Error in close: {str(e)}")
            
    async def _log_debug(self, prompt: str, response: str):
        """Log prompt and response in debug mode"""
        if not self.debug_mode:
            return
            
        try:
            async with self._file_lock:
                timestamp = datetime.now().isoformat()
                
                with open(self.prompt_log, 'a') as f:
                    f.write(f"\n=== {timestamp} ===\n{prompt}\n")
                    
                with open(self.response_log, 'a') as f:
                    f.write(f"\n=== {timestamp} ===\n{response}\n")
                    
        except Exception as e:
            self.logger.error(f"Failed to log debug info: {str(e)}")
