import asyncio
import signal
import time
from typing import Dict, Set
import yaml
from datetime import datetime, time, timedelta
from utils.logger import setup_logger, get_logger, prune_old_logs
from core.scalper_engine import ScalperEngine

class ScalperLoop:
    """Main trading loop coordinator"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        self.logger.info("Initializing ScalperLoop...")
        
        # Trading state
        self.is_running = False
        self.active_symbols: Set[str] = set(self.config.get('symbols', []))
        if not self.active_symbols:
            self.logger.warning("No trading symbols configured!")
        else:
            self.logger.info(f"Monitoring symbols: {', '.join(self.active_symbols)}")
            
        # Track last processing time per symbol
        self.last_processed: Dict[str, datetime] = {}
        
        # Set up log maintenance
        self.last_log_prune = datetime.now()
        self.log_prune_interval = timedelta(hours=24)  # Prune logs once per day
        
        # Initialize engine
        self.logger.info("Creating ScalperEngine...")
        self.engine = ScalperEngine(self.config)
        
    async def initialize(self):
        """Initialize all components"""
        try:
            self.logger.info("Initializing trading engine...")
            await self.engine.initialize()
            self.logger.info("Trading engine initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize trading engine: {str(e)}")
            raise
            
    async def start(self, shutdown_event: asyncio.Event):
        """
        Start the trading loop
        
        Parameters
        ----------
        shutdown_event : asyncio.Event
            Event to signal graceful shutdown
        """
        try:
            self.is_running = True
            self.logger.info("Trading loop started")
            
            # Main loop
            while not shutdown_event.is_set():
                try:
                    # Process all active symbols
                    for symbol in self.active_symbols:
                        if self._should_process_symbol(symbol):
                            try:
                                result = await self.engine.process_symbol(symbol)
                                if result:
                                    self.logger.info(f"Processed {symbol}: {result.get('action', 'unknown')}")
                            except Exception as e:
                                self.logger.error(f"Error processing {symbol}: {str(e)}")
                            finally:
                                # Update processing time
                                self.last_processed[symbol] = datetime.now()
                                
                    # Sleep between iterations
                    await asyncio.sleep(1)
                    
                    # Check if it's time to prune logs
                    if datetime.now() - self.last_log_prune > self.log_prune_interval:
                        self.logger.info("Running scheduled log pruning...")
                        prune_old_logs(self.config)
                        self.last_log_prune = datetime.now()
                
                except Exception as e:
                    self.logger.error(f"Error in main loop: {str(e)}")
                    await asyncio.sleep(5)  # Back off on error
                
        except Exception as e:
            self.logger.error(f"Critical error in trading loop: {str(e)}")
            raise
            
    def _should_process_symbol(self, symbol: str) -> bool:
        """Check if we should process a symbol based on time and conditions"""
        now = datetime.now()
        
        # Check trading hours if configured
        trading_hours = self.config.get('trading_hours', {})
        if trading_hours:
            try:
                trading_start = time.fromisoformat(trading_hours.get('start', '00:00:00'))
                trading_end = time.fromisoformat(trading_hours.get('end', '23:59:59'))
                
                current_time = now.time()
                if not trading_start <= current_time <= trading_end:
                    return False
            except ValueError as e:
                self.logger.error(f"Invalid trading hours format: {e}")
                return False
                
        # Check processing interval
        last_time = self.last_processed.get(symbol)
        if last_time:
            elapsed = (now - last_time).total_seconds()
            cooldown = self.config.get('cooldown_seconds', 60)
            if elapsed < cooldown:
                return False
                
        return True

    async def shutdown(self):
        """Clean shutdown of all components"""
        try:
            self.logger.info("Initiating shutdown sequence")
            
            # Stop main loop
            self.is_running = False
            
            # Close all positions if configured
            if self.config.get('close_positions_on_shutdown', True):
                self.logger.info("Closing all positions...")
                await self.engine.close_all_positions()
            
            # Close engine components
            await self.engine.close()
            
            self.logger.info("Shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {str(e)}")
            raise
            
async def main():
    """Main entry point"""
    # Create and start trading loop
    loop = ScalperLoop()
    await loop.start()
    
if __name__ == "__main__":
    # Run main loop
    asyncio.run(main())
