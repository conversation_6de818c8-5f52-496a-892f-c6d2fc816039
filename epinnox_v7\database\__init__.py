"""
Epinnox v7 Database Module

This module provides comprehensive database functionality for:
- Trade history storage and retrieval
- Strategy performance tracking
- Parameter optimization results
- User preferences and settings
- Backtest results persistence
- LLM simulation data storage

Supports both SQLite (development) and PostgreSQL (production).
"""

from .database_manager import DatabaseManager
from .models import (
    TradeRecord,
    StrategyPerformance,
    BacktestResult,
    OptimizationResult,
    LLMPrediction,
    PositionRecord,
    UserPreference
)
from .repositories import (
    TradeRepository,
    BacktestRepository,
    OptimizationRepository,
    LLMRepository,
    UserRepository
)

__all__ = [
    'DatabaseManager',
    'TradeRecord',
    'StrategyPerformance',
    'BacktestResult',
    'OptimizationResult',
    'LLMPrediction',
    'UserPreference',
    'TradeRepository',
    'BacktestRepository',
    'OptimizationRepository',
    'LLMRepository',
    'UserRepository'
]
