{"timestamp": "2025-06-14T16:32:37.988918", "summary": {"total_tests": 23, "passed_tests": 23, "success_rate": 100.0}, "details": {"imports": {"config_validator": true, "backtester": true, "llm_sim": true, "logger": true}, "functionality": {"config_load": {"success": true, "has_symbols": true}, "backtester_init": {"success": true}, "data_fetch": {"success": true, "candles": 144, "fetch_time": 3.1468899250030518}, "strategy_load": {"success": true, "strategy_name": "Simple Momentum Strategy"}, "llm_worker": {"success": true}}, "files": {"files": {"config/scalper_config.yaml": true, "strategy_builder/strategies/simple_momentum.yaml": true, "gui/dashboard.py": true, "gui/strategy_tab.py": true, "gui/parameter_sweeper.py": true, "gui/llm_sim_tab.py": true, "main.py": true}, "directories": {"logs": true, "logs/backtest_results": true, "gui": true, "strategy_builder": true, "utils": true, "core": true}}, "gui": {"success": true, "gui_ready": true}}}