#!/usr/bin/env python3
"""
GUI Launcher for Epinnox v7 Dashboard

This script launches the PyQt5 dashboard interface for Epinnox v7,
providing visual strategy analysis, parameter optimization, and
live LLM simulation monitoring.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set environment variables for better GUI experience
os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '1'

try:
    from gui.dashboard import main as dashboard_main
    
    if __name__ == "__main__":
        print("🚀 Launching Epinnox v7 Dashboard...")
        print("📊 Features available:")
        print("   • Strategy Analysis & Backtesting")
        print("   • Parameter Optimization")
        print("   • Live LLM Simulation")
        print("   • Performance Monitoring")
        print()
        
        dashboard_main()
        
except ImportError as e:
    print(f"❌ Error importing GUI components: {e}")
    print("📦 Please install required dependencies:")
    print("   pip install PyQt5 matplotlib plotly")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error launching dashboard: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
