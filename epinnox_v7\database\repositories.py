"""
Repository Classes for Epinnox v7 Database Operations

Provides high-level data access patterns for all database entities.
Each repository handles CRUD operations and business logic for its domain.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, and_, or_, func

from .models import (
    TradeRecord, StrategyPerformance, BacktestResult, 
    OptimizationResult, LLMPrediction, UserPreference
)
from utils.logger import get_logger

logger = get_logger()


class BaseRepository:
    """Base repository with common operations"""
    
    def __init__(self, session: Session, model_class):
        self.session = session
        self.model_class = model_class
    
    def create(self, **kwargs) -> Any:
        """Create new record"""
        instance = self.model_class(**kwargs)
        self.session.add(instance)
        self.session.flush()
        return instance
    
    def get_by_id(self, record_id: int) -> Optional[Any]:
        """Get record by ID"""
        return self.session.query(self.model_class).filter(
            self.model_class.id == record_id
        ).first()
    
    def get_all(self, limit: Optional[int] = None) -> List[Any]:
        """Get all records with optional limit"""
        query = self.session.query(self.model_class)
        if limit:
            query = query.limit(limit)
        return query.all()
    
    def delete_by_id(self, record_id: int) -> bool:
        """Delete record by ID"""
        record = self.get_by_id(record_id)
        if record:
            self.session.delete(record)
            return True
        return False


class TradeRepository(BaseRepository):
    """Repository for trade records"""
    
    def __init__(self, session: Session):
        super().__init__(session, TradeRecord)
    
    def create_trade(self, trade_data: Dict[str, Any]) -> TradeRecord:
        """Create new trade record"""
        return self.create(**trade_data)
    
    def get_trades_by_symbol(self, symbol: str, limit: Optional[int] = None) -> List[TradeRecord]:
        """Get trades for specific symbol"""
        query = self.session.query(TradeRecord).filter(
            TradeRecord.symbol == symbol
        ).order_by(desc(TradeRecord.entry_time))
        
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    def get_trades_by_strategy(self, strategy_name: str, limit: Optional[int] = None) -> List[TradeRecord]:
        """Get trades for specific strategy"""
        query = self.session.query(TradeRecord).filter(
            TradeRecord.strategy_name == strategy_name
        ).order_by(desc(TradeRecord.entry_time))
        
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    def get_trades_by_date_range(self, start_date: datetime, end_date: datetime) -> List[TradeRecord]:
        """Get trades within date range"""
        return self.session.query(TradeRecord).filter(
            and_(
                TradeRecord.entry_time >= start_date,
                TradeRecord.entry_time <= end_date
            )
        ).order_by(desc(TradeRecord.entry_time)).all()
    
    def get_performance_summary(self, 
                              strategy_name: Optional[str] = None,
                              symbol: Optional[str] = None,
                              days: int = 30) -> Dict[str, Any]:
        """Get performance summary for trades"""
        
        # Build base query
        query = self.session.query(TradeRecord)
        
        # Apply filters
        if strategy_name:
            query = query.filter(TradeRecord.strategy_name == strategy_name)
        if symbol:
            query = query.filter(TradeRecord.symbol == symbol)
        
        # Date filter
        start_date = datetime.now(timezone.utc) - timedelta(days=days)
        query = query.filter(TradeRecord.entry_time >= start_date)
        
        trades = query.all()
        
        if not trades:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'total_pnl': 0.0,
                'avg_pnl': 0.0,
                'max_win': 0.0,
                'max_loss': 0.0
            }
        
        # Calculate metrics
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t.pnl_usd and t.pnl_usd > 0])
        total_pnl = sum(t.pnl_usd for t in trades if t.pnl_usd)
        
        pnl_values = [t.pnl_usd for t in trades if t.pnl_usd is not None]
        
        return {
            'total_trades': total_trades,
            'win_rate': (winning_trades / total_trades) * 100 if total_trades > 0 else 0,
            'total_pnl': total_pnl,
            'avg_pnl': total_pnl / total_trades if total_trades > 0 else 0,
            'max_win': max(pnl_values) if pnl_values else 0,
            'max_loss': min(pnl_values) if pnl_values else 0,
            'winning_trades': winning_trades,
            'losing_trades': total_trades - winning_trades
        }
    
    def get_recent_trades(self, hours: int = 24, limit: int = 100) -> List[TradeRecord]:
        """Get recent trades"""
        start_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        
        return self.session.query(TradeRecord).filter(
            TradeRecord.entry_time >= start_time
        ).order_by(desc(TradeRecord.entry_time)).limit(limit).all()


class BacktestRepository(BaseRepository):
    """Repository for backtest results"""
    
    def __init__(self, session: Session):
        super().__init__(session, BacktestResult)
    
    def create_backtest(self, backtest_data: Dict[str, Any]) -> BacktestResult:
        """Create new backtest result"""
        return self.create(**backtest_data)
    
    def get_backtest_by_id(self, backtest_id: str) -> Optional[BacktestResult]:
        """Get backtest by unique ID"""
        return self.session.query(BacktestResult).filter(
            BacktestResult.backtest_id == backtest_id
        ).first()
    
    def get_backtests_by_strategy(self, strategy_name: str, limit: int = 50) -> List[BacktestResult]:
        """Get backtests for specific strategy"""
        return self.session.query(BacktestResult).filter(
            BacktestResult.strategy_name == strategy_name
        ).order_by(desc(BacktestResult.created_at)).limit(limit).all()
    
    def get_backtests_by_symbol(self, symbol: str, limit: int = 50) -> List[BacktestResult]:
        """Get backtests for specific symbol"""
        return self.session.query(BacktestResult).filter(
            BacktestResult.symbol == symbol
        ).order_by(desc(BacktestResult.created_at)).limit(limit).all()
    
    def get_best_backtests(self, metric: str = 'total_pnl_pct', limit: int = 10) -> List[BacktestResult]:
        """Get best performing backtests by metric"""
        order_column = getattr(BacktestResult, metric, BacktestResult.total_pnl_pct)
        
        return self.session.query(BacktestResult).order_by(
            desc(order_column)
        ).limit(limit).all()
    
    def get_recent_backtests(self, days: int = 7, limit: int = 50) -> List[BacktestResult]:
        """Get recent backtests"""
        start_date = datetime.now(timezone.utc) - timedelta(days=days)
        
        return self.session.query(BacktestResult).filter(
            BacktestResult.created_at >= start_date
        ).order_by(desc(BacktestResult.created_at)).limit(limit).all()
    
    def get_backtest_summary(self) -> Dict[str, Any]:
        """Get summary statistics for all backtests"""
        total_backtests = self.session.query(BacktestResult).count()
        
        if total_backtests == 0:
            return {
                'total_backtests': 0,
                'avg_performance': 0.0,
                'best_performance': 0.0,
                'strategies_tested': 0,
                'symbols_tested': 0
            }
        
        # Get performance statistics
        avg_performance = self.session.query(
            func.avg(BacktestResult.total_pnl_pct)
        ).scalar() or 0.0
        
        best_performance = self.session.query(
            func.max(BacktestResult.total_pnl_pct)
        ).scalar() or 0.0
        
        # Count unique strategies and symbols
        strategies_tested = self.session.query(
            func.count(func.distinct(BacktestResult.strategy_name))
        ).scalar() or 0
        
        symbols_tested = self.session.query(
            func.count(func.distinct(BacktestResult.symbol))
        ).scalar() or 0
        
        return {
            'total_backtests': total_backtests,
            'avg_performance': float(avg_performance),
            'best_performance': float(best_performance),
            'strategies_tested': strategies_tested,
            'symbols_tested': symbols_tested
        }


class OptimizationRepository(BaseRepository):
    """Repository for optimization results"""
    
    def __init__(self, session: Session):
        super().__init__(session, OptimizationResult)
    
    def create_optimization(self, optimization_data: Dict[str, Any]) -> OptimizationResult:
        """Create new optimization result"""
        return self.create(**optimization_data)
    
    def get_optimization_by_id(self, optimization_id: str) -> Optional[OptimizationResult]:
        """Get optimization by unique ID"""
        return self.session.query(OptimizationResult).filter(
            OptimizationResult.optimization_id == optimization_id
        ).first()
    
    def get_optimizations_by_strategy(self, strategy_name: str) -> List[OptimizationResult]:
        """Get optimizations for specific strategy"""
        return self.session.query(OptimizationResult).filter(
            OptimizationResult.strategy_name == strategy_name
        ).order_by(desc(OptimizationResult.start_time)).all()
    
    def get_best_parameters(self, strategy_name: str, symbol: str) -> Optional[Dict[str, Any]]:
        """Get best parameters for strategy-symbol combination"""
        optimization = self.session.query(OptimizationResult).filter(
            and_(
                OptimizationResult.strategy_name == strategy_name,
                OptimizationResult.symbol == symbol
            )
        ).order_by(desc(OptimizationResult.best_performance)).first()
        
        return optimization.best_parameters if optimization else None


class LLMRepository(BaseRepository):
    """Repository for LLM predictions"""
    
    def __init__(self, session: Session):
        super().__init__(session, LLMPrediction)
    
    def create_prediction(self, prediction_data: Dict[str, Any]) -> LLMPrediction:
        """Create new LLM prediction"""
        return self.create(**prediction_data)
    
    def get_predictions_by_symbol(self, symbol: str, limit: int = 100) -> List[LLMPrediction]:
        """Get predictions for specific symbol"""
        return self.session.query(LLMPrediction).filter(
            LLMPrediction.symbol == symbol
        ).order_by(desc(LLMPrediction.timestamp)).limit(limit).all()
    
    def get_accuracy_stats(self, symbol: Optional[str] = None, days: int = 7) -> Dict[str, Any]:
        """Get prediction accuracy statistics"""
        query = self.session.query(LLMPrediction)
        
        if symbol:
            query = query.filter(LLMPrediction.symbol == symbol)
        
        # Date filter
        start_date = datetime.now(timezone.utc) - timedelta(days=days)
        query = query.filter(LLMPrediction.timestamp >= start_date)
        
        predictions = query.all()
        
        if not predictions:
            return {
                'total_predictions': 0,
                'accuracy_1m': 0.0,
                'accuracy_5m': 0.0,
                'accuracy_15m': 0.0
            }
        
        # Calculate accuracy
        total = len(predictions)
        correct_1m = len([p for p in predictions if p.prediction_correct_1m])
        correct_5m = len([p for p in predictions if p.prediction_correct_5m])
        correct_15m = len([p for p in predictions if p.prediction_correct_15m])
        
        return {
            'total_predictions': total,
            'accuracy_1m': (correct_1m / total) * 100 if total > 0 else 0,
            'accuracy_5m': (correct_5m / total) * 100 if total > 0 else 0,
            'accuracy_15m': (correct_15m / total) * 100 if total > 0 else 0,
            'avg_confidence': sum(p.confidence for p in predictions) / total if total > 0 else 0
        }
    
    def update_prediction_accuracy(self, prediction_id: str, 
                                 actual_prices: Dict[str, float]) -> bool:
        """Update prediction with actual price data"""
        prediction = self.session.query(LLMPrediction).filter(
            LLMPrediction.prediction_id == prediction_id
        ).first()
        
        if not prediction:
            return False
        
        # Update actual prices
        if '1m' in actual_prices:
            prediction.actual_price_1m = actual_prices['1m']
            prediction.prediction_correct_1m = self._check_prediction_accuracy(
                prediction, actual_prices['1m']
            )
        
        if '5m' in actual_prices:
            prediction.actual_price_5m = actual_prices['5m']
            prediction.prediction_correct_5m = self._check_prediction_accuracy(
                prediction, actual_prices['5m']
            )
        
        if '15m' in actual_prices:
            prediction.actual_price_15m = actual_prices['15m']
            prediction.prediction_correct_15m = self._check_prediction_accuracy(
                prediction, actual_prices['15m']
            )
        
        prediction.updated_at = datetime.now(timezone.utc)
        return True
    
    def _check_prediction_accuracy(self, prediction: LLMPrediction, actual_price: float) -> bool:
        """Check if prediction was accurate"""
        if prediction.predicted_action == 'LONG':
            return actual_price > prediction.current_price
        elif prediction.predicted_action == 'SHORT':
            return actual_price < prediction.current_price
        else:  # HOLD
            # Consider HOLD accurate if price moved less than 0.1%
            price_change_pct = abs(actual_price - prediction.current_price) / prediction.current_price
            return price_change_pct < 0.001


class UserRepository(BaseRepository):
    """Repository for user preferences"""
    
    def __init__(self, session: Session):
        super().__init__(session, UserPreference)
    
    def set_preference(self, user_id: str, key: str, value: Any, value_type: str = 'string') -> UserPreference:
        """Set user preference"""
        # Check if preference exists
        existing = self.session.query(UserPreference).filter(
            and_(
                UserPreference.user_id == user_id,
                UserPreference.preference_key == key
            )
        ).first()
        
        if existing:
            existing.preference_value = str(value)
            existing.preference_type = value_type
            existing.updated_at = datetime.now(timezone.utc)
            return existing
        else:
            return self.create(
                user_id=user_id,
                preference_key=key,
                preference_value=str(value),
                preference_type=value_type
            )
    
    def get_preference(self, user_id: str, key: str, default: Any = None) -> Any:
        """Get user preference"""
        preference = self.session.query(UserPreference).filter(
            and_(
                UserPreference.user_id == user_id,
                UserPreference.preference_key == key
            )
        ).first()
        
        if not preference:
            return default
        
        # Convert value based on type
        value = preference.preference_value
        if preference.preference_type == 'number':
            return float(value)
        elif preference.preference_type == 'boolean':
            return value.lower() in ('true', '1', 'yes')
        elif preference.preference_type == 'json':
            import json
            return json.loads(value)
        else:
            return value
    
    def get_all_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get all preferences for user"""
        preferences = self.session.query(UserPreference).filter(
            UserPreference.user_id == user_id
        ).all()
        
        result = {}
        for pref in preferences:
            result[pref.preference_key] = self.get_preference(user_id, pref.preference_key)
        
        return result
