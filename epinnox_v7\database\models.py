"""
Database Models for Epinnox v7

SQLAlchemy models for all database entities including trades, strategies,
backtests, optimizations, LLM predictions, and user preferences.
"""

from sqlalchemy import (
    Column, Integer, String, Float, DateTime, Boolean, Text, JSON,
    ForeignKey, Index, UniqueConstraint, Enum
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from enum import Enum as PyEnum
import secrets
import hashlib

Base = declarative_base()


class UserRole(PyEnum):
    """User role enumeration"""
    ADMIN = "admin"
    ANALYST = "analyst"
    VIEWER = "viewer"


class User(Base):
    """User account model"""
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    salt = Column(String(32), nullable=False)
    role = Column(Enum(UserRole), default=UserRole.VIEWER)

    # Account status
    is_active = Column(Boolean, default=True)
    is_locked = Column(Boolean, default=False)
    failed_login_attempts = Column(Integer, default=0)
    last_login = Column(DateTime)

    # Security settings
    require_2fa = Column(Boolean, default=False)
    totp_secret = Column(String(32))

    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def set_password(self, password: str):
        """Set password with salt and hash"""
        self.salt = secrets.token_hex(16)
        self.password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            self.salt.encode('utf-8'),
            100000
        ).hex()

    def verify_password(self, password: str) -> bool:
        """Verify password against stored hash"""
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            self.salt.encode('utf-8'),
            100000
        ).hex()
        return password_hash == self.password_hash


class UserSession(Base):
    """User session model"""
    __tablename__ = 'user_sessions'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    session_token = Column(String(64), unique=True, nullable=False)
    csrf_token = Column(String(64), nullable=False)

    # Session metadata
    ip_address = Column(String(45))  # IPv6 compatible
    user_agent = Column(Text)

    # Timing
    created_at = Column(DateTime, default=datetime.utcnow)
    last_activity = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)

    # Status
    is_active = Column(Boolean, default=True)

    def __init__(self, user_id: int, ip_address: str = None, user_agent: str = None):
        self.user_id = user_id
        self.session_token = secrets.token_urlsafe(48)
        self.csrf_token = secrets.token_urlsafe(32)
        self.ip_address = ip_address
        self.user_agent = user_agent
        self.expires_at = datetime.utcnow() + timedelta(minutes=30)  # 30 min timeout

    def is_expired(self) -> bool:
        """Check if session is expired"""
        return datetime.utcnow() > self.expires_at

    def refresh(self):
        """Refresh session expiration"""
        self.last_activity = datetime.utcnow()
        self.expires_at = datetime.utcnow() + timedelta(minutes=30)

    def invalidate(self):
        """Invalidate session"""
        self.is_active = False


class TradeRecord(Base):
    """Individual trade record with full execution details"""
    __tablename__ = 'trades'
    
    id = Column(Integer, primary_key=True)
    
    # Trade identification
    trade_id = Column(String(50), unique=True, nullable=False)
    strategy_name = Column(String(100), nullable=False)
    symbol = Column(String(20), nullable=False)
    
    # Trade details
    side = Column(String(10), nullable=False)  # LONG/SHORT
    entry_price = Column(Float, nullable=False)
    exit_price = Column(Float)
    quantity = Column(Float, nullable=False)
    leverage = Column(Float, default=1.0)
    
    # Timing
    entry_time = Column(DateTime, nullable=False)
    exit_time = Column(DateTime)
    duration_seconds = Column(Integer)
    
    # Performance
    pnl_usd = Column(Float)
    pnl_pct = Column(Float)
    fees_usd = Column(Float, default=0.0)
    slippage_bps = Column(Float, default=0.0)
    
    # Risk management
    stop_loss = Column(Float)
    take_profit = Column(Float)
    max_drawdown_pct = Column(Float)
    
    # Market conditions
    entry_volatility = Column(Float)
    exit_volatility = Column(Float)
    market_regime = Column(String(20))
    
    # LLM data (if applicable)
    llm_confidence = Column(Float)
    llm_reasoning = Column(Text)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_trades_symbol_time', 'symbol', 'entry_time'),
        Index('idx_trades_strategy_time', 'strategy_name', 'entry_time'),
        Index('idx_trades_pnl', 'pnl_usd'),
    )


class StrategyPerformance(Base):
    """Aggregated strategy performance metrics"""
    __tablename__ = 'strategy_performance'
    
    id = Column(Integer, primary_key=True)
    
    # Strategy identification
    strategy_name = Column(String(100), nullable=False)
    strategy_version = Column(String(20), default='1.0')
    symbol = Column(String(20), nullable=False)
    
    # Time period
    period_start = Column(DateTime, nullable=False)
    period_end = Column(DateTime, nullable=False)
    
    # Trade statistics
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    losing_trades = Column(Integer, default=0)
    win_rate = Column(Float, default=0.0)
    
    # Performance metrics
    total_pnl = Column(Float, default=0.0)
    total_pnl_pct = Column(Float, default=0.0)
    max_drawdown = Column(Float, default=0.0)
    max_drawdown_pct = Column(Float, default=0.0)
    sharpe_ratio = Column(Float, default=0.0)
    calmar_ratio = Column(Float, default=0.0)
    profit_factor = Column(Float, default=0.0)
    
    # Risk metrics
    avg_win = Column(Float, default=0.0)
    avg_loss = Column(Float, default=0.0)
    max_win = Column(Float, default=0.0)
    max_loss = Column(Float, default=0.0)
    
    # Efficiency metrics
    avg_trade_duration = Column(Float, default=0.0)  # seconds
    trades_per_day = Column(Float, default=0.0)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        UniqueConstraint('strategy_name', 'symbol', 'period_start', 'period_end'),
        Index('idx_strategy_perf_name_time', 'strategy_name', 'period_end'),
    )


class BacktestResult(Base):
    """Complete backtest execution results"""
    __tablename__ = 'backtest_results'
    
    id = Column(Integer, primary_key=True)
    
    # Backtest identification
    backtest_id = Column(String(50), unique=True, nullable=False)
    strategy_name = Column(String(100), nullable=False)
    symbol = Column(String(20), nullable=False)
    
    # Configuration
    initial_balance = Column(Float, nullable=False)
    leverage = Column(Float, default=1.0)
    timeframe = Column(String(10), nullable=False)
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    
    # Strategy parameters (JSON)
    strategy_config = Column(JSON)
    
    # Results summary
    total_trades = Column(Integer, default=0)
    win_rate = Column(Float, default=0.0)
    total_pnl = Column(Float, default=0.0)
    total_pnl_pct = Column(Float, default=0.0)
    max_drawdown_pct = Column(Float, default=0.0)
    sharpe_ratio = Column(Float, default=0.0)
    
    # Detailed results (JSON)
    trade_list = Column(JSON)  # List of all trades
    equity_curve = Column(JSON)  # Equity progression
    performance_metrics = Column(JSON)  # Additional metrics
    
    # Analysis
    performance_grade = Column(String(2))  # A, B, C, D, F
    recommendations = Column(JSON)  # List of recommendations
    
    # Execution details
    execution_time_seconds = Column(Float)
    data_points_processed = Column(Integer)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_backtest_strategy_symbol', 'strategy_name', 'symbol'),
        Index('idx_backtest_date', 'start_date', 'end_date'),
        Index('idx_backtest_performance', 'total_pnl_pct'),
    )


class OptimizationResult(Base):
    """Parameter optimization results"""
    __tablename__ = 'optimization_results'
    
    id = Column(Integer, primary_key=True)
    
    # Optimization identification
    optimization_id = Column(String(50), unique=True, nullable=False)
    strategy_name = Column(String(100), nullable=False)
    symbol = Column(String(20), nullable=False)
    
    # Optimization configuration
    parameter_ranges = Column(JSON)  # Parameter ranges tested
    optimization_metric = Column(String(50), default='total_pnl')  # What was optimized for
    
    # Results
    total_combinations = Column(Integer, default=0)
    completed_combinations = Column(Integer, default=0)
    best_parameters = Column(JSON)  # Best parameter combination
    best_performance = Column(Float)  # Best metric value
    
    # All results (JSON array)
    all_results = Column(JSON)  # All parameter combinations and results
    
    # Analysis
    parameter_sensitivity = Column(JSON)  # Which parameters matter most
    performance_distribution = Column(JSON)  # Distribution of results
    
    # Execution details
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime)
    execution_time_seconds = Column(Float)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_optimization_strategy_symbol', 'strategy_name', 'symbol'),
        Index('idx_optimization_performance', 'best_performance'),
    )


class LLMPrediction(Base):
    """LLM trading predictions and accuracy tracking"""
    __tablename__ = 'llm_predictions'
    
    id = Column(Integer, primary_key=True)
    
    # Prediction identification
    prediction_id = Column(String(50), unique=True, nullable=False)
    symbol = Column(String(20), nullable=False)
    
    # Market context
    timestamp = Column(DateTime, nullable=False)
    current_price = Column(Float, nullable=False)
    market_data = Column(JSON)  # Full market context
    
    # Prediction
    predicted_action = Column(String(10), nullable=False)  # LONG/SHORT/HOLD
    confidence = Column(Float, nullable=False)
    reasoning = Column(Text)
    entry_price = Column(Float)
    stop_loss = Column(Float)
    take_profit = Column(Float)
    position_size = Column(Float)
    
    # Accuracy tracking
    actual_price_1m = Column(Float)  # Price after 1 minute
    actual_price_5m = Column(Float)  # Price after 5 minutes
    actual_price_15m = Column(Float)  # Price after 15 minutes
    
    prediction_correct_1m = Column(Boolean)
    prediction_correct_5m = Column(Boolean)
    prediction_correct_15m = Column(Boolean)
    
    # Performance if executed
    hypothetical_pnl_1m = Column(Float)
    hypothetical_pnl_5m = Column(Float)
    hypothetical_pnl_15m = Column(Float)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_llm_symbol_time', 'symbol', 'timestamp'),
        Index('idx_llm_accuracy', 'prediction_correct_5m'),
        Index('idx_llm_confidence', 'confidence'),
    )


class PositionRecord(Base):
    """Live trading positions tracking"""
    __tablename__ = 'positions'

    id = Column(Integer, primary_key=True)

    # Position identification
    position_id = Column(String(50), unique=True, nullable=False)
    symbol = Column(String(20), nullable=False)
    exchange = Column(String(20), nullable=False, default='htx')

    # Position details
    side = Column(String(10), nullable=False)  # long/short
    size = Column(Float, nullable=False)
    entry_price = Column(Float, nullable=False)
    current_price = Column(Float)
    mark_price = Column(Float)

    # P&L tracking
    unrealized_pnl = Column(Float, default=0.0)
    unrealized_pnl_pct = Column(Float, default=0.0)
    realized_pnl = Column(Float, default=0.0)

    # Risk management
    margin_used = Column(Float, default=0.0)
    liquidation_price = Column(Float)
    leverage = Column(Float, default=1.0)

    # Order management
    stop_loss_price = Column(Float)
    take_profit_price = Column(Float)
    stop_loss_order_id = Column(String(50))
    take_profit_order_id = Column(String(50))

    # Status
    status = Column(String(20), default='open')  # open/closed/liquidated

    # Timestamps
    opened_at = Column(DateTime, nullable=False)
    closed_at = Column(DateTime)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('idx_position_symbol', 'symbol'),
        Index('idx_position_status', 'status'),
        Index('idx_position_exchange', 'exchange'),
        Index('idx_position_opened', 'opened_at'),
    )


class UserPreference(Base):
    """User preferences and settings"""
    __tablename__ = 'user_preferences'
    
    id = Column(Integer, primary_key=True)
    
    # User identification
    user_id = Column(String(50), nullable=False)
    
    # Preference details
    preference_key = Column(String(100), nullable=False)
    preference_value = Column(Text)  # JSON string for complex values
    preference_type = Column(String(20), default='string')  # string, number, boolean, json
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        UniqueConstraint('user_id', 'preference_key'),
        Index('idx_user_prefs', 'user_id'),
    )


# Additional utility models for system monitoring
class SystemMetric(Base):
    """System performance and health metrics"""
    __tablename__ = 'system_metrics'

    id = Column(Integer, primary_key=True)

    # Metric identification
    metric_name = Column(String(100), nullable=False)
    metric_category = Column(String(50), nullable=False)  # performance, health, trading

    # Metric data
    timestamp = Column(DateTime, nullable=False)
    value = Column(Float, nullable=False)
    unit = Column(String(20))  # MB, %, seconds, etc.

    # Additional context
    metric_metadata = Column(JSON)  # Additional metric context

    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('idx_system_metrics_name_time', 'metric_name', 'timestamp'),
        Index('idx_system_metrics_category', 'metric_category'),
    )
