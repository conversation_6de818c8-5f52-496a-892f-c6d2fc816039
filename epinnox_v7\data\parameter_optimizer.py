from typing import Dict, List, Optional
import numpy as np
import pandas as pd
from utils.logger import get_logger

class ParameterOptimizer:
    """Optimizes trading parameters per symbol"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # Initialize parameter caches
        self.symbol_params = {}
        self.performance_history = {}
        self.optimization_history = {}
        
        # Default parameters
        self.default_params = {
            'entry_threshold': 0.75,      # Minimum entry confidence
            'exit_threshold': 0.6,        # Minimum exit confidence
            'position_size_base': 0.1,    # Base position size (% of capital)
            'max_position_size': 0.3,     # Maximum position size
            'sl_atr_multiplier': 2.0,     # Stop loss ATR multiplier
            'tp_atr_multiplier': 3.0,     # Take profit ATR multiplier
            'min_rr_ratio': 1.5,          # Minimum risk/reward ratio
            'max_correlation': 0.7,       # Maximum correlation threshold
            'min_liquidity': 0.6,         # Minimum liquidity score
            'trend_threshold': 0.3        # Minimum trend strength
        }
        
        # Parameter ranges for optimization
        self.param_ranges = {
            'entry_threshold': (0.6, 0.9),
            'exit_threshold': (0.4, 0.8),
            'position_size_base': (0.05, 0.2),
            'max_position_size': (0.2, 0.4),
            'sl_atr_multiplier': (1.5, 3.0),
            'tp_atr_multiplier': (2.0, 4.0),
            'min_rr_ratio': (1.2, 2.0),
            'max_correlation': (0.5, 0.8),
            'min_liquidity': (0.4, 0.8),
            'trend_threshold': (0.2, 0.5)
        }
        
        # Optimization settings
        self.min_trades = 20              # Minimum trades for optimization
        self.optimization_window = 100    # Number of trades to consider
        self.adaptation_rate = 0.1        # Rate of parameter adjustment

    def get_symbol_parameters(self, symbol: str) -> Dict:
        """Get optimized parameters for a symbol"""
        if symbol not in self.symbol_params:
            # Initialize with default parameters
            self.symbol_params[symbol] = self.default_params.copy()
            
        return self.symbol_params[symbol]

    def update_performance(self, symbol: str, trade_result: Dict):
        """Update performance history with new trade result"""
        if symbol not in self.performance_history:
            self.performance_history[symbol] = []
            
        self.performance_history[symbol].append({
            'timestamp': pd.Timestamp.now(),
            'params': self.symbol_params[symbol].copy(),
            'result': trade_result
        })
        
        # Keep only recent history
        if len(self.performance_history[symbol]) > self.optimization_window:
            self.performance_history[symbol] = self.performance_history[symbol][-self.optimization_window:]
            
        # Check if optimization is needed
        if len(self.performance_history[symbol]) >= self.min_trades:
            self._optimize_parameters(symbol)

    def _optimize_parameters(self, symbol: str):
        """Optimize parameters based on recent performance"""
        try:
            history = self.performance_history[symbol]
            
            # Calculate performance metrics
            win_rate = self._calculate_win_rate(history)
            avg_return = self._calculate_avg_return(history)
            risk_adjusted_return = self._calculate_risk_adjusted_return(history)
            
            # Current parameters
            current_params = self.symbol_params[symbol]
            
            # Optimize each parameter
            new_params = {}
            for param, (min_val, max_val) in self.param_ranges.items():
                current_val = current_params[param]
                
                # Calculate parameter adjustment
                adjustment = self._calculate_parameter_adjustment(
                    param,
                    history,
                    win_rate,
                    avg_return,
                    risk_adjusted_return
                )
                
                # Apply adjustment with limits
                new_val = current_val + adjustment * self.adaptation_rate
                new_val = max(min_val, min(max_val, new_val))
                new_params[param] = float(new_val)
            
            # Update parameters
            self.symbol_params[symbol] = new_params
            
            # Log optimization
            self._log_optimization(symbol, current_params, new_params)
            
        except Exception as e:
            self.logger.error(f"Error optimizing parameters for {symbol}: {str(e)}")

    def _calculate_win_rate(self, history: List[Dict]) -> float:
        """Calculate win rate from trade history"""
        if not history:
            return 0.0
            
        wins = sum(1 for trade in history if trade['result']['pnl'] > 0)
        return float(wins / len(history))

    def _calculate_avg_return(self, history: List[Dict]) -> float:
        """Calculate average return from trade history"""
        if not history:
            return 0.0
            
        returns = [trade['result']['pnl'] for trade in history]
        return float(np.mean(returns))

    def _calculate_risk_adjusted_return(self, history: List[Dict]) -> float:
        """Calculate risk-adjusted return from trade history"""
        if not history:
            return 0.0
            
        returns = [trade['result']['pnl'] for trade in history]
        if not returns or np.std(returns) == 0:
            return 0.0
            
        return float(np.mean(returns) / np.std(returns))

    def _calculate_parameter_adjustment(
        self,
        param: str,
        history: List[Dict],
        win_rate: float,
        avg_return: float,
        risk_adjusted_return: float
    ) -> float:
        """Calculate adjustment for a specific parameter"""
        try:
            # Group trades by parameter value
            param_performance = {}
            for trade in history:
                param_val = trade['params'][param]
                if param_val not in param_performance:
                    param_performance[param_val] = []
                param_performance[param_val].append(trade['result']['pnl'])
            
            # Calculate performance for each parameter value
            best_val = None
            best_performance = float('-inf')
            
            for param_val, returns in param_performance.items():
                # Calculate composite performance score
                win_rate_local = sum(1 for r in returns if r > 0) / len(returns)
                avg_return_local = np.mean(returns)
                sharpe_local = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
                
                performance = (
                    win_rate_local * 0.3 +
                    avg_return_local * 0.3 +
                    sharpe_local * 0.4
                )
                
                if performance > best_performance:
                    best_performance = performance
                    best_val = param_val
            
            if best_val is None:
                return 0.0
                
            # Calculate adjustment direction and magnitude
            current_val = history[-1]['params'][param]
            adjustment = best_val - current_val
            
            return float(adjustment)
            
        except Exception as e:
            self.logger.error(f"Error calculating parameter adjustment: {str(e)}")
            return 0.0

    def _log_optimization(
        self,
        symbol: str,
        old_params: Dict,
        new_params: Dict
    ):
        """Log parameter optimization results"""
        if symbol not in self.optimization_history:
            self.optimization_history[symbol] = []
            
        self.optimization_history[symbol].append({
            'timestamp': pd.Timestamp.now(),
            'old_params': old_params,
            'new_params': new_params
        })
        
        # Log changes
        changes = []
        for param in old_params:
            if abs(old_params[param] - new_params[param]) > 0.0001:
                changes.append(f"{param}: {old_params[param]:.4f} -> {new_params[param]:.4f}")
                
        if changes:
            self.logger.info(f"Parameter optimization for {symbol}:")
            for change in changes:
                self.logger.info(f"  {change}")

    def get_optimization_summary(self, symbol: str) -> Dict:
        """Get optimization summary for a symbol"""
        if symbol not in self.optimization_history:
            return {}
            
        history = self.optimization_history[symbol]
        if not history:
            return {}
            
        # Get parameter evolution
        param_evolution = {}
        for param in self.default_params:
            param_evolution[param] = [
                opt['new_params'][param] for opt in history
            ]
            
        # Calculate stability
        stability = {}
        for param, values in param_evolution.items():
            if len(values) > 1:
                stability[param] = 1 - np.std(values) / np.mean(values)
            else:
                stability[param] = 1.0
                
        return {
            'optimization_count': len(history),
            'parameter_stability': stability,
            'current_parameters': self.symbol_params[symbol],
            'last_optimization': history[-1]['timestamp']
        }
