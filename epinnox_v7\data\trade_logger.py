import json
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
import pandas as pd
from utils.logger import get_logger, log_trade, log_performance

class TradeLogger:
    """Logs trades and calculates performance metrics"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # Get log directory from config with fallback
        log_dir = Path(self.config.get('logging', {}).get('log_dir', 'logs'))
        self.log_dir = log_dir
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize log files
        self.trade_log_file = self.log_dir / "trades.json"
        self.csv_log_file = self.log_dir / "trade_history.csv"
        self.trades: List[Dict] = self._load_trades()
        
        # Ensure CSV header exists
        if not self.csv_log_file.exists():
            with open(self.csv_log_file, 'w') as f:
                f.write("timestamp,symbol,action,entry_price,exit_price,pnl_pct,confidence,reason\n")
        
        # Performance metrics
        self.total_trades = len(self.trades)
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        
        # Lock for thread-safe file writes
        self._file_lock = asyncio.Lock()
        
        # Calculate initial metrics
        self._calculate_metrics()

    async def log_trade(self, trade_data: Dict):
        """
        Log a completed trade with performance metrics
        
        Parameters
        ----------
        trade_data : Dict
            Trade information including:
            - symbol: str
            - action: str (long/short/close)
            - entry_price: float
            - exit_price: Optional[float]
            - size: float
            - pnl: float
            - pnl_pct: float
            - confidence: float
            - reason: str
            - timestamp: float
        """
        try:
            async with self._file_lock:
                # Add trade to memory
                self.trades.append(trade_data)
                
                # Update metrics
                self._calculate_metrics()
                
                # Log trade details
                log_trade(
                    f"{trade_data['symbol']} {trade_data['action']} "
                    f"PnL: {trade_data.get('pnl', 0):.2f} USDT ({trade_data.get('pnl_pct', 0):.2f}%)"
                )
                
                # Save to JSON file
                self._save_trades()
                
                # Append to CSV log
                csv_line = (
                    f"{datetime.fromtimestamp(trade_data['timestamp']).isoformat()},"
                    f"{trade_data['symbol']},"
                    f"{trade_data['action']},"
                    f"{trade_data['entry_price']},"
                    f"{trade_data.get('exit_price', '')},"
                    f"{trade_data.get('pnl_pct', 0):.2f},"
                    f"{trade_data.get('confidence', 0):.2f},"
                    f"\"{trade_data.get('reason', '')}\"\n"
                )
                
                with open(self.csv_log_file, 'a') as f:
                    f.write(csv_line)
                    
        except Exception as e:
            self.logger.error(f"Error logging trade: {str(e)}")

    def get_performance_metrics(self, window: Optional[int] = None) -> Dict:
        """
        Get performance metrics, optionally over a specific window
        
        Parameters
        ----------
        window : Optional[int]
            Number of recent trades to consider
            
        Returns
        -------
        Dict
            Performance metrics including:
            - win_rate: float
            - total_pnl: float
            - drawdown: float
            - quality_score: float
        """
        if not self.trades:
            return {
                'win_rate': 0,
                'total_pnl': 0,
                'drawdown': 0,
                'quality_score': 0
            }
            
        # Get relevant trades
        trades_to_analyze = self.trades[-window:] if window else self.trades
        
        # Calculate metrics
        wins = sum(1 for t in trades_to_analyze if t['pnl'] > 0)
        total = len(trades_to_analyze)
        
        win_rate = (wins / total) if total > 0 else 0
        period_pnl = sum(t['pnl'] for t in trades_to_analyze)
        
        # Calculate quality score (composite metric)
        avg_win = sum(t['pnl'] for t in trades_to_analyze if t['pnl'] > 0) / wins if wins > 0 else 0
        avg_loss = sum(t['pnl'] for t in trades_to_analyze if t['pnl'] < 0) / (total - wins) if (total - wins) > 0 else 0
        
        # Quality score factors in win rate and risk-adjusted return
        quality_score = (win_rate * 0.4 + 
                        (avg_win / abs(avg_loss) if avg_loss != 0 else 0) * 0.3 +
                        (1 - abs(period_pnl / total) / 100) * 0.3) if total > 0 else 0
                        
        return {
            'win_rate': win_rate * 100,  # as percentage
            'total_pnl': period_pnl,
            'drawdown': self.max_drawdown,
            'quality_score': quality_score
        }
        
    def _calculate_metrics(self):
        """Update running performance metrics"""
        if not self.trades:
            return
            
        # Reset counters
        self.total_trades = len(self.trades)
        self.winning_trades = sum(1 for t in self.trades if t['pnl'] > 0)
        self.losing_trades = self.total_trades - self.winning_trades
        
        # Calculate total PnL
        self.total_pnl = sum(t['pnl'] for t in self.trades)
        
        # Calculate max drawdown
        running_pnl = 0
        peak = 0
        drawdown = 0
        
        for trade in self.trades:
            running_pnl += trade['pnl']
            peak = max(peak, running_pnl)
            drawdown = min(drawdown, running_pnl - peak)
            
        self.max_drawdown = abs(drawdown)
        
        # Log periodic performance updates
        if self.total_trades % 10 == 0:
            metrics = self.get_performance_metrics(window=10)
            log_performance(
                f"Last 10 trades: Win Rate {metrics['win_rate']:.1f}% | "
                f"PnL {metrics['total_pnl']:.2f} USDT"
            )
            
    def _load_trades(self) -> List[Dict]:
        """Load trades from log file"""
        if not self.trade_log_file.exists():
            return []
            
        try:
            with open(self.trade_log_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading trade log: {str(e)}")
            return []
            
    def _save_trades(self):
        """Save trades to log file"""
        try:
            with open(self.trade_log_file, 'w') as f:
                json.dump(self.trades, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving trade log: {str(e)}")
            
    def export_trades_csv(self, filepath: Optional[str] = None) -> None:
        """Export trades to CSV file"""
        if not filepath:
            filepath = self.log_dir / f"trades_{datetime.now().strftime('%Y%m%d')}.csv"
            
        df = pd.DataFrame(self.trades)
        df.to_csv(filepath, index=False)
        self.logger.info(f"Exported trades to {filepath}")
