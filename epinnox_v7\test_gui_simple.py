#!/usr/bin/env python3
"""
Simple GUI Testing Script for Epinnox v7 Dashboard

This script provides basic testing for the GUI components without complex imports.
Run with: python test_gui_simple.py
"""

import asyncio
import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime
import pandas as pd

# Ensure we're in the right directory
os.chdir(Path(__file__).parent)
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing Module Imports...")
    
    results = {}
    
    # Test core imports
    try:
        from utils.config_validator import ConfigValidator
        results['config_validator'] = True
        print("  ✅ ConfigValidator imported successfully")
    except Exception as e:
        results['config_validator'] = False
        print(f"  ❌ ConfigValidator import failed: {e}")
    
    try:
        from strategy_builder.backtester import Backtester
        results['backtester'] = True
        print("  ✅ Backtester imported successfully")
    except Exception as e:
        results['backtester'] = False
        print(f"  ❌ Backtester import failed: {e}")
    
    try:
        from gui.llm_sim_tab import LLMSimulationWorker
        results['llm_sim'] = True
        print("  ✅ LLMSimulationWorker imported successfully")
    except Exception as e:
        results['llm_sim'] = False
        print(f"  ❌ LLMSimulationWorker import failed: {e}")
    
    try:
        from utils.logger import get_logger
        results['logger'] = True
        print("  ✅ Logger imported successfully")
    except Exception as e:
        results['logger'] = False
        print(f"  ❌ Logger import failed: {e}")
    
    return results

async def test_basic_functionality():
    """Test basic functionality of core components"""
    print("\n🧪 Testing Basic Functionality...")
    
    results = {}
    
    # Test 1: Config Loading
    try:
        from utils.config_validator import ConfigValidator
        config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
        results['config_load'] = {
            'success': config is not None,
            'has_symbols': 'symbols' in config if config else False
        }
        print("  ✅ Configuration loaded successfully")
    except Exception as e:
        results['config_load'] = {'success': False, 'error': str(e)}
        print(f"  ❌ Configuration loading failed: {e}")
    
    # Test 2: Backtester Initialization
    try:
        from strategy_builder.backtester import Backtester
        backtester = Backtester()
        results['backtester_init'] = {'success': True}
        print("  ✅ Backtester initialized successfully")
    except Exception as e:
        results['backtester_init'] = {'success': False, 'error': str(e)}
        print(f"  ❌ Backtester initialization failed: {e}")
    
    # Test 3: Data Fetching (small amount)
    try:
        if results['backtester_init']['success']:
            print("  📊 Testing data fetching...")
            start_time = time.time()
            data = await backtester.fetch_historical_data(
                symbol='DOGE/USDT:USDT',
                timeframe='1m',
                days=0.1,  # Small amount for testing
                exchange_id='binance'
            )
            fetch_time = time.time() - start_time
            
            results['data_fetch'] = {
                'success': data is not None and len(data) > 0,
                'candles': len(data) if data is not None else 0,
                'fetch_time': fetch_time
            }
            print(f"    ✅ Fetched {len(data)} candles in {fetch_time:.2f}s")
        else:
            results['data_fetch'] = {'success': False, 'error': 'Backtester init failed'}
            print("    ⚠️  Data fetch skipped - backtester init failed")
    except Exception as e:
        results['data_fetch'] = {'success': False, 'error': str(e)}
        print(f"    ❌ Data fetching failed: {e}")
    
    # Test 4: Strategy Loading
    try:
        if results['backtester_init']['success']:
            strategy = backtester.load_strategy('strategy_builder/strategies/simple_momentum.yaml')
            results['strategy_load'] = {
                'success': strategy is not None,
                'strategy_name': strategy.get('name', 'Unknown') if strategy else None
            }
            print(f"  ✅ Strategy loaded: {strategy.get('name', 'Unknown')}")
        else:
            results['strategy_load'] = {'success': False, 'error': 'Backtester init failed'}
            print("  ⚠️  Strategy load skipped - backtester init failed")
    except Exception as e:
        results['strategy_load'] = {'success': False, 'error': str(e)}
        print(f"  ❌ Strategy loading failed: {e}")
    
    # Test 5: LLM Simulation Worker
    try:
        from gui.llm_sim_tab import LLMSimulationWorker
        worker = LLMSimulationWorker('DOGE/USDT:USDT', 30, config)
        results['llm_worker'] = {'success': True}
        print("  ✅ LLM simulation worker created successfully")
    except Exception as e:
        results['llm_worker'] = {'success': False, 'error': str(e)}
        print(f"  ❌ LLM worker creation failed: {e}")
    
    return results

def test_file_structure():
    """Test if required files and directories exist"""
    print("\n📁 Testing File Structure...")
    
    required_files = [
        'config/scalper_config.yaml',
        'strategy_builder/strategies/simple_momentum.yaml',
        'gui/dashboard.py',
        'gui/strategy_tab.py',
        'gui/parameter_sweeper.py',
        'gui/llm_sim_tab.py',
        'main.py'
    ]
    
    required_dirs = [
        'logs',
        'logs/backtest_results',
        'gui',
        'strategy_builder',
        'utils',
        'core'
    ]
    
    results = {'files': {}, 'directories': {}}
    
    # Check files
    for file_path in required_files:
        path = Path(file_path)
        exists = path.exists()
        results['files'][file_path] = exists
        status = "✅" if exists else "❌"
        print(f"  {status} {file_path}")
    
    # Check directories
    for dir_path in required_dirs:
        path = Path(dir_path)
        exists = path.exists() and path.is_dir()
        results['directories'][dir_path] = exists
        status = "✅" if exists else "❌"
        print(f"  {status} {dir_path}/")
    
    return results

async def test_gui_launch():
    """Test if GUI can be launched (without actually showing it)"""
    print("\n🖥️  Testing GUI Launch Capability...")
    
    try:
        # Test PyQt5 import
        from PyQt5.QtWidgets import QApplication
        print("  ✅ PyQt5 imported successfully")
        
        # Test GUI module imports
        from gui.dashboard import EpinnoxDashboard
        print("  ✅ Dashboard class imported successfully")
        
        # Test if we can create QApplication (but don't show GUI)
        import sys
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("  ✅ QApplication created successfully")
        
        return {'success': True, 'gui_ready': True}
        
    except Exception as e:
        print(f"  ❌ GUI launch test failed: {e}")
        return {'success': False, 'error': str(e)}

def generate_simple_report(import_results, functionality_results, file_results, gui_results):
    """Generate a simple test report"""
    print("\n" + "=" * 50)
    print("📋 SIMPLE TEST REPORT")
    print("=" * 50)
    
    # Count successes
    import_success = sum(1 for v in import_results.values() if v)
    import_total = len(import_results)
    
    func_success = sum(1 for v in functionality_results.values() if v.get('success', False))
    func_total = len(functionality_results)
    
    file_success = sum(1 for v in file_results['files'].values() if v)
    file_total = len(file_results['files'])
    
    dir_success = sum(1 for v in file_results['directories'].values() if v)
    dir_total = len(file_results['directories'])
    
    gui_success = 1 if gui_results.get('success', False) else 0
    
    total_success = import_success + func_success + file_success + dir_success + gui_success
    total_tests = import_total + func_total + file_total + dir_total + 1
    
    print(f"📊 Overall: {total_success}/{total_tests} tests passed ({total_success/total_tests*100:.1f}%)")
    print()
    print(f"🔍 Imports: {import_success}/{import_total} passed")
    print(f"🧪 Functionality: {func_success}/{func_total} passed")
    print(f"📁 Files: {file_success}/{file_total} found")
    print(f"📂 Directories: {dir_success}/{dir_total} found")
    print(f"🖥️  GUI: {gui_success}/1 ready")
    print()
    
    # Recommendations
    if total_success == total_tests:
        print("🎉 All tests passed! System is ready for advanced testing.")
        print("✅ Recommended next step: Run comprehensive GUI testing")
    else:
        print("⚠️  Some tests failed. Please fix issues before proceeding:")
        
        if import_success < import_total:
            print("  - Fix import issues (check Python path and dependencies)")
        if func_success < func_total:
            print("  - Fix functionality issues (check configuration and network)")
        if file_success < file_total:
            print("  - Ensure all required files are present")
        if dir_success < dir_total:
            print("  - Create missing directories")
        if not gui_results.get('success', False):
            print("  - Fix GUI dependencies (install PyQt5)")
    
    # Save report
    report_data = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_tests': total_tests,
            'passed_tests': total_success,
            'success_rate': total_success/total_tests*100
        },
        'details': {
            'imports': import_results,
            'functionality': functionality_results,
            'files': file_results,
            'gui': gui_results
        }
    }
    
    # Create logs directory if it doesn't exist
    Path('logs').mkdir(exist_ok=True)
    
    report_file = Path('logs/simple_test_report.json')
    with open(report_file, 'w') as f:
        json.dump(report_data, f, indent=2, default=str)
    
    print(f"📄 Report saved to: {report_file}")

async def main():
    """Run simple GUI testing"""
    print("🚀 Epinnox v7 - Simple GUI Testing")
    print("=" * 40)
    
    # Run tests
    import_results = test_imports()
    functionality_results = await test_basic_functionality()
    file_results = test_file_structure()
    gui_results = await test_gui_launch()
    
    # Generate report
    generate_simple_report(import_results, functionality_results, file_results, gui_results)

if __name__ == "__main__":
    asyncio.run(main())
